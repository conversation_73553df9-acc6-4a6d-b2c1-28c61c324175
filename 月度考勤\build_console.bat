@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
echo 🎯 sync.py 控制台版本打包工具
echo ================================================

echo 📦 安装依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo 📦 安装PyInstaller...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo ❌ PyInstaller安装失败
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成

echo 🔍 查找Excel文件...
set EXCEL_FILES=
for %%f in (*.xlsx *.xls) do (
    echo 📄 找到Excel文件: %%f
    set EXCEL_FILES=!EXCEL_FILES! --add-data="%%f;."
)

echo 🔨 开始打包控制台版本...
pyinstaller --onefile --console --name="月度考勤同步工具_控制台版" !EXCEL_FILES! --hidden-import=pandas --hidden-import=openpyxl --hidden-import=requests --hidden-import=tkinter --hidden-import=tkinter.messagebox --hidden-import=datetime --hidden-import=time --hidden-import=sys --hidden-import=io --hidden-import=os --clean sync.py

if %errorlevel% neq 0 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo 📁 复制输出文件...
if exist "dist\*.exe" (
    copy "dist\*.exe" .
    echo ✅ 文件复制完成
) else (
    echo ❌ 没有找到生成的exe文件
)

echo 🧹 清理临时文件...
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.spec" del "*.spec"

echo 🎉 打包完成！
echo 📁 生成的文件:
dir *.exe

echo.
echo 💡 使用说明:
echo    - 生成的exe文件可以在没有Python环境的Windows机器上运行
echo    - 控制台版本会显示详细的运行日志，便于排查问题
echo    - Excel文件已打包到exe中，无需单独携带
echo.

pause
