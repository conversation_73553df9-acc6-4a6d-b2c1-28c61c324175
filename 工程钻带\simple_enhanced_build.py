#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import datetime
import subprocess
import shutil
import locale

def add_expiry_check(content, expiry_date="2026-01-01"):
    """添加过期检查代码"""
    expiry_code = f'''
import datetime
import sys

def check_expiry():
    """检查软件是否过期"""
    try:
        expiry_date = datetime.datetime.strptime("{expiry_date}", "%Y-%m-%d")
        current_date = datetime.datetime.now()
        
        if current_date > expiry_date:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()
            
            messagebox.showerror(
                "软件无法运行", 
                f"ERROR。\\n\\n"
                "请联系开发者获取帮助。\\n\\n"
                "技术支持: JSYPCB团队"
            )
            
            root.destroy()
            sys.exit(1)
    except Exception:
        # 如果检查失败，静默退出
        sys.exit(1)

# 在程序开始时检查过期
check_expiry()

'''
    
    # 在import语句后插入过期检查代码
    lines = content.split('\n')
    insert_pos = 0
    
    # 找到合适的插入位置（在import语句之后）
    for i, line in enumerate(lines):
        if line.strip() and not line.strip().startswith('#') and not line.strip().startswith('import') and not line.strip().startswith('from'):
            insert_pos = i
            break
    
    lines.insert(insert_pos, expiry_code)
    return '\n'.join(lines)

def create_protected_script(source_file, target_file, expiry_date="2026-01-01"):
    """创建受保护的脚本"""
    try:
        print(f"读取源文件: {source_file}")
        with open(source_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        print("添加过期检查...")
        protected_content = add_expiry_check(original_content, expiry_date)
        
        print(f"写入保护文件: {target_file}")
        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(protected_content)
        
        print("✅ 代码保护完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建保护脚本失败: {e}")
        return False

def build_with_pyinstaller():
    """使用PyInstaller构建受保护的可执行文件"""
    print("=" * 60)
    print("JSYPCB钻带转Excel工具 - 增强版构建脚本")
    print("=" * 60)
    print()
    
    # 设置控制台编码
    try:
        if sys.platform == 'win32':
            os.system('chcp 65001 > nul')
        print("已设置控制台编码为UTF-8")
    except:
        pass
    
    # 获取当前日期和版本信息
    current_date = datetime.datetime.now()
    version = "1.4"
    build_date = current_date.strftime("%Y%m%d")
    
    # 构建输出目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = os.path.join(current_dir, 'dist_enhanced')
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    
    print(f"当前工作目录: {current_dir}")
    print(f"输出目录: {output_path}")
    
    # 确保源文件存在
    source_file = os.path.join(current_dir, 'transfer.py')
    if not os.path.exists(source_file):
        print(f"❌ 错误: 找不到源文件: {source_file}")
        sys.exit(1)
    
    print("\n步骤1: 创建受保护的脚本...")
    
    # 创建受保护的脚本
    protected_file = os.path.join(output_path, 'transfer_protected.py')
    if not create_protected_script(source_file, protected_file):
        print("⚠️  创建保护脚本失败，使用原始文件...")
        protected_file = source_file
    
    print("\n步骤2: 检查PyInstaller...")
    
    # 检查并安装PyInstaller
    try:
        subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                      check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("✅ PyInstaller已安装")
    except:
        print("正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller', 
                           '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple'], check=True)
            print("✅ PyInstaller安装成功")
        except Exception as e:
            print(f"❌ PyInstaller安装失败: {e}")
            sys.exit(1)
    
    print("\n步骤3: 使用PyInstaller构建可执行文件...")

    # 查找图标文件
    icon_path = None
    for icon_name in ['converted_icon.ico', 'icon.ico']:
        icon_file = os.path.join(current_dir, icon_name)
        if os.path.exists(icon_file):
            icon_path = icon_file
            print(f"找到图标文件: {icon_path}")

            # 验证图标文件
            try:
                file_size = os.path.getsize(icon_file)
                print(f"图标文件大小: {file_size} 字节")
                if file_size > 0:
                    print("✅ 图标文件有效")
                else:
                    print("⚠️ 图标文件为空")
                    icon_path = None
            except Exception as e:
                print(f"⚠️ 图标文件检查失败: {e}")
                icon_path = None
            break

    if not icon_path:
        print("⚠️ 未找到有效的图标文件，将使用默认图标")

    # PyInstaller命令
    final_dir = os.path.join(output_path, "final")
    if not os.path.exists(final_dir):
        os.makedirs(final_dir)

    pyinstaller_cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--name=钻带转Excel工具',
        '--onefile',
        '--windowed',
        '--noconsole',
        f'--distpath={final_dir}',
        '--clean',
        '--optimize=2',  # 优化字节码
        '--noupx',  # 禁用UPX压缩，避免图标问题
    ]

    # 添加图标配置
    if icon_path:
        # 确保图标路径使用绝对路径
        abs_icon_path = os.path.abspath(icon_path)
        pyinstaller_cmd.extend(['--icon', abs_icon_path])
        print(f"使用图标: {abs_icon_path}")

        # 同时将图标作为数据文件添加，确保程序运行时可以访问
        pyinstaller_cmd.extend(['--add-data', f'{abs_icon_path};.'])
        print(f"添加图标作为资源文件")

    # 添加版本信息（Windows特有）
    if sys.platform == 'win32':
        version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
# filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
# Set not needed items to zero 0.
filevers=(1,4,0,0),
prodvers=(1,4,0,0),
# Contains a bitmask that specifies the valid bits 'flags'r
mask=0x3f,
# Contains a bitmask that specifies the Boolean attributes of the file.
flags=0x0,
# The operating system for which this file was designed.
# 0x4 - NT and there is no need to change it.
OS=0x4,
# The general type of file.
# 0x1 - the file is an application.
fileType=0x1,
# The function of the file.
# 0x0 - the function is not defined for this fileType
subtype=0x0,
# Creation date and time stamp.
date=(0, 0)
),
kids=[
StringFileInfo(
[
StringTable(
u'080404B0',
[StringStruct(u'CompanyName', u'JSYPCB团队'),
StringStruct(u'FileDescription', u'钻带转Excel工具'),
StringStruct(u'FileVersion', u'*******'),
StringStruct(u'InternalName', u'钻带转Excel工具'),
StringStruct(u'LegalCopyright', u'Copyright © 2024 JSYPCB团队'),
StringStruct(u'OriginalFilename', u'钻带转Excel工具.exe'),
StringStruct(u'ProductName', u'钻带转Excel工具'),
StringStruct(u'ProductVersion', u'*******')])
]),
VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
]
)'''
        version_file = os.path.join(output_path, 'version_info.txt')
        try:
            with open(version_file, 'w', encoding='utf-8') as f:
                f.write(version_info)
            pyinstaller_cmd.extend(['--version-file', version_file])
            print("添加版本信息文件")
        except Exception as e:
            print(f"⚠️ 创建版本信息文件失败: {e}")

    # 添加源文件
    pyinstaller_cmd.append(protected_file)
    
    try:
        print(f"执行PyInstaller命令...")
        
        process = subprocess.run(pyinstaller_cmd, check=True, cwd=current_dir)
        print("✅ PyInstaller构建成功")
        
        # 检查生成的文件
        exe_file = os.path.join(final_dir, '钻带转Excel工具.exe')
        if os.path.exists(exe_file):
            print(f"✅ 可执行文件已生成: {exe_file}")

            # 验证图标是否正确嵌入
            if icon_path:
                print("\n🔍 验证图标嵌入...")
                try:
                    # 检查exe文件是否包含图标资源
                    file_size = os.path.getsize(exe_file)
                    icon_size = os.path.getsize(icon_path)

                    print(f"  可执行文件大小: {file_size / 1024 / 1024:.2f} MB")
                    print(f"  图标文件大小: {icon_size} 字节")

                    # 简单验证：检查文件大小是否合理增加
                    if file_size > icon_size:
                        print("  ✅ 图标可能已正确嵌入")
                    else:
                        print("  ⚠️ 图标嵌入状态未知")

                except Exception as e:
                    print(f"  ⚠️ 图标验证失败: {e}")

            print("\n🎉 增强版构建完成! 🎉")
            print(f"版本: {version} - 构建日期: {build_date}")
            print(f"可执行文件位置: {exe_file}")
            print(f"文件大小: {os.path.getsize(exe_file) / 1024 / 1024:.2f} MB")

            print("\n🔒 代码保护特性:")
            print("  ✅ 过期时间检查 (2026-01-01)")
            print("  ✅ 字节码优化")
            print("  ✅ 单文件打包")
            print("  ✅ 无控制台窗口")

            if icon_path:
                print("\n🎨 图标配置:")
                print("  ✅ 任务栏图标嵌入")
                print("  ✅ 文件资源管理器图标")
                print("  ✅ 程序窗口图标资源")
                print(f"  📁 图标文件: {os.path.basename(icon_path)}")

            print("\n📋 使用说明:")
            print("  1. 双击运行 '钻带转Excel工具.exe'")
            print("  2. 程序窗口应显示正确的图标")
            print("  3. 任务栏中应显示自定义图标")
            print("  4. 文件属性中应显示版本信息")

        else:
            print("❌ 未找到生成的可执行文件")
            # 列出输出目录中的所有文件
            print("输出目录内容:")
            try:
                for file in os.listdir(final_dir):
                    print(f"  {file}")
            except:
                print("  (目录为空或无法访问)")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller构建失败: {e}")
        sys.exit(1)
    
    # 清理临时文件
    print("\n🧹 清理临时文件...")
    try:
        # 清理受保护的脚本文件
        if protected_file != source_file and os.path.exists(protected_file):
            os.remove(protected_file)
            print("  ✅ 清理受保护脚本文件")

        # 清理版本信息文件
        version_file = os.path.join(output_path, 'version_info.txt')
        if os.path.exists(version_file):
            os.remove(version_file)
            print("  ✅ 清理版本信息文件")

        # 清理PyInstaller生成的临时文件
        build_dir = os.path.join(current_dir, 'build')
        if os.path.exists(build_dir):
            shutil.rmtree(build_dir)
            print("  ✅ 清理构建临时目录")

        # 清理spec文件
        spec_file = os.path.join(current_dir, '钻带转Excel工具.spec')
        if os.path.exists(spec_file):
            os.remove(spec_file)
            print("  ✅ 清理spec文件")

        print("✅ 临时文件清理完成")

    except Exception as e:
        print(f"⚠️ 清理临时文件时出现警告: {e}")

    print("\n" + "=" * 60)
    print("构建过程完成！")
    print("=" * 60)

if __name__ == "__main__":
    build_with_pyinstaller()
