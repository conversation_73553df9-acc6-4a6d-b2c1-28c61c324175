#第一个字符必须字母或者下划线
#其他字符可以是字母、数字或下划线
#对大小写敏感
#长度无限制
#保留关键字

age=50
user_name='张三'
_total=100
MAX_SIZE=100


姓名=30

def is_prime(num):
    try:
        exec(f"{num}=None")
        return True
    except:
        return False
print(is_prime('2a')) 
print(is_prime('a2e')) 


print("hello world")


###11
'''
1111
'''

# 1122
#""" 
# """ 111
# 1/
# 
#  """ """

if True:
    print("hello")
else:
    print("world")
print(1)



item_one=1
item_two=2
item_three=3

_total=item_one+\
        item_two+\
        item_three
total=['item_one',item_two,item_three]
print(total)


str='123456789'
print(str)
print(str[0:-1:2])
print(str[1:5:2]) #246
print(str * 2) 
print(str + '你好')   
print('hello\nrunoob')  
print(r'hello\nrunoob')


input("\n\n按下 enter 键退出")

import sys;x='abc';sys.stdout.write(x+'\n')


x='a'
y='b'
print(x)
print(y)

print('-------------')
print(x,end=",")
print(y,end=".")
print()


import sys
print('python版本：',sys.version)
print('命令行参数')
for i in sys.argv:
    print(i)
print('\n 路径为：',sys.path)


counter=100
miles=1000.0
name="runoob"

print(counter)
print(miles)
print(name)

x=10
y=3.14
name='runoob'
is_active=True

a,b,c=1,2,'three'

print(type(x))

print(type(y))
print(type(name))
print(type(is_active))
print(b)










