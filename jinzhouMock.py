from flask import Flask, request, jsonify
import re

app = Flask(__name__)
drill_mac_codes_storage = []
DRILL_CODE_PATTERN = r'^drill\d{2}$'
@app.route('/upload', methods=['POST'])
def upload_data():

    data = request.get_json()
    print("接收到的数据:", data)
    return jsonify({
        "code": 200,
        "message": "succsess",
    })
@app.route('/api/getDrills', methods=['POST'])
def get_drill_info():
    drill_info_map = {
        "drill01": {"macCode": "drill01", "macName": "钻机 1"},
        "drill02": {"macCode": "drill02", "macName": "钻机 2"},
    }
    return jsonify({
        "code": 200,
        "message": "查询成功",
        "data": [drill_info_map] 
    })
if __name__ == '__main__':
    app.run(debug=True)