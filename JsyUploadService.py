import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import time
import logging
import os
import traceback
from datetime import datetime

# 获取脚本所在目录的绝对路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
os.chdir(SCRIPT_DIR)  # 切换到脚本所在目录

# 配置日志
log_dir = os.path.join(SCRIPT_DIR, 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'service_{datetime.now().strftime("%Y%m%d")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class JsyUploadService(win32serviceutil.ServiceFramework):
    _svc_name_ = "JsyUploadService"
    _svc_display_name_ = "JsyUpload"
    _svc_description_ = "This is the first background service for PCB data upload"

    def __init__(self, args):
        try:
            win32serviceutil.ServiceFramework.__init__(self, args)
            self.stop_event = win32event.CreateEvent(None, 0, 0, None)
            socket.setdefaulttimeout(60)
            self.is_alive = True
            
            # 确保在正确的目录
            os.chdir(SCRIPT_DIR)
            
            # 导入主模块
            sys.path.insert(0, SCRIPT_DIR)
            import JsyUpload
            self.jsy_upload = JsyUpload
            
            logging.info(f"Service initialized successfully in directory: {SCRIPT_DIR}")
        except Exception as e:
            logging.error(f"Service initialization error: {str(e)}")
            logging.error(traceback.format_exc())
            raise

    def SvcStop(self):
        try:
            self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
            win32event.SetEvent(self.stop_event)
            self.is_alive = False
            logging.info("Service is stopping")
        except Exception as e:
            logging.error(f"Service stop error: {str(e)}")
            logging.error(traceback.format_exc())

    def SvcDoRun(self):
        try:
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )
            logging.info("Service started successfully")
            self.main()
        except Exception as e:
            logging.error(f"Service run error: {str(e)}")
            logging.error(traceback.format_exc())
            raise

    def main(self):
        try:
            while self.is_alive:
                try:
                    logging.info("Starting data upload check")
                    self.jsy_upload.check_and_upload_new_data()
                    logging.info("Data upload check completed")
                except Exception as e:
                    logging.error(f"Data upload error: {str(e)}")
                    logging.error(traceback.format_exc())
                time.sleep(60)
        except Exception as e:
            logging.error(f"Main loop error: {str(e)}")
            logging.error(traceback.format_exc())
            raise

if __name__ == '__main__':
    try:
        if len(sys.argv) == 1:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(JsyUploadService)
            servicemanager.StartServiceCtrlDispatcher()
        else:
            win32serviceutil.HandleCommandLine(JsyUploadService)
    except Exception as e:
        logging.error(f"Service startup error: {str(e)}")
        logging.error(traceback.format_exc())
        raise 