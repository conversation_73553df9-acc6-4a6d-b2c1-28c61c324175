# sync.py 打包说明

## 📦 打包方法

### 方法1: 使用批处理文件（推荐）
```bash
# 双击运行或在命令行执行
build.bat
```

### 方法2: 使用Python脚本
```bash
python build.py
```

### 方法3: 使用PyInstaller配置文件
```bash
pyinstaller sync.spec
```

### 方法4: 手动命令
```bash
# 安装依赖
pip install -r requirements.txt

# 打包（GUI版本）
pyinstaller --onefile --windowed --name="月度考勤同步工具" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=requests --hidden-import=tkinter --clean sync.py

# 打包（调试版本）
pyinstaller --onefile --name="月度考勤同步工具_调试版" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=requests --hidden-import=tkinter --clean sync.py
```

## 🎯 输出文件

打包完成后会生成以下文件：
- `月度考勤同步工具.exe` - 正式版本（无控制台窗口）
- `月度考勤同步工具_调试版.exe` - 调试版本（有控制台窗口）

## 💡 使用说明

### 正式版本
- 适合最终用户使用
- 无控制台窗口，界面简洁
- 如果出现错误，错误信息会通过GUI弹窗显示

### 调试版本
- 适合开发和调试使用
- 有控制台窗口，可以看到详细的运行信息和错误信息
- 便于排查问题

## 🔧 依赖包

程序依赖以下Python包：
- `requests` - HTTP请求库
- `pandas` - 数据处理库
- `openpyxl` - Excel文件操作库
- `tkinter` - GUI界面库（Python内置）

## 📋 系统要求

### 开发环境（打包时需要）
- Python 3.7+
- Windows 10/11
- 网络连接（用于安装依赖包）

### 运行环境（最终用户）
- Windows 10/11 (64位)
- 无需安装Python
- 无需安装任何依赖包

## 🚀 部署步骤

1. **准备打包环境**
   ```bash
   # 确保Python已安装
   python --version
   
   # 安装PyInstaller
   pip install pyinstaller
   ```

2. **执行打包**
   ```bash
   # 运行批处理文件
   build.bat
   ```

3. **测试可执行文件**
   ```bash
   # 运行生成的exe文件
   月度考勤同步工具.exe
   ```

4. **分发给用户**
   - 将生成的`.exe`文件复制到目标机器
   - 确保目标机器有网络连接（程序需要访问API）
   - 双击运行即可

## ⚠️ 注意事项

1. **文件大小**: 打包后的exe文件可能比较大（50-100MB），这是正常的，因为包含了Python运行时和所有依赖库。

2. **首次运行**: 第一次运行可能需要几秒钟启动时间，这是正常的。

3. **防病毒软件**: 某些防病毒软件可能会误报，需要添加到白名单。

4. **网络访问**: 程序需要访问 `api.hikiot.com`，确保网络连接正常。

5. **Excel文件**: 如果程序需要读取特定的Excel文件，确保这些文件与exe文件在同一目录下。

## 🐛 故障排除

### 打包失败
- 检查Python版本是否支持
- 检查网络连接
- 尝试手动安装依赖包：`pip install -r requirements.txt`

### 运行失败
- 使用调试版本查看错误信息
- 检查网络连接
- 检查是否有必要的Excel文件

### 性能问题
- 第一次启动较慢是正常的
- 如果持续缓慢，可以尝试重新打包

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 操作系统版本
2. Python版本（如果在开发环境）
3. 错误信息截图
4. 使用的打包方法
