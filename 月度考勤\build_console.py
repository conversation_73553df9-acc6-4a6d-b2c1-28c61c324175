#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
sync.py 控制台版本打包脚本
确保有控制台日志输出，并正确打包Excel文件
"""

import os
import sys
import subprocess
import shutil
import glob
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def find_excel_files():
    """查找Excel文件"""
    xlsx_files = glob.glob("*.xlsx")
    xls_files = glob.glob("*.xls")
    all_excel_files = xlsx_files + xls_files
    
    if all_excel_files:
        print("📄 找到Excel文件:")
        for file in all_excel_files:
            print(f"   - {file}")
    else:
        print("ℹ️ 没有找到Excel文件")
    
    return all_excel_files

def build_console_version():
    """打包带控制台的版本"""
    print("🔨 开始打包控制台版本...")
    
    # 查找Excel文件
    excel_files = find_excel_files()
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--console",                    # 保留控制台窗口（重要！）
        "--name=月度考勤同步工具_控制台版",  # 可执行文件名称
        "--hidden-import=pandas",       # 确保pandas被包含
        "--hidden-import=openpyxl",     # 确保openpyxl被包含
        "--hidden-import=requests",     # 确保requests被包含
        "--hidden-import=tkinter",      # 确保tkinter被包含
        "--hidden-import=tkinter.messagebox",  # 确保messagebox被包含
        "--hidden-import=datetime",     # 确保datetime被包含
        "--hidden-import=time",         # 确保time被包含
        "--hidden-import=sys",          # 确保sys被包含
        "--hidden-import=io",           # 确保io被包含
        "--hidden-import=os",           # 确保os被包含
        "--clean",                      # 清理临时文件
    ]
    
    # 添加Excel文件
    for excel_file in excel_files:
        cmd.append(f"--add-data={excel_file};.")
        print(f"📄 添加Excel文件: {excel_file}")
    
    # 添加主程序文件
    cmd.append("sync.py")
    
    try:
        print(f"🔨 执行命令:")
        print(f"   {' '.join(cmd)}")
        print()
        
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ 控制台版本打包完成")
            return True
        else:
            print(f"❌ 控制台版本打包失败，返回码: {result.returncode}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 控制台版本打包失败: {e}")
        return False

def build_gui_version():
    """打包GUI版本（无控制台）"""
    print("🔨 开始打包GUI版本...")
    
    # 查找Excel文件
    excel_files = find_excel_files()
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 隐藏控制台窗口
        "--name=月度考勤同步工具_GUI版",   # 可执行文件名称
        "--hidden-import=pandas",       
        "--hidden-import=openpyxl",     
        "--hidden-import=requests",     
        "--hidden-import=tkinter",      
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=datetime",     
        "--hidden-import=time",         
        "--hidden-import=sys",          
        "--hidden-import=io",           
        "--hidden-import=os",           
        "--clean",                      
    ]
    
    # 添加Excel文件
    for excel_file in excel_files:
        cmd.append(f"--add-data={excel_file};.")
    
    # 添加主程序文件
    cmd.append("sync.py")
    
    try:
        print(f"🔨 执行命令:")
        print(f"   {' '.join(cmd)}")
        print()
        
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ GUI版本打包完成")
            return True
        else:
            print(f"❌ GUI版本打包失败，返回码: {result.returncode}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ GUI版本打包失败: {e}")
        return False

def copy_output():
    """复制输出文件到当前目录"""
    dist_dir = Path("dist")
    if dist_dir.exists():
        print("📁 复制输出文件...")
        for file in dist_dir.glob("*.exe"):
            shutil.copy2(file, ".")
            size = file.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ 已复制: {file.name} ({size:.1f} MB)")

def cleanup():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["*.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已删除目录: {dir_name}")
    
    import glob
    for pattern in files_to_remove:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"✅ 已删除文件: {file}")

def main():
    """主函数"""
    print("🎯 sync.py 控制台版本打包工具")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("sync.py"):
        print("❌ 找不到sync.py文件，请确保在正确的目录下运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装完成")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 打包控制台版本（推荐）
    print("\n🔨 打包控制台版本（推荐，有日志输出）...")
    console_success = build_console_version()
    
    # 打包GUI版本
    print("\n🔨 打包GUI版本（无控制台窗口）...")
    gui_success = build_gui_version()
    
    if console_success or gui_success:
        copy_output()
        cleanup()
        
        print("\n🎉 打包完成！")
        print("📁 输出文件:")
        for file in Path(".").glob("*.exe"):
            size = file.stat().st_size / (1024 * 1024)  # MB
            print(f"   {file.name} ({size:.1f} MB)")
        
        print("\n💡 使用说明:")
        print("   - 控制台版本: 有控制台窗口，可以看到详细的运行日志")
        print("   - GUI版本: 无控制台窗口，界面简洁")
        print("   - 推荐使用控制台版本，便于查看运行状态和错误信息")
        print("   - Excel文件已打包到exe中，无需单独携带")
        
        return True
    else:
        print("❌ 打包失败")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
