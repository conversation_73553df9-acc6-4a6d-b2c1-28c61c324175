import pyodbc
import requests
import time
import json
import logging
from datetime import datetime

# 配置日志
# 创建 logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# 文件 handler
file_handler = logging.FileHandler('D:\\logs\\jsy_upload_service.log', encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# 控制台 handler（可选）
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

def check_and_upload_new_data():
    try:
        # 数据库连接
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=10.5.2.10,1433;"
            "DATABASE=PCB_ERP_JSY_PRDO;"
            "UID=sa;"
            "PWD=dR5uY7rC"
        )
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        # 完整的SQL查询
        full_sql = """
        WITH DiameterMap AS (
            SELECT 
                CAST(LowerBound AS DECIMAL(10,3)) AS LowerBound,
                CAST(UpperBound AS DECIMAL(10,3)) AS UpperBound,
                CAST(Value AS DECIMAL(10,3)) AS MappedThickness
            FROM (
                VALUES
                    (0.000, 0.200, 0.200),
                    (0.200, 0.225, 0.200),
                    (0.225, 0.250, 0.225),
                    (0.250, 0.275, 0.250),
                    (0.275, 0.300, 0.275),
                    (0.300, 0.350, 0.300),
                    (0.350, 0.400, 0.350),
                    (0.400, 0.450, 0.400),
                    (0.450, 0.500, 0.450),
                    (0.500, 0.550, 0.500),
                    (0.550, 0.600, 0.550),
                    (0.600, 0.650, 0.600),
                    (0.650, 0.700, 0.650),
                    (0.700, 999.000, 0.700)
            ) t(LowerBound, UpperBound, Value)
        ),
        BoardMap AS (
            SELECT 
                CAST(LowerBound AS DECIMAL(10,3)) AS LowerBound,
                CAST(UpperBound AS DECIMAL(10,3)) AS UpperBound,
                CAST(MappedValue AS DECIMAL(10,3)) AS Board
            FROM (
                VALUES
                    (0.000, 0.300, 0.3),
                    (0.300, 0.400, 0.4),
                    (0.400, 0.500, 0.5),
                    (0.500, 0.600, 0.6),
                    (0.600, 0.700, 0.7),
                    (0.700, 0.800, 0.8),
                    (0.800, 0.900, 0.9),
                    (0.900, 1.000, 1.0),
                    (1.000, 1.100, 1.1),
                    (1.100, 1.200, 1.2),
                    (1.200, 1.300, 1.3),
                    (1.300, 1.400, 1.4),
                    (1.400, 1.500, 1.5),
                    (1.500, 1.600, 1.6),
                    (1.600, 1.700, 1.7),
                    (1.700, 1.800, 1.8),
                    (1.800, 1.900, 1.9),
                    (1.900, 2.000, 2.0),
                    (2.000, 2.100, 2.1),
                    (2.100, 2.200, 2.2),
                    (2.200, 2.300, 2.3),
                    (2.300, 2.400, 2.4),
                    (2.400, 2.500, 2.5)
            ) t(LowerBound, UpperBound, MappedValue)
        ),
        MainQuery AS (
            SELECT
                a.WipId,
                CASE 
                    WHEN c.LayerNumber = 2 THEN TRY_CAST(k.Thicknessvalue AS DECIMAL(18,6))
                    ELSE TRY_CAST(j.ParamValue AS DECIMAL(18,6))
                END AS ParamOrThickness,
                m.MinDrill
            FROM s_pc.wip a
            LEFT JOIN s_mk.ProductNumber c ON a.ProductNumberId = c.ProductNumberId
            LEFT JOIN s_pe.MakeProductPart n ON n.ProductPartId = a.ProductPartId 
            LEFT JOIN s_pe.MakeDrillRouterBit m ON m.ProductPartId = n.ProductPartId
            LEFT JOIN s_mk.ProductNumberExt k ON k.ProductNumberId = c.ProductNumberId
            OUTER APPLY (
                SELECT TOP 1 OutSubProcId, ProductNumberId
                FROM s_pc.BoardPass
                WHERE WipId = a.WipId
                  AND OutSubProcId IN (
                    '131E745FBF8B49B696F4A0756E43D030',
                    '07A45C13E7914EB7B8F049F75514CBFD',
                    'A7182F64D14C4D438EAA74B1B31A5D63',
                    '92BAF05D0F8448B18DA02FB6FA328851'
                )
            ) fr     
            OUTER APPLY (
                SELECT TOP 1 ParamValue
                FROM s_pe.MakeProcedureParam j
                WHERE j.SubProcedureId = fr.OutSubProcId
                  AND j.ProductNumberId = fr.ProductNumberId
                  AND j.EngineersParamId IN (
                        '110631D87E6C43D993B4196A67363875',
                        '4602DC239770479DA044E0F88718D92B',
                        'A09651DCD2D347EAA86E2C4FB3316A46',
                        'B012B99C03D34258839EEECF090A754E'
                      )
            ) j
            WHERE  m.TabTitle='DRL' 
        ),
        InputValues AS (
		   SELECT
			mq.WipId,
			tm1.MappedThickness,
			tm2.Board 
		FROM MainQuery mq
        LEFT JOIN DiameterMap tm1 
            ON mq.MinDrill >= tm1.LowerBound AND mq.MinDrill < tm1.UpperBound
        LEFT JOIN BoardMap tm2 
            ON ROUND(COALESCE(mq.ParamOrThickness, 0.0), 3) > tm2.LowerBound 
            AND ROUND(COALESCE(mq.ParamOrThickness, 0.0), 3) <= tm2.UpperBound
        ),
        DrillSpecResult AS (
            SELECT
                iv.WipId,
                ds.MinDrillDiameter
            FROM InputValues iv
            OUTER APPLY (
                SELECT TOP 1 ds.MinDrillDiameter
                FROM DrillSpec ds
                WHERE ds.PlateThickness = iv.MappedThickness
                  AND ds.DiameterRange = iv.Board
                ORDER BY ds.MinDrillDiameter DESC
            ) ds
        ),
        MainData AS (
            SELECT 
                l.ID,
                b.SubProcedureName,
                d.WorkOrderLotCode as lotNo,
                (ISNULL(e.PnlACount, 0) + ISNULL(e.PnlBCount, 0) + ISNULL(e.PnlCCount, 0)) as totalNum,
                CONCAT(c.ProductNumber, c.Ver) as modelNo,
                a.PnlCount as singleNum,
                c.ProductNumberId,
                b.SubProcedureId,
                l.TData 
            FROM dbo.JSYPCB_Sync_Logs l
            INNER JOIN s_pc.wip a ON l.ID = a.WipId
            LEFT JOIN s_pe.BaseSubProcedure as b ON b.SubProcedureId = a.CurSubProcId
            LEFT JOIN s_mk.ProductNumber c ON a.ProductNumberId = c.ProductNumberId
            LEFT JOIN s_pc.WorkOrderLot d ON d.WorkOrderId = a.WorkOrderId
            LEFT JOIN s_pc.WorkOrder e ON e.WorkOrderId = a.WorkOrderId
            WHERE l.SyncStatus = 0
            AND l.lotNo = d.WorkOrderLotCode
        )
        SELECT 
            m.ID,
            m.SubProcedureName,
            concat('JSY',m.lotNo ) as lotNo,
            m.totalNum,
            m.modelNo,
            m.singleNum,
            m.ProductNumberId,
            m.SubProcedureId,
            m.TData,
            STUFF((
                SELECT DISTINCT ',' + CONCAT(
                    '{',
                        '"toolNo":', QUOTENAME(ISNULL(b.Cutter, ''), '"'), ',',
                        '"pgmDiameter":', QUOTENAME(ISNULL(b.DrillDiaTxt, '0'), '"'), ',',
                        '"drillNum":', 
                                        QUOTENAME(
                                            CASE 
                                                WHEN b.HolesPcsCount = '/' THEN '0'
                                                ELSE ISNULL(b.HolesPcsCount, '0')
                                            END, 
                                            '"'
                                        ), 
                                    ',',
                        '"isGat":', ISNULL(CASE 
                                WHEN TRY_CAST(ISNULL(b.DrillDiaTxt, '0') AS DECIMAL(10,3)) < 0.5 THEN '1'
                                ELSE '0'
                        END, '"N"'), ',',
                        '"moCountMin":', QUOTENAME('0', '"'), ',',
                        '"moCountMax":', QUOTENAME(
                            CASE 
                                WHEN b.GrindTimes IS NOT NULL AND b.GrindTimes != '' 
                                THEN SUBSTRING(ISNULL(b.GrindTimes, ''), 2, LEN(ISNULL(b.GrindTimes, '')))
                                ELSE '0'
                            END, 
                            '"'
                        ),
                    '}'
                )
                FROM s_pe.MakeDrillHole b 
                LEFT JOIN s_pe.MakeDrillRouterBit c on c.DrillRouterBitId=b.DrillRouterBitId
                WHERE b.ProductNumberId = m.ProductNumberId
                AND c.SubProcedureId=m.SubProcedureId
                AND b.Cutter IS NOT NULL
                AND b.Cutter != ''
                GROUP BY 
                    b.Cutter, 
                    b.DrillDiaTxt,
                    b.HolesPcsCount,
                    b.GrindTimes
                FOR XML PATH('')
            ), 1, 1, '') as details,
            r.MinDrillDiameter AS laminationNum
        FROM MainData m
        LEFT JOIN DrillSpecResult r ON m.ID = r.WipId
        """
        
        cursor.execute(full_sql)
        rows = cursor.fetchall()

        for row in rows:
            details = []
            if row.details:
                try:
                    details_str = row.details
                    if not details_str.startswith('['):
                        details_str = '[' + details_str
                    if not details_str.endswith(']'):
                        details_str = details_str + ']'
                    details = json.loads(details_str)
                except json.JSONDecodeError as je:
                    logging.warning(f"JSON decode error for details of ID {row.ID}: {je}")
                    logging.warning(f"Problematic JSON string: {details_str}")
            else:
                logging.warning(f"No details found for ID {row.ID}")

            main_data = {
                'lotNo': row.lotNo,
                'totalNum': row.singleNum,
                'modelNo': row.modelNo,
                'singleNum': "",
                'sendToMacNo': "",
                'drillMacCodes': "",
                'laminationNum': row.laminationNum,
                'proParameterCode': ""
            }
            upload_data = {
                'keyValue': '' if row.TData is  None or row.TData == 0 or row.TData == '' else row.TData,
                'main': main_data,
                'detail': details
            }

            formatted_data = json.dumps(upload_data, ensure_ascii=False)
            logging.info(f"发送的数据:\n{formatted_data}")

            try:
                new_data = {
                    'token': 'mxmxhbljrgnbljwjbnmx',
                    'keyValue': '',
                    'data': json.dumps(upload_data, ensure_ascii=False)
                }               
                response = requests.post(
                    'https://70bu59de971.vicp.fun/jinzhou/anonymous/lotmanage/savelotinfo',
                    data=new_data,
                )
                response_data = response.json()
                logging.info(f"接口返回原始数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                if response_data.get("data", {}).get("IsOk", False):
                    tdata = response_data.get("data", {}).get("TData", "")
                    cursor.execute("""
                        UPDATE JSYPCB_Sync_Logs 
                        SET SyncStatus = 1, 
                            TData = ?,
                            SyncTime = GETDATE(),
                            LastResult = 'Success'
                        WHERE ID = ?
                    """, (tdata, row.ID))
                    conn.commit()
                    logging.info(f"ID {row.ID} 已成功上传并更新同步状态。TData: {tdata}")
                else:
                    cursor.execute("""
                        UPDATE JSYPCB_Sync_Logs 
                        SET LastResult = ?, RetryCount = RetryCount + 1
                        WHERE ID = ?
                    """, f"Failed: {response.status_code}", row.ID)
                    conn.commit()
                    logging.error(f"ID {row.ID} 上传失败，状态码：{response.status_code}")
            except Exception as e:
                logging.error(f"处理 ID {row.ID} 时出错：{e}")
                conn.rollback()

    except Exception as e:
        logging.error(f"发生异常：{e}")
    finally:
        if 'conn' in locals():
            conn.close()
            logging.info("数据库连接已关闭")

def main():
    while True:
        check_and_upload_new_data()
        time.sleep(60)  # 每分钟检查一次

if __name__ == "__main__":
    main()