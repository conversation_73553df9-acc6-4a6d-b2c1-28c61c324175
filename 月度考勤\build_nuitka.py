#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用Nuitka打包sync.py
Nuitka是一个Python编译器，可以生成更小、更快的可执行文件
"""

import os
import sys
import subprocess
import shutil
import glob
from pathlib import Path

def check_nuitka():
    """检查Nuitka是否已安装"""
    try:
        import nuitka
        print("✅ Nuitka已安装")
        return True
    except ImportError:
        print("❌ Nuitka未安装")
        return False

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        
        # 安装Nuitka
        if not check_nuitka():
            print("📦 安装Nuitka...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "nuitka"])
            print("✅ Nuitka安装完成")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def build_with_nuitka():
    """使用Nuitka打包可执行文件"""
    print("🔨 开始使用Nuitka打包...")
    
    # 查找Excel文件
    xlsx_files = glob.glob("*.xlsx")
    include_data_files = []
    
    if xlsx_files:
        for xlsx_file in xlsx_files:
            include_data_files.append(f"--include-data-file={xlsx_file}={xlsx_file}")
            print(f"📄 找到Excel文件: {xlsx_file}")
    else:
        print("ℹ️ 没有找到Excel文件")
    
    # Nuitka命令参数
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",                  # 独立模式
        "--onefile",                     # 单文件模式
        "--windows-disable-console",     # 禁用控制台
        "--windows-company-name=海康威视",  # 公司名称
        "--windows-product-name=月度考勤同步工具",  # 产品名称
        "--windows-file-version=1.0.0.0",  # 文件版本
        "--windows-product-version=1.0.0.0",  # 产品版本
        "--output-filename=月度考勤同步工具.exe",  # 输出文件名
        "--enable-plugin=tk-inter",      # 启用tkinter插件
        "--include-package=pandas",      # 包含pandas包
        "--include-package=openpyxl",    # 包含openpyxl包
        "--include-package=requests",    # 包含requests包
    ]
    
    # 添加Excel文件
    cmd.extend(include_data_files)
    
    # 添加主程序文件
    cmd.append("sync.py")
    
    try:
        # 执行打包命令
        subprocess.check_call(cmd)
        print("✅ Nuitka打包完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka打包失败: {e}")
        return False

def build_debug_version():
    """使用Nuitka打包调试版本"""
    print("🔨 开始使用Nuitka打包调试版本...")
    
    # 查找Excel文件
    xlsx_files = glob.glob("*.xlsx")
    include_data_files = []
    
    if xlsx_files:
        for xlsx_file in xlsx_files:
            include_data_files.append(f"--include-data-file={xlsx_file}={xlsx_file}")
            print(f"📄 找到Excel文件: {xlsx_file}")
    else:
        print("ℹ️ 没有找到Excel文件")
    
    # Nuitka命令参数
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",                  # 独立模式
        "--onefile",                     # 单文件模式
        "--windows-company-name=海康威视",  # 公司名称
        "--windows-product-name=月度考勤同步工具_调试版",  # 产品名称
        "--windows-file-version=1.0.0.0",  # 文件版本
        "--windows-product-version=1.0.0.0",  # 产品版本
        "--output-filename=月度考勤同步工具_调试版.exe",  # 输出文件名
        "--enable-plugin=tk-inter",      # 启用tkinter插件
        "--include-package=pandas",      # 包含pandas包
        "--include-package=openpyxl",    # 包含openpyxl包
        "--include-package=requests",    # 包含requests包
    ]
    
    # 添加Excel文件
    cmd.extend(include_data_files)
    
    # 添加主程序文件
    cmd.append("sync.py")
    
    try:
        # 执行打包命令
        subprocess.check_call(cmd)
        print("✅ Nuitka调试版本打包完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka调试版本打包失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 sync.py Nuitka打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("sync.py"):
        print("❌ 找不到sync.py文件，请确保在正确的目录下运行此脚本")
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 使用Nuitka打包
    success = build_with_nuitka()
    
    # 如果失败，尝试打包调试版本
    if not success:
        print("\n🔨 尝试打包调试版本...")
        success = build_debug_version()
    
    if success:
        print("\n🎉 打包完成！")
        print("📁 输出文件:")
        for file in Path(".").glob("*.exe"):
            size = file.stat().st_size / (1024 * 1024)  # MB
            print(f"   {file.name} ({size:.1f} MB)")
        
        print("\n💡 使用说明:")
        print("   - 生成的exe文件可以在没有Python环境的Windows机器上运行")
        print("   - 如果遇到问题，请使用调试版本查看错误信息")
        
        return True
    else:
        print("❌ 打包失败")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
