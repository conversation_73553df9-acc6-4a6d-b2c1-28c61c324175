import requests
import time
from datetime import datetime,timedelta
import sys
import io
import os
import tkinter as tk
from tkinter import messagebox
import logging
import traceback
from pathlib import Path

# 设置控制台编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

import pandas as pd
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter

operator_names = None

# 日志配置类
class LoggerSetup:
    def __init__(self):
        self.logger = None
        self.log_file_path = None
        self.setup_logger()

    def setup_logger(self):
        """设置日志系统"""
        try:
            # 创建D盘logs目录
            log_dir = Path("D:/logs")
            log_dir.mkdir(parents=True, exist_ok=True)

            # 生成日志文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.log_file_path = log_dir / f"sync_{timestamp}.log"

            # 创建logger
            self.logger = logging.getLogger('sync_logger')
            self.logger.setLevel(logging.DEBUG)

            # 清除已有的处理器
            self.logger.handlers.clear()

            # 创建文件处理器
            file_handler = logging.FileHandler(
                self.log_file_path,
                mode='w',
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)

            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)

            # 创建格式器
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            # 添加处理器到logger
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)

            # 记录日志系统启动
            self.logger.info("=" * 60)
            self.logger.info("月度考勤同步工具启动")
            self.logger.info(f"日志文件: {self.log_file_path}")
            self.logger.info("=" * 60)

            print(f"📝 日志文件已创建: {self.log_file_path}")

        except Exception as e:
            print(f"⚠️ 日志系统初始化失败: {e}")
            print("程序将继续运行，但不会保存日志文件")

            # 创建一个简单的控制台logger
            self.logger = logging.getLogger('sync_logger_fallback')
            self.logger.setLevel(logging.INFO)
            console_handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

    def get_logger(self):
        """获取logger实例"""
        return self.logger

    def log_exception(self, e, context=""):
        """记录异常信息"""
        if self.logger:
            self.logger.error(f"异常发生 {context}: {str(e)}")
            self.logger.error(f"异常详情: {traceback.format_exc()}")

    def log_api_request(self, method, url, headers=None, data=None):
        """记录API请求"""
        if self.logger:
            self.logger.info(f"API请求: {method} {url}")
            if headers:
                self.logger.debug(f"请求头: {headers}")
            if data:
                self.logger.debug(f"请求数据: {data}")

    def log_api_response(self, response, success=True):
        """记录API响应"""
        if self.logger:
            if success:
                self.logger.info(f"API响应成功: 状态码 {response.status_code}")
                self.logger.debug(f"响应内容: {response.text[:500]}...")
            else:
                self.logger.error(f"API响应失败: 状态码 {response.status_code}")
                self.logger.error(f"响应内容: {response.text}")

    def close_logger(self):
        """关闭日志系统"""
        if self.logger:
            self.logger.info("=" * 60)
            self.logger.info("月度考勤同步工具结束")
            self.logger.info("=" * 60)

            # 关闭所有处理器
            for handler in self.logger.handlers:
                handler.close()
                self.logger.removeHandler(handler)

# 全局日志实例
log_setup = LoggerSetup()
logger = log_setup.get_logger()

def login_and_get_token():
    """
    发送登录请求并获取 token
    :return: 成功返回 token 和 other_token；失败返回 None
    """
    logger.info("开始登录流程")

    url = "https://api.hikiot.com/api-saas/open/v1/pwdLogin"

    headers = {
        'Authorization': 'Basic bGluay13ZWI6bGluaw==',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.hikiot.com',
        'Connection': 'keep-alive'
    }

    data = {
        "username": "19136753172",
        "password": "34eNtEaiBjs/c/cDnSJccA==",
        "isAuto": True
    }

    try:
        logger.info(f"发送登录请求到: {url}")
        log_setup.log_api_request("POST", url, headers, data)

        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        log_setup.log_api_response(response, True)

        result = response.json()
        logger.debug(f"登录响应解析结果: {result}")

        if result.get("code") == 0:
            data_section = result.get("data", {})
            token = data_section.get("token")
            other_token = data_section.get("otherToken")
            if token:
                logger.info("登录成功！")
                logger.info(f"Token: {token[:10]}...{token[-10:] if len(token) > 20 else token}")
                logger.info(f"Other Token: {other_token[:10]}...{other_token[-10:] if len(other_token) > 20 else other_token}")
                print("Token:", token)
                print("Other Token:", other_token)
                return token, other_token
            else:
                logger.error("响应中没有找到token")
                print("响应中没有找到token")
                return None
        else:
            error_msg = result.get("msg", "未知错误")
            logger.error(f"登录失败，错误信息：{error_msg}")
            print("登录失败，错误信息：", error_msg)
            return None
    except requests.exceptions.RequestException as e:
        log_setup.log_exception(e, "登录请求")
        print("请求异常:", e)
        return None
    except ValueError as e:
        log_setup.log_exception(e, "JSON解析")
        print("JSON 解析错误:", e)
        return None
    except Exception as e:
        log_setup.log_exception(e, "登录流程")
        print("登录过程中发生未知错误:", e)
        return None
    
def login_choose_team(bearToken):
    """
    选择团队
    :return: 成功返回 team_id；失败返回 None
    """
    url = "https://api.hikiot.com/api-link-saas/v3/team/change"
    headers = {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.hikiot.com',
        'Connection': 'keep-alive',
        'Authorization': f'Bearer {bearToken}'
    }

    data = {
        "teamNo": "ZHA1889483710563377266",
        "terminal": 2
    }
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status() 
        result = response.json()
        if result.get("code") == 0:
            print("第二步已经完成")
        else:
            print("第二步失败,错误消息：", result.get("msg", "未知错误"))
            return None
    except requests.exceptions.RequestException as e:
        print("请求异常:", e)
        return None
    except ValueError as e:
        print("JSON 解析错误:", e)
        return None    
    return 

def sync(bearToken):
    logger.info("开始同步考勤数据")

    url = "https://api.hikiot.com/api-saas/v1/sync"
    headers = {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.hikiot.com',
        'Connection': 'keep-alive',
        'Authorization': f'Bearer {bearToken}'
    }
    end_date = datetime.now().strftime('%Y-%m-%d')
    begin_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

    logger.info(f"同步日期范围: {begin_date} 到 {end_date}")
    data = {
    "syncStartDate": begin_date,
    "syncEndDate": end_date,
    "deviceSerials": [
        "FV9277048",
        "GB2007065",
        "FT1287256",
        "FT1287229",
        "FZ5688420",
        "FT1287230",
        "FZ5688452",
        "FZ5688464",
        "FZ5688461",
        "FT1287215",
        "FT1287234",
        "FT1287217",
        "FW5291856",
        "FV9277058",
        "FW5291846",
        "FW1384531",
        "FW1384527",
        "FV9277047",
        "FT1287142",
        "FU4151239",
        "FU4150701",
        "FU4150696",
        "FU4150918",
        "FU4150690",
        "FU4150698",
        "FR9981678",
        "FU4150695",
        "FU4150703",
        "FV9277044"
    ],
    "from": 1
    }
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status() 
        result = response.json()
        if result.get("code") == 0:
            print("同步中")
        else:
            print("第三步失败,错误消息：", result.get("msg", "未知错误"))
            return None
    except requests.exceptions.RequestException as e:
        print("请求异常:", e)
        return None
    except ValueError as e:
        print("JSON 解析错误:", e)
        return None    
    return 

def export_origin_record(bearToken):
    now = datetime.now().strftime('%Y-%m-%d')
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    excel_id_1=record_Api(now,bearToken)
    open_Api(excel_id_1,1) 
    time.sleep(160)
    excel_id_2=record_Api(yesterday,bearToken) 
    open_Api(excel_id_1,excel_id_2)   
    return 
def record_Api(date,bearToken):
    data = {
        "beginDate": date,
        "endDate": date,
        "containsDeletedPerson": "false",
        "allowExportAgain": "true"
    }
    url = "https://api.hikiot.com/api-saas/v1/export/origin/record"
    headers = {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.hikiot.com',
        'Connection': 'keep-alive',
        'Authorization': f'Bearer {bearToken}'
    }
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status() 
        result = response.json()
        if result.get("code") == 0:
            print(result.get("data"))
            return result.get("data")
        else:
            print("无法获取原始记录ID", result.get("msg", "未知错误"))
            return None
    except requests.exceptions.RequestException as e:
        print("请求异常:", e)
    return 

def open_Api(id1,id2):
    url = 'https://open-api.hikiot.com/auth/exchangeAppToken'
    app_key = '1905605994948616256'
    app_secret = 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKS7NdkPOkVtk+iHPKlpa+hXtFHem0DiMsV1zX9gMHHG5TWDCW8F8FTG7NRuJFwTvxmny14nt+HHW7SLcdBQz+UHlRSN/rOlpsiRQQ8bW11JrYkKmWX+VLQ0Ane2D7ZTWy8l8GmRoiNdBnt9bz/t6gMQnePkN+JvhItJREYOUr/NAgMBAAECgYEAntbpmFAHlxSO70Mfqhc99n5DIEIun8S8wgvSR8UfUUZAk3Wzrfsi/wwFJtzBcIuV1A4ombRgqXNKqO9gokaZ/iGtBQXnI0AkuDelUFrYmyV5Iy1P/mYWniaJNM7I+PkbOSHOt0F923h3NrmxOyBBD3ujdtPtKw2mYo9JbGrFkH0CQQD22uBb9sBRCdXu+LATQKQZWGotbDgIgL/MlXJ3IU0rLuznN1lbezP6EI/fYvzZ+GaCttoWZzNLGl8Lzk1KkeMvAkEAqtV9OHWf9D304XtBwPvXjzc9v2JWyF3cU+sgDlCtyCxxtAwBQhYL2io8OvA4nr7In4l9JAa4u11HAcSpQNe9wwJBAJaE5ZMNxTxkq+7X8rz6iFTwBWYG+6+rmcfMRIyBYMV5Cnj91d2jJRFQcfm7KQRbo14G0rogWTVtMhFHzPcwN38CQGdzhzQUjvuyNUQChywdlkkFE+B85b7KkC1FepEQrFxHBcgZaP2jKiRAZV7vr+n58LYj5WNWqrHT57cbZ797pk8CQHY3p2yb3fGQV5sqv5wCEyl5SUCbLnd2qwOIAogSZ8ygVSTl3k227jJyogPEk3EJ8EAtCAuy1kFgx8KEO8ZbmZo='
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        'appKey': app_key,
        'appSecret': app_secret
    }    
    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        response_data = response.json()
        print('Response Data:', response_data)
        app_access_token = response_data.get('data', {}).get('appAccessToken')
        print('appAccessToken:', app_access_token)
        if app_access_token:
            url_apply_auth_code = 'https://open-api.hikiot.com/auth/third/applyAuthCode'
            headers_apply_auth_code = {
                'Content-Type': 'application/json'
            }
            data_apply_auth_code = {
                'appKey': app_key,
                'userName': '19136753172',
                'password': 'wwxkpo35781',  
                'redirectUrl': 'https://open.hikiot.com/util'
            }
            response_apply_auth_code = requests.post(url_apply_auth_code, headers=headers_apply_auth_code, json=data_apply_auth_code)
            if response_apply_auth_code.status_code == 200:
                response_data_apply_auth_code = response_apply_auth_code.json()
                auth_code = response_data_apply_auth_code.get('data', {}).get('authCode')
                print('auth_code:', auth_code)
                if auth_code:
                    url_code2_token = 'https://open-api.hikiot.com/auth/third/code2Token'
                    headers_code2_token = {
                        'Content-Type': 'application/json',
                        'App-Access-Token': app_access_token
                    }
                    params_code2_token = {
                        'authCode': auth_code
                    }
                    response_code2_token = requests.get(url_code2_token, headers=headers_code2_token, params=params_code2_token)
                    if response_code2_token.status_code == 200:
                        response_data_code2_token = response_code2_token.json()
                        user_access_token = response_data_code2_token.get('data', {}).get('userAccessToken')
                        print('userAccessToken:', user_access_token)
                    print("获取考勤记录中")
                    url_get_adminForLeave = 'https://open-api.hikiot.com/coordination/v1/adminForLeave/page'
                    headers_get_adminForLeave={
                            'Content-Type': 'application/json',
                            'App-Access-Token': app_access_token,
                            'User-Access-Token':user_access_token
                    }
                    yesterday=(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
                    print(yesterday)
                    params_get_adminForLeave = {
                            "startTime":yesterday,
                            "endTime":yesterday,
                            "status": 2,
                            "page": 1,
                            "size": 999
                    }
                    response_get_adminForLeave = requests.post(url_get_adminForLeave, headers=headers_get_adminForLeave, json=params_get_adminForLeave)
                    global operator_names
                    operator_names=[]
                    if 1:
                        leave_data=response_get_adminForLeave.json()
                        if leave_data.get('code') == 0 and 'data' in leave_data:
                            for record in leave_data['data']:
                                operator_name = record.get('operatorName')
                                if operator_name and operator_name not in operator_names:
                                    operator_names.append(operator_name)
                            print(f'成功提取到 {len(operator_names)} 个请假人员: {operator_names}')
                        else:
                            print(f'请假记录接口返回错误: {leave_data.get("msg", "未知错误")}')
                        if user_access_token:
                                for id in id1,id2:
                                    url_file_center_detail = 'https://open-api.hikiot.com/fileCenter/v1/detail'
                                    headers_file_center_detail = {
                                        'Content-Type': 'application/json',
                                        'App-Access-Token': app_access_token
                                    }
                                    params_file_center_detail = {
                                        'id': id
                                    }
                                    response_file_center_detail = requests.get(url_file_center_detail, headers=headers_file_center_detail, params=params_file_center_detail)
                                    if response_file_center_detail.status_code == 200:
                                        response_data_file_center_detail = response_file_center_detail.json()
                                        while True:
                                            statusDesc = response_data_file_center_detail.get('data', {}).get('statusDesc')
                                            print('Current status:', statusDesc)
                                            if statusDesc == "导出中":
                                                print("文件正在导出中，等待5秒后重试...")
                                                time.sleep(5) 
                                                response_file_center_detail = requests.get(
                                                    url_file_center_detail, 
                                                    headers=headers_file_center_detail, 
                                                    params=params_file_center_detail
                                                )
                                                if response_file_center_detail.status_code == 200:
                                                    response_data_file_center_detail = response_file_center_detail.json()
                                                else:
                                                    print('File Center Detail Error:', response_file_center_detail.status_code)
                                                    break
                                            else:
                                                fileUrl = response_data_file_center_detail.get('data', {}).get('fileUrl')
                                                print('File Center Detail Response Data:', fileUrl)
                                                if fileUrl:
                                                    try:
                                                        origin_folder = r'D:\attendance\origin'
                                                        if not os.path.exists(origin_folder):
                                                            os.makedirs(origin_folder)
                                                        if id==id2:
                                                            date= (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
                                                        else:
                                                            date= datetime.now().strftime('%Y%m%d')
                                                        file_name = os.path.join(origin_folder, f"{date}.xlsx")
                                                        file_response = requests.get(fileUrl)
                                                        if file_response.status_code == 200:
                                                            with open(file_name, 'wb') as f:
                                                                f.write(file_response.content)
                                                            print(f'File successfully downloaded as {file_name}')
                                                        else:
                                                            print('Failed to download file:', file_response.status_code)
                                                    except Exception as e:
                                                        print('Error downloading file:', str(e))
                                                else:
                                                    print('No file URL found')
                                                break  
                    else:
                        print('code2Token Error:', response_code2_token.status_code, response_code2_token.text)
                else:
                    print('Failed to get auth_code')
            else:
                print('applyAuthCode Error:', response_apply_auth_code.status_code, response_apply_auth_code.text)
        else:
            print('Failed to get appAccessToken')
    else:
        print('Error:', response.status_code, response.text)


def calculate_work_hours(punch_times):
    if len(punch_times) < 2:
        return 0
    
    total_minutes = 0
    for i in range(0, len(punch_times)-1, 2):
        start_time = punch_times[i]
        end_time = punch_times[i+1]
        if start_time and end_time:
            h1, m1, _ = map(int, start_time.split(':'))
            h2, m2, _ = map(int, end_time.split(':'))
            minutes = (h2 * 60 + m2) - (h1 * 60 + m1)
            total_minutes += minutes
    
    return round(total_minutes / 60, 1)

def calculate_time_difference(time1, time2):
    if not time1 or not time2:
        return 0
    
    h1, m1, _ = map(int, time1.split(':'))
    h2, m2, _ = map(int, time2.split(':'))
    
    return abs((h2 * 60 + m2) - (h1 * 60 + m1))

def get_best_punch_time_night(all_punch_times, time_range, is_earliest):
    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        
        if start_hour > end_hour: 
            if (hour >= start_hour) or (hour <= end_hour):
                filtered_times.append((time_part, device))
        else: 
            if start_hour <= hour <= end_hour:
                filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    def get_sortable_time(time_str):
        hour = int(time_str[:2])
        minute = int(time_str[3:5])
        second = int(time_str[6:8]) if len(time_str) >= 8 else 0
        
        if start_hour > end_hour:
            if is_earliest:

                if hour >= start_hour:
                    return hour * 3600 + minute * 60 + second
                elif hour <= end_hour:
                    return (24 + hour) * 3600 + minute * 60 + second
            else:
                if hour >= start_hour:
                    return (24 + hour) * 3600 + minute * 60 + second
                elif hour <= end_hour:
                    return hour * 3600 + minute * 60 + second
        
        return hour * 3600 + minute * 60 + second
    

    if is_earliest:
        return min(filtered_times, key=lambda x: get_sortable_time(x[0]))
    else:
        return max(filtered_times, key=lambda x: get_sortable_time(x[0]))

def get_best_punch_time(all_punch_times, target_time, is_earliest):

    target_hour, target_minute = target_time
    filtered_times = []
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        current_minutes = hour * 60 + minute
        target_minutes = target_hour * 60 + target_minute

        if target_hour == 9:  
            if hour <= 9:
                filtered_times.append((time_part, device))
        elif target_hour == 11: 
            if 11 <= hour <= 13:
                filtered_times.append((time_part, device))
        elif target_hour == 12:  
            if 12 <= hour <= 14:
                filtered_times.append((time_part, device))
        elif target_hour == 17 and target_minute == 30:  
            if hour >= 17:
                filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    return min(filtered_times, key=lambda x: x[0]) if is_earliest else max(filtered_times, key=lambda x: x[0])

def get_best_punch_time_hourly(all_punch_times, time_range, is_earliest):

    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    start_minutes = start_hour * 60 + start_minute
    end_minutes = end_hour * 60 + end_minute
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        current_minutes = hour * 60 + minute

        if start_minutes <= current_minutes <= end_minutes:
            filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    return min(filtered_times, key=lambda x: x[0]) if is_earliest else max(filtered_times, key=lambda x: x[0])

def process_night_shift_record(record, punch_times):

    punch_early_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), True)
    punch_late_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), False)
    punch_morning = get_best_punch_time_night(punch_times, (6, 0, 9, 0), False)
    punch_evening = get_best_punch_time_night(punch_times, (17, 0, 22, 0), True)

    if punch_evening:
        record['打卡时间3'] = punch_evening[0]
        hour = int(punch_evening[0][:2])
        record['状态3'] = '迟到' if hour >= 20 else '正常'
    else:
        record['打卡时间3'] = ''
        record['状态3'] = '缺卡'
    
    if punch_early_night:
        record['打卡时间4'] = punch_early_night[0]
        record['状态4'] = '正常'
    else:
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'
    
    if punch_late_night:
        if record['打卡时间4'] == punch_late_night[0]:
            record['打卡时间5'] = ''
            record['状态5'] = '缺卡'
        else:
            record['打卡时间5'] = punch_late_night[0]
            record['状态5'] = '正常'
    else:
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'
    
    record['打卡时间1'] = ''
    record['打卡时间2'] = ''
    record['状态1'] = '正常'
    record['状态2'] = '正常'

    try:
        current_date = datetime.now().strftime('%Y%m%d')
        #current_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        current_file = f'D:\\attendance\\origin\\{current_date}.xlsx'
        
        if os.path.exists(current_file):
            df_current = pd.read_excel(current_file)
            employee_records = df_current[df_current['姓名'] == record['姓名']]
            morning_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    if 6< hour < 10:  # 只取10点之前的打卡记录
                        morning_punches.append((time_part, row['打卡设备']))
            
            if morning_punches:
                earliest_punch = min(morning_punches, key=lambda x: x[0])
                record['打卡时间6'] = '次日'+earliest_punch[0]
                hour = int(earliest_punch[0][:2])
                record['状态6'] = '早退' if hour < 8 else '正常'
            else:
                record['打卡时间6'] = ''
                record['状态6'] = '缺卡'
        else:
            record['打卡时间6'] = ''
            record['状态6'] = '缺卡'
    except Exception as e:
        print(f"读取当前日期打卡数据出错: {str(e)}")
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'
    
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        record['加班'] = '3.5'
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_monthly_record(record, punch_times):


    if record['类别'] == '中班':
        punch1 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
        if punch1:
            record['打卡时间1'] = punch1[0]
            record['状态1'] = '正常' if punch1[0] <= '12:00:00' else '迟到'
        else:
            record['打卡时间1'] = ''
            record['状态1'] = '缺卡'
        punch2 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), True)
        
        if punch2:
            record['打卡时间2'] = punch2[0]
            record['状态2'] = '正常' if punch2[0] >= '17:00:00' else '早退'
        else:
            record['打卡时间2'] = ''
            record['状态2'] = '缺卡'

        punch3 = get_best_punch_time_hourly(punch_times, (17, 0, 19, 0), False)
        
        if punch3:
            if punch3[0] == record['打卡时间2']:
                record['打卡时间3'] = ''
                record['状态3'] = '缺卡'
            else:
                record['打卡时间3'] = punch3[0]
                record['状态3'] = '正常' if punch3[0] <= '18:00:00' else '迟到'
        else:
            record['打卡时间3'] = ''
            record['状态3'] = '缺卡'

        night_punches = [(time_part, device) for time_part, device in punch_times 
                        if (time_part >= '23:00:00' or time_part <= '01:00:00')]
        punch4 = max(night_punches, key=lambda x: x[0]) if night_punches else None
        
        if punch4:
            record['打卡时间4'] = punch4[0]
            hour = int(punch4[0][:2])
            record['状态4'] = '正常' if hour == 0 or hour == 1 else '早退'
        else:
            record['打卡时间4'] = ''
            record['状态4'] = '缺卡'

        record['打卡时间5'] = ''
        record['打卡时间6'] = ''
        record['状态5'] = ''
        record['状态6'] = ''

        all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 5))
        if all_status_normal:
            record['正班'] = '8'
            record['加班'] = '3.5'
        else:
            record['正班'] = ''
            record['加班'] = ''

    elif record['类别'] == '经理' or record['类别'] == '保洁'  or record['类别'] == '帮厨':
        punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 12, 0), True)
        if punch1:
            record['打卡时间1'] = punch1[0]
            record['状态1'] = '正常' if punch1[0] <= '08:01:00' else '迟到'
        else:
            record['打卡时间1'] = ''
            record['状态1'] = '缺卡'
        punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 23, 59), False)
        if punch4:
            record['打卡时间4'] = punch4[0]
            record['状态4'] = '正常' if punch4[0] >= '17:30:00' else '早退'
        else:
            record['打卡时间4'] = ''
            record['状态4'] = '缺卡'

        record['打卡时间2'] = ''
        record['打卡时间3'] = ''
        record['状态2'] = '正常'
        record['状态3'] = '正常'

        record['打卡时间5'] = ''
        record['打卡时间6'] = ''
        record['状态5'] = '正常'
        record['状态6'] = '正常'

        record['正班'] = '8' if record['状态1'] == '正常' and record['状态4'] == '正常' else ''
        record['加班'] = '0'

    elif record['类别'] == '月薪':
        time_points = [
            ((9, 0), True),    
            ((11, 0), True),   
            ((12, 0), False),   
            ((17, 30), False)  
        ]

        all_status_normal = True
        for i, (target_time, is_earliest) in enumerate(time_points, 1):
            if i == 2:  
                best_punch = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), is_earliest)
            elif i == 3:  
                best_punch = get_best_punch_time_hourly(punch_times, (12, 0, 14, 0), is_earliest)
            else:
                best_punch = get_best_punch_time(punch_times, target_time, is_earliest)
            
            if best_punch:
                time_part = best_punch[0]
                record[f'打卡时间{i}'] = time_part
                
                if i == 1:  
                    record[f'状态{i}'] = '正常' if time_part <= '08:01:00' else '迟到'
                elif i == 2: 
                    record[f'状态{i}'] = '正常' if '11:00:00' <= time_part <= '13:00:00' else '早退'
                elif i == 3:  
                    record[f'状态{i}'] = '正常' if time_part <= '14:00:00' else '迟到'
                elif i == 4:  
                    record[f'状态{i}'] = '正常' if time_part >= '17:30:00' else '早退'
                
                if record[f'状态{i}'] != '正常':
                    all_status_normal = False
            else:
                record[f'打卡时间{i}'] = ''
                record[f'状态{i}'] = '缺卡'
                all_status_normal = False

        record['打卡时间5'] = ''
        record['打卡时间6'] = ''
        record['状态5'] = '正常'
        record['状态6'] = '正常'

        record['正班'] = '8' if all_status_normal else ''
        record['加班'] = '0'

    return record

def process_hourly_record(record, punch_times):
    """
    处理计时人员打卡记录
    """
    time_rules = [
        ((0, 0, 9, 0), True),     
        ((11, 30, 13, 30), True), 
        ((11, 30, 13, 30), False), 
        ((16, 0, 18, 0), True),   
        ((17, 0, 18, 0), False),  
        ((20, 0, 23, 59), False)   
    ]

    valid_times = []
    all_status_normal = True
    for i, (time_range, is_earliest) in enumerate(time_rules, 1):
        best_punch = get_best_punch_time_hourly(punch_times, time_range, is_earliest)
        if best_punch:
            time_part, device = best_punch
            record[f'打卡时间{i}'] = time_part
            valid_times.append(time_part)
            
            if i == 3:
                if record['打卡时间2'] and record['打卡时间2'] <= '12:00:00':

                    time_diff = calculate_time_difference(record['打卡时间2'], time_part)
                    if time_diff > 30:
                        record['状态3'] = '迟到'
                        all_status_normal = False
                    else:
                        record['状态3'] = '正常'
                else:
                    if time_part <= '13:31:00':
                        record['状态3'] = '正常'
                    else:
                        record['状态3'] = '迟到'
                        all_status_normal = False
            elif i == 5:
                if not record['打卡时间5']:
                    record['打卡时间5'] = ''
                    record['打卡时间6'] = ''
                    record['状态5'] = '正常'
                    record['状态6'] = '正常'
                    break
                elif record['打卡时间5'] == record['打卡时间4']:
                    record['打卡时间5'] = ''
                    record['状态5'] = '缺卡'
                    record['打卡时间6'] = ''
                    record['状态6'] = '正常'
                    break
                else:
                    time_diff = calculate_time_difference(record['打卡时间4'], time_part)
                    record['状态5'] = '迟到' if time_diff > 30 else '正常'
            else:
                record[f'状态{i}'] = '正常'
        else:
            record[f'打卡时间{i}'] = ''
            record[f'状态{i}'] = '缺卡'
            if i <= 4:
                all_status_normal = False

    if all_status_normal:
        record['正班'] = '8'
    else:
        record['正班'] = ''

    if all_status_normal:
        overtime_hours = 0
        
        if record['打卡时间2'] and record['打卡时间3']:
            time_diff = calculate_time_difference(record['打卡时间2'], record['打卡时间3'])
            if time_diff <= 30:
                overtime_hours = 1

        if record['打卡时间5'] and record['打卡时间6']:
            overtime_hours = 3
            
        if overtime_hours > 0:
            record['加班'] = str(overtime_hours)
        else:
            record['加班'] = ''
    else:
        record['加班'] = ''

    if not record['打卡时间5']:
        record['状态5'] = '正常'
    if not record['打卡时间6']:
        record['状态6'] = '正常'

    return record

def process_white_ten_record(record, punch_times):
    """
    处理白十打卡记录
    """
    
    punch1 = get_best_punch_time_hourly(punch_times, (9, 0, 11, 0), True)
    
    if punch1:
        record['打卡时间1'] = punch1[0]
        record['状态1'] = '正常' if punch1[0] <= '10:01:00' else '迟到'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)

    if punch2:
        record['打卡时间2'] = punch2[0]
        record['状态2'] = '正常' if punch2[0] >= '11:30:00' else '早退'
    else:
        record['打卡时间2'] = ''
        record['状态2'] = '缺卡'

    # 打卡时间3：11:30-13:00 取最晚
    punch3 = get_best_punch_time_hourly(punch_times, (11, 30, 13, 0), False)
    
    if punch3:
        # 检查是否与前一个打卡时间相同
        if record['打卡时间2'] == punch3[0]:
            record['打卡时间3'] = ''
            record['状态3'] = '缺卡'
        else:
            record['打卡时间3'] = punch3[0]
            if punch2:
                time_diff = calculate_time_difference(record['打卡时间2'], punch3[0])
                record['状态3'] = '正常' if time_diff <= 30 else '迟到'
            else:
                record['状态3'] = '缺卡'
    else:
        record['打卡时间3'] = ''
        record['状态3'] = '缺卡'

    # 打卡时间4：16:00-18:00 取最早，需在17:30前
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    
    if punch4:
        record['打卡时间4'] = punch4[0]
        record['状态4'] = '正常' if punch4[0] >= '17:00:00' else '早退'
    else:
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'

    # 打卡时间5：17:00-18:00 取最晚
    punch5 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), False)
    
    if punch5:
        # 检查是否与前一个打卡时间相同
        if record['打卡时间4'] == punch5[0]:
            record['打卡时间5'] = ''
            record['状态5'] = '缺卡'
        else:
            record['打卡时间5'] = punch5[0]
            if punch4:
                time_diff = calculate_time_difference(record['打卡时间4'], punch5[0])
                record['状态5'] = '正常' if time_diff <= 30 else '迟到'
            else:
                record['状态5'] = '缺卡'
    else:
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'

    # 打卡时间6：19:00-21:00 取最晚，需在20:00前
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 24, 0), False)
    
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常' if punch6[0] >= '19:00:00' else '早退'
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'
    # 工时计算
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        
        # 新的加班计算逻辑: 如果打卡时间6超过22点则加班3小时，否则计算打卡时间6减去打卡时间5的时间差
        if record['打卡时间6']:
            try:
                # 检查是否超过22点
                if record['打卡时间6'] >= '22:00:00':
                    record['加班'] = '3'
                # 否则计算打卡时间6减去打卡时间5的小时数
                elif record['打卡时间5']:
                    # 将打卡时间转换为datetime对象
                    time5 = datetime.strptime(record['打卡时间5'], '%H:%M:%S')
                    time6 = datetime.strptime(record['打卡时间6'], '%H:%M:%S')
                    # 如果跨越午夜，需要调整时间
                    if time6 < time5:
                        time6 += timedelta(days=1)
                    
                    # 计算时间差(小时)，保留一位小数
                    time_diff = round((time6 - time5).total_seconds() / 3600, 1)
                    record['加班'] = str(time_diff)
                else:
                    record['加班'] = '0'
            except Exception as e:
                print(f"计算白班十点加班时间出错: {e}")
                record['加班'] = '0'  # 出错时默认为0小时
        else:
            record['加班'] = '0'  # 如果没有打卡时间6，不计算加班
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_security_record(record, punch_times):
    """
    处理保安打卡记录
    """

    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    
    if punch1:
        record['打卡时间1'] = punch1[0]
        record['状态1'] = '正常' if punch1[0] <= '07:01:00' else '迟到'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    # 打卡时间2到打卡时间5的状态均为正常
    record['打卡时间2'] = ''
    record['打卡时间3'] = ''
    record['打卡时间4'] = ''
    record['打卡时间5'] = ''
    record['状态2'] = '正常'
    record['状态3'] = '正常'
    record['状态4'] = '正常'
    record['状态5'] = '正常'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 21, 0), False)
    
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常'  if punch6[0] >= '19:00:00' else '早退'
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'

    # 工时计算
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '12'
        record['加班'] = ''
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_b_security_record(record, punch_times):
    """
    处理保安打卡记录
    """

    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    
    if punch1:
        record['打卡时间1'] = punch1[0]
        record['状态1'] = '正常' if punch1[0] >= '07:00:00' else '早退'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    # 打卡时间2到打卡时间5的状态均为正常
    record['打卡时间2'] = ''
    record['打卡时间3'] = ''
    record['打卡时间4'] = ''
    record['打卡时间5'] = ''
    record['状态2'] = '正常'
    record['状态3'] = '正常'
    record['状态4'] = '正常'
    record['状态5'] = '正常'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 21, 0), True)
    
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常'  if punch6[0] <= '19:00:00' else '迟到'
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'

    # 工时计算
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '12'
        record['加班'] = ''
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_night_monthly_record(record, punch_times, now_file):
    """
    处理夜班月薪打卡记录
    """
    # 打卡时间1：从当前日期的Excel文件中获取，取6点-10点最晚时间
    try:
        current_date = datetime.now().strftime('%Y%m%d')
        #now = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        current_file = f'D:\\attendance\\origin\\{current_date}.xlsx'
        
        if os.path.exists(current_file):
            df_current = pd.read_excel(current_file)
            employee_records = df_current[df_current['姓名'] == record['姓名']]
            
            morning_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    if 6 <= hour <= 10:
                        morning_punches.append((time_part, row['打卡设备']))
            
            if morning_punches:
                latest_punch = max(morning_punches, key=lambda x: x[0])
                record['打卡时间1'] = latest_punch[0]
                hour = int(latest_punch[0][:2])
                record['状态1'] = '正常' if hour >= 8 else '早退'
            else:
                record['打卡时间1'] = ''
                record['状态1'] = '缺卡'
        else:
            record['打卡时间1'] = ''
            record['状态1'] = '缺卡'
    except Exception as e:
        print(f"读取当前日期打卡数据出错: {str(e)}")
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'
    
    # 打卡时间2和打卡时间3的状态为正常
    record['打卡时间2'] = ''
    record['打卡时间3'] = ''
    record['状态2'] = '正常'
    record['状态3'] = '正常'
    
    # 打卡时间4：从now_file文件中获取，取17点-21点最早时间
    try:
        if os.path.exists(now_file):
            df_now = pd.read_excel(now_file)
            employee_records = df_now[df_now['姓名'] == record['姓名']]
            
            evening_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    if 17 <= hour <= 21:
                        evening_punches.append((time_part, row['打卡设备']))
            
            if evening_punches:
                earliest_punch = min(evening_punches, key=lambda x: x[0])
                record['打卡时间4'] = earliest_punch[0]
                hour = int(earliest_punch[0][:2])
                record['状态4'] = '迟到' if hour >= 20 else '正常'
            else:
                record['打卡时间4'] = ''
                record['状态4'] = '缺卡'
        else:
            record['打卡时间4'] = ''
            record['状态4'] = '缺卡'
    except Exception as e:
        print(f"读取now_file打卡数据出错: {str(e)}")
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'
    
    # 打卡时间5：从now_file文件中获取，取0点-1.30点最早打卡时间
    try:
        if os.path.exists(now_file):
            df_now = pd.read_excel(now_file)
            employee_records = df_now[df_now['姓名'] == record['姓名']]
            
            night_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    minute = int(time_part[3:5])
                    if (hour == 0) or (hour == 1 and minute <= 30):
                        night_punches.append((time_part, row['打卡设备']))
            
            if night_punches:
                earliest_punch = min(night_punches, key=lambda x: x[0])
                record['打卡时间5'] = earliest_punch[0]
                hour = int(earliest_punch[0][:2])
                record['状态5'] = '正常' if hour > 0 else '异常'
            else:
                record['打卡时间5'] = ''
                record['状态5'] = '缺卡'
        else:
            record['打卡时间5'] = ''
            record['状态5'] = '缺卡'
    except Exception as e:
        print(f"读取now_file打卡数据出错: {str(e)}")
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'
    
    # 打卡时间6：从now_file文件中获取，取0点-1.40点最晚打卡时间
    try:
        if os.path.exists(now_file):
            df_now = pd.read_excel(now_file)
            employee_records = df_now[df_now['姓名'] == record['姓名']]
            
            night_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    minute = int(time_part[3:5])
                    if (hour == 0) or (hour == 1 and minute <= 40):
                        night_punches.append((time_part, row['打卡设备']))
            
            if night_punches:
                latest_punch = max(night_punches, key=lambda x: x[0])
                if record['打卡时间5'] == latest_punch[0]:
                    record['打卡时间6'] = ''
                    record['状态6'] = '缺卡'
                else:
                    record['打卡时间6'] = latest_punch[0]
                    hour = int(latest_punch[0][:2])
                    minute = int(latest_punch[0][3:5])
                    record['状态6'] = '正常' if (hour < 1) or (hour == 1 and minute <= 30) else '迟到'
            else:
                record['打卡时间6'] = ''
                record['状态6'] = '缺卡'
        else:
            record['打卡时间6'] = ''
            record['状态6'] = '缺卡'
    except Exception as e:
        print(f"读取now_file打卡数据出错: {str(e)}")
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        record['加班'] = '3.5'
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def filter_operator(df_exception, operator_names):
    if not operator_names or df_exception is None or df_exception.empty:
        return df_exception
    
    # 只处理异常记录表
    for index, row in df_exception.iterrows():
        if row['姓名'] in operator_names:
            # 将状态1到状态6都设置为"请假"
            for i in range(1, 7):
                df_exception.at[index, f'状态{i}'] = '请假'
            # 清空正班和加班时间
            df_exception.at[index, '正班'] = ''
            df_exception.at[index, '加班'] = ''
    
    return df_exception

def process_bai_jiu_record(record, punch_times):
    """
    处理白九打卡记录
    """

    # 打卡时间1取值为8点到10点的最早的打卡时间
    punch1 = get_best_punch_time_hourly(punch_times, (8, 0, 10, 0), True)
    if punch1:
        record['打卡时间1'] = punch1[0]
        # 状态1根据9点判断 大于九点迟到 反之正常
        record['状态1'] = '迟到' if punch1[0] > '09:00:00' else '正常'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    # 打卡时间2取值为11点到13点的最早的打卡时间
    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
    if punch2:
        record['打卡时间2'] = punch2[0]
        # 状态2根据12点判断 大于12点正常 反之早退
        record['状态2'] = '正常' if punch2[0] > '12:00:00' else '早退'
    else:
        record['打卡时间2'] = ''
        record['状态2'] = '缺卡'

    # 打卡时间3取值为11点到13点的最晚的打卡时间
    punch3 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), False)
    if punch3:
        # 如果打卡时间2等于打卡时间3则 打卡时间3为空，打卡时间3的状态为缺卡
        if record['打卡时间2'] == punch3[0]:
            record['打卡时间3'] = ''
            record['状态3'] = '缺卡'
        else:
            record['打卡时间3'] = punch3[0]
            # 如果打卡时间2不等于打卡时间3 状态3根据和打卡时间2差额 在30分钟之内为正常 反之为迟到
            if record['打卡时间2']:
                time_diff = calculate_time_difference(record['打卡时间2'], punch3[0])
                record['状态3'] = '正常' if time_diff <= 30 else '迟到'
            else:
                 record['状态3'] = '缺卡' # 如果打卡时间2不存在，打卡时间3也应为缺卡
    else:
        record['打卡时间3'] = ''
        record['状态3'] = '缺卡'

    # 打卡时间4取值为16点到18点的最早的打卡时间
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    if punch4:
        record['打卡时间4'] = punch4[0]
        # 状态4根据17.30点判断 大于正常 反之早退
        record['状态4'] = '正常' if punch4[0] > '17:30:00' else '早退'
    else:
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'

    # 打卡时间5取值为16点到18点的最晚的打卡时间
    punch5 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), False)
    if punch5:
        # 打卡时间5若等于打卡时间4 状态为异常
        if record['打卡时间4'] == punch5[0]:
             record['打卡时间5'] = punch5[0]
             record['状态5'] = '异常'
        else:
            record['打卡时间5'] = punch5[0]
            # 状态5根据和打卡时间4差额 在30分钟之内为正常 反之为迟到
            if record['打卡时间4']:
                time_diff = calculate_time_difference(record['打卡时间4'], punch5[0])
                record['状态5'] = '正常' if time_diff <= 30 else '迟到'
            else:
                 record['状态5'] = '缺卡' # 如果打卡时间4不存在，打卡时间5也应为缺卡
    else:
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'

    # 打卡时间6 取值为19点到22点的最晚的打卡时间
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 22, 0), False)
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常' # 状态6的初始判断，后面会根据打卡时间5和6是否存在进行调整
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'

    # 若是打卡时间5不存在 且打卡时间6不存在 则打卡时间5和打卡时间6的状态记为正常
    if not record['打卡时间5'] and not record['打卡时间6']:
        record['状态5'] = '正常'
        record['状态6'] = '正常'
        
    # 状态全为正常的话 正班为8小时
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        
        # 新的加班计算逻辑: 如果打卡时间6超过21点则加班3小时，否则计算打卡时间6减去打卡时间5的时间差
        if record['打卡时间6']:
            try:
                # 检查是否超过21点
                if record['打卡时间6'] >= '21:00:00':
                    record['加班'] = '3'
                # 否则计算打卡时间6减去打卡时间5的小时数
                elif record['打卡时间5']:
                    # 将打卡时间转换为datetime对象
                    time5 = datetime.strptime(record['打卡时间5'], '%H:%M:%S')
                    time6 = datetime.strptime(record['打卡时间6'], '%H:%M:%S')
                    # 如果跨越午夜，需要调整时间
                    if time6 < time5:
                        time6 += timedelta(days=1)
                    
                    # 计算时间差(小时)，保留一位小数
                    time_diff = round((time6 - time5).total_seconds() / 3600, 1)
                    record['加班'] = str(time_diff)
                else:
                    record['加班'] = '0'
            except Exception as e:
                print(f"计算白班九点加班时间出错: {e}")
                record['加班'] = '0'  # 出错时默认为0小时
        else:
            record['加班'] = '0'  # 如果没有打卡时间6，不计算加班
    else:
        record['正班'] = ''
        record['加班'] = '' # 如果状态不全正常，加班时间为空

    return record

# 添加无需打卡组别的处理函数
def process_no_punch_record(record, punch_times):
    """
    处理无需打卡组别的记录，所有状态记为正常
    """
    for i in range(1, 7):
        record[f'打卡时间{i}'] = ''
        record[f'状态{i}'] = '正常'
    record['正班'] = '8'
    record['加班'] = '0'
    return record

def process_attendance_data(input_file,operator_names=None):
    try:
        
        current_date = datetime.now().strftime('%Y%m%d')
        exception_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        #exception_date = (datetime.now() - timedelta(days=2)).strftime('%Y%m%d')
        output_file = f'D:\\attendance\\out\\out_{exception_date}.xlsx'
        exception_file = f'D:\\attendance\\exceptions\\exception_{exception_date}.xlsx'
        employee_file = 'D:\\attendance\\employee.xlsx'
        #now = datetime.now().strftime('%Y%m%d')
        #now=exception_date = (datetime.now() - timedelta(days=2)).strftime('%Y%m%d')
        now_file = f'D:\\attendance\\origin\\{exception_date}.xlsx'

        if not os.path.exists(employee_file):
            print(f'员工文件不存在: {employee_file}')
            return
        df_employee = pd.read_excel(employee_file)
        employee_category = dict(zip(df_employee['姓名'], df_employee['类别']))
        employee_department = dict(zip(df_employee['姓名'], df_employee['部门']))
        df = pd.read_excel(input_file)
        
        reorganized_data = []
        exception_data = []
        processed_employees = set()

        grouped = df.groupby(['姓名', '日期'])

        for (name, date), group in grouped:
            category = employee_category.get(name, '未知')
            
            processed_employees.add(name)
            department = group['部门'].iloc[0]

            record = {
                '姓名': name,
                '部门': department,
                '类别': category,
                '日期': date,
                '打卡时间1': '', '状态1': '缺卡',
                '打卡时间2': '', '状态2': '缺卡',
                '打卡时间3': '', '状态3': '缺卡',
                '打卡时间4': '', '状态4': '缺卡',
                '打卡时间5': '', '状态5': '缺卡',
                '打卡时间6': '', '状态6': '缺卡',
                '正班': '', '加班': ''
            }

            punch_times = []
            for _, row in group.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    punch_times.append((time_part, row['打卡设备']))
            

            if category == '夜班':
                record = process_night_shift_record(record, punch_times)
            elif category == '中班':
                record = process_monthly_record(record, punch_times)
            elif category == '白班十点': 
                record = process_white_ten_record(record, punch_times)
            elif category in ['月薪', '经理','保洁','帮厨']:
                record = process_monthly_record(record, punch_times)
            elif category == '白班':
                record = process_hourly_record(record, punch_times)
            elif category == '保安':  
                record = process_security_record(record, punch_times)
            elif category == '夜班保安':  
                record = process_b_security_record(record, punch_times)
            elif category == '夜班月薪':
                record = process_night_monthly_record(record, punch_times, now_file)
            elif category == '白班九点': # 添加白九类别处理
                record = process_bai_jiu_record(record, punch_times)
            elif category == '无需打卡': # 添加无需打卡类别处理
                record = process_no_punch_record(record, punch_times)

            reorganized_data.append(record)

            if category != '经理' and any(record[f'状态{i}'] in ['迟到', '早退', '缺卡'] for i in range(1, 7)):
                exception_data.append(record)
            elif category == '经理' and any(record[f'状态{i}'] == '缺卡' for i in [1, 4]):
                exception_data.append(record)

        for _, employee_row in df_employee.iterrows():
            name = employee_row['姓名']
            if name not in processed_employees:
                category = employee_row['类别']
                department = employee_row['部门']
                record = {
                    '姓名': name,
                    '部门': department,
                    '类别': category,
                    '日期': exception_date,
                    '打卡时间1': '', '状态1': '缺卡',
                    '打卡时间2': '', '状态2': '缺卡',
                    '打卡时间3': '', '状态3': '缺卡',
                    '打卡时间4': '', '状态4': '缺卡',
                    '打卡时间5': '', '状态5': '缺卡',
                    '打卡时间6': '', '状态6': '缺卡',
                    '正班': '', '加班': ''
                }
                reorganized_data.append(record)
                exception_data.append(record)
        df_reorganized = pd.DataFrame(reorganized_data)
        df_exception = pd.DataFrame(exception_data) if exception_data else None
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_reorganized.to_excel(writer, index=False)
            worksheet = writer.sheets['Sheet1']
            red_font = Font(color='FF0000')
            status_columns = [col for col in df_reorganized.columns if col.startswith('状态')]
            for row in range(2, len(df_reorganized) + 2):
                for col in status_columns:
                    col_idx = df_reorganized.columns.get_loc(col) + 1
                    cell = worksheet.cell(row=row, column=col_idx)
                    if cell.value in ['迟到', '早退', '缺卡']:
                        cell.font = red_font
                        
                        time_col = col.replace('状态', '打卡时间')
                        time_col_idx = df_reorganized.columns.get_loc(time_col) + 1
                        time_cell = worksheet.cell(row=row, column=time_col_idx)
                        time_cell.font = red_font

        # 保存异常记录时应用请假过滤
        if df_exception is not None and not df_exception.empty:
            # 对异常记录应用请假过滤
            if operator_names:
                df_exception = filter_operator(df_exception, operator_names)
            
            with pd.ExcelWriter(exception_file, engine='openpyxl') as writer:
                df_exception.to_excel(writer, index=False)
                
                worksheet = writer.sheets['Sheet1']
                red_font = Font(color='FF0000')
                green_font = Font(color='00AA00')
                
                for row in range(2, len(df_exception) + 2):
                    for col in status_columns:
                        col_idx = df_exception.columns.get_loc(col) + 1
                        cell = worksheet.cell(row=row, column=col_idx)
                        
                        if cell.value == '请假':
                            cell.font = green_font
                            time_col = col.replace('状态', '打卡时间')
                            time_col_idx = df_exception.columns.get_loc(time_col) + 1
                            time_cell = worksheet.cell(row=row, column=time_col_idx)
                            time_cell.font = green_font
                            
                        elif cell.value in ['迟到', '早退', '缺卡']:
                            cell.font = red_font
                            time_col = col.replace('状态', '打卡时间')
                            time_col_idx = df_exception.columns.get_loc(time_col) + 1
                            time_cell = worksheet.cell(row=row, column=time_col_idx)
                            time_cell.font = red_font
        
        print(f"\n结果已保存至: {output_file}")

        # 保存异常记录（如果有的话）
        if exception_data:
            df_exception = pd.DataFrame(exception_data)
            with pd.ExcelWriter(exception_file, engine='openpyxl') as writer:
                df_exception.to_excel(writer, index=False)
            
                # 获取工作表
                worksheet = writer.sheets['Sheet1']
                
                # 为异常记录文件也添加红色标记
                for row in range(2, len(df_exception) + 2):
                    for col in status_columns:
                        col_idx = df_exception.columns.get_loc(col) + 1
                        cell = worksheet.cell(row=row, column=col_idx)
                        if cell.value in ['迟到', '早退', '缺卡']:
                            cell.font = red_font
                            
                            # 同时将对应的打卡时间也标红
                            time_col = col.replace('状态', '打卡时间')
                            time_col_idx = df_exception.columns.get_loc(time_col) + 1
                            time_cell = worksheet.cell(row=row, column=time_col_idx)
                            time_cell.font = red_font
            
            print(f"异常记录已保存至: {exception_file}")
            #os.remove(now_file)
            print("当前夜班文件已经删除")

    except Exception as e:
        logger.error(f'处理出错: {str(e)}')
        log_setup.log_exception(e, "数据处理")
        print(f'\n处理出错: {str(e)}')
        import traceback
        print(traceback.format_exc())

def main():
    """主函数"""
    try:
        logger.info("程序开始执行")
        start_time = datetime.now()
        logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 登录获取token
        result = login_and_get_token()
        if not result:
            logger.error("登录失败，程序退出")
            return False

        token, other_token = result
        logger.info(f"最终获取的Token: {token[:10]}...{token[-10:] if len(token) > 20 else token}")
        print(f"最终获取的Token: {token}")

        # 选择团队
        logger.info("开始选择团队")
        team_result = login_choose_team(token)
        if not team_result:
            logger.warning("团队选择失败，但继续执行")

        # 同步数据
        logger.info("开始同步考勤数据")
        sync_result = sync(token)
        if not sync_result:
            logger.warning("数据同步失败，但继续执行")

        # 等待10分钟
        logger.info("等待10分钟后导出原始记录")
        #time.sleep(10 * 60)

        # 导出原始记录
        logger.info("开始导出原始记录")
        export_result = export_origin_record(token)
        if not export_result:
            logger.warning("原始记录导出失败，但继续执行")

        # 处理考勤数据
        exception_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        input_file = f'D:\\attendance\\origin\\{exception_date}.xlsx'
        logger.info(f"准备处理考勤数据文件: {input_file}")
        global operator_names
        if os.path.exists(input_file):
            logger.info("开始处理考勤数据")
            process_attendance_data(input_file,operator_names)
            logger.info("考勤数据处理完成")
        else:
            logger.error(f'输入文件不存在: {input_file}')
            print(f'输入文件不存在: {input_file}')

        # 记录程序结束时间
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"程序执行完成")
        logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"总耗时: {duration}")
        return True
    except Exception as e:
        logger.error(f"主函数执行出错: {str(e)}")
        log_setup.log_exception(e, "主函数")
        print(f"程序执行出错: {str(e)}")
        return False
    finally:
        # 关闭日志系统
        log_setup.close_logger()


# 程序入口
if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("程序执行完成")
        else:
            print("程序执行失败")
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        print("程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
    finally:
        input("按回车键退出...")
