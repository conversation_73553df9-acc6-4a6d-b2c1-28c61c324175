import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font
from openpyxl.utils.dataframe import dataframe_to_rows

# 输入/输出路径
input_path = r'D:\attendance\7\calculated_modified_origin.xlsx'
output_path = r'D:\attendance\7\inserted_and_colored_calculated_modified_origin.xlsx'

# 拆分函数
def split_hours(cell):
    try:
        total_hours = float(cell)
        if total_hours < 8:
            return (str(total_hours), "")
        else:
            return ("8.0", str(round(total_hours - 8, 1)))
    except:
        return (cell, "")

# 读取 Excel 文件
df = pd.read_excel(input_path)

# 创建一个列表用于存储最终结果
new_rows = []

# 遍历 DataFrame 中的所有行
for index, row in df.iterrows():
    # 第一行（标题行）直接添加，不处理
    if index == 0:
        new_rows.append(row.to_dict())
        continue

    # 处理数据行：先添加原行
    current_row = row.to_dict()
    hours_columns = row.index[4:]  # 假设前 4 列是固定字段（如姓名、部门等）
    split_values = {}

    # 处理 E 列及以后的打卡时间
    for col in hours_columns:
        original_value = row[col]
        first, second = split_hours(original_value)
        current_row[col] = first
        split_values[col] = second

    # 添加当前行
    new_rows.append(current_row)

    # 构造新行（A-D 列复制，E 列及以后用拆分值）
    new_row = {col: row[col] for col in row.index[:4]}  # 复制前四列
    for col in hours_columns:
        new_row[col] = split_values.get(col, "")

    # 添加新行
    new_rows.append(new_row)

# 构建新的 DataFrame
final_df = pd.DataFrame(new_rows)

# === 使用 openpyxl 写入并设置样式 ===
wb = Workbook()
ws = wb.active

# 将 DataFrame 写入工作表（带标题）
for r_idx, row in enumerate(dataframe_to_rows(final_df, index=False, header=True), 1):
    for c_idx, value in enumerate(row, 1):
        ws.cell(row=r_idx, column=c_idx, value=value)

# 标红所有含冒号 ":" 的单元格
red_font = Font(color="FF0000")  # 红色字体

for row in ws.iter_rows():
    for cell in row:
        val = cell.value
        if val is None:
            continue
        if ':' in str(val):  # 只要含冒号就标红
            cell.font = red_font

# 保存文件
wb.save(output_path)

print(f"✅ 已完成插入行 + 拆分时间 + 含冒号单元格标红")
print(f"📁 结果已保存至: {output_path}")