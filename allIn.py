import pandas as pd

# 读取 Excel 文件
file_path = r'D:\attendance\7\origin.xlsx'
df = pd.read_excel(file_path)

# 定义时间替换和去重函数
def clean_time_cell(cell):
    if isinstance(cell, str):
        # 分割时间并去除空白
        times = [t.strip() for t in cell.split('\n') if t.strip()]
        
        # 时间替换规则
        new_times = []
        for t in times:
            if t >= "07:30" and t < "08:10":
                new_times.append("08:00")
            elif t >= "9:30" and t < "10:00":
                new_times.append("10:00")           
            elif t >= "11:30" and t < "11:45":
                new_times.append("11:45")
            elif t >= "11:45" and t < "12:00":
                new_times.append("12:00")
            elif t >= "12:00" and t < "12:15":
                new_times.append("12:15")
            elif t >= "12:15" and t < "12:30":
                new_times.append("12:30")
            elif t >= "12:30" and t < "12:45":
                new_times.append("12:45")
            elif t >= "12:45" and t < "13:00":
                new_times.append("13:00")  
            elif t >= "16:50" and t < "17:00":
                new_times.append("17:00")
            elif t >= "17:00" and t < "17:15":
                new_times.append("17:15")
            elif t >= "17:15" and t < "17:30":
                new_times.append("17:30")
            elif t >= "17:30" and t < "17:45":
                new_times.append("17:45")
            elif t >= "17:45" and t < "18:00":
                new_times.append("18:00")
            elif t >= "18:00" and t < "18:15":
                new_times.append("18:15")
            elif t >= "18:15" and t < "18:30":
                new_times.append("18:30")
            elif t >= "18:15" and t < "18:30":
                new_times.append("18:30")     
            elif t >= "20:50" and t < "21:10":
                new_times.append("21:00")      
            elif t >= "21:50" and t < "22:10":
                new_times.append("22:00")    
            elif t >= "09:40" and t < "10:10":
                new_times.append("09:00")    
            else:
                new_times.append(t)
        
        # 去除重复时间并保持原始顺序
        seen = set()
        unique_times = []
        for t in new_times:
            if t not in seen:
                seen.add(t)
                unique_times.append(t)
        
        return '\n'.join(unique_times) if unique_times else ''
    return cell

# 应用处理函数
df = df.applymap(clean_time_cell)

# 保存结果
output_path = r'D:\attendance\7\modified_origin.xlsx'
df.to_excel(output_path, index=False)

print(f"文件已保存到 {output_path}")