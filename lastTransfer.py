import pandas as pd
from datetime import datetime

# 文件路径
input_path = r'D:\attendance\7\modified_origin.xlsx'
output_path = r'D:\attendance\7\calculated_modified_origin.xlsx'

# 定义时间格式
time_format = "%H:%M"

# 定义处理函数
def calculate_shift_time(cell):
    if not isinstance(cell, str):
        return cell

    times = cell.strip().split('\n')
    count = len(times)

    # 只处理 2、4、6 个时间的情况
    if count not in [2, 4, 6]:
        return cell

    try:
        time_objs = [datetime.strptime(t.strip(), time_format) for t in times]
    except ValueError:
        return cell  # 如果有非法时间格式，保留原样

    total_seconds = 0

    # 按照两两配对的方式计算总时长
    for i in range(0, count, 2):
        if i + 1 >= count:
            break
        start = time_objs[i]
        end = time_objs[i + 1]
        diff = (end - start).total_seconds()
        if diff > 0:
            total_seconds += diff

    # 转换为小时数（保留一位小数）
    total_hours = round(total_seconds / 3600, 1)
    return str(total_hours)

# 读取 Excel
df = pd.read_excel(input_path)

# 应用计算函数
df = df.applymap(calculate_shift_time)

# 保存到新文件
df.to_excel(output_path, index=False)

print(f"已成功生成计算后的文件: {output_path}")