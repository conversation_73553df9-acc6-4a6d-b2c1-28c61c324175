import pandas as pd
import os
from datetime import datetime, timedelta
import re
from openpyxl import load_workbook
from openpyxl.styles import PatternFill, Font

def clean_time_cell(cell):
    """
    从单元格中提取时间数据，处理可能包含换行符的情况，并过滤无效格式
    """
    if not isinstance(cell, str):
        return []
    
    # 处理可能包含"次日"的情况
    cell = cell.replace("次日", "")
    
    # 尝试多种可能的分隔符
    all_times = []
    
    # 处理可能的换行符
    if '\n' in cell:
        times = [t.strip() for t in cell.split('\n') if t.strip()]
        all_times.extend(times)
    else:
        # 处理空格分隔的时间
        times = [t.strip() for t in cell.split() if t.strip()]
        all_times.extend(times)
        
        # 使用正则表达式提取所有时间条目 (格式: HH:MM)
        if not all_times:
            time_pattern = r'\d{1,2}:\d{2}'
            all_times.extend(re.findall(time_pattern, cell))
    
    # 只保留有效的时间格式并标准化
    valid_times = []
    for t in all_times:
        # 去掉可能残留的"次日"前缀
        t = t.replace("次日", "").strip()
        
        # 接受HH:MM或H:MM格式
        if re.match(r'^\d{1,2}:\d{2}$', t):
            # 标准化为HH:MM格式
            if len(t) == 4:  # 如果是H:MM格式
                t = '0' + t
            valid_times.append(t)
    
    # 转换为 (时间, 设备) 元组列表 - 设备为空，因为数据中没有提供
    return [(f"{t}:00", "") for t in valid_times]

def calculate_time_difference(time1, time2):
    """
    计算两个时间之间的差值 (以分钟为单位)
    """
    if not time1 or not time2:
        return 0
    
    h1, m1, s1 = map(int, time1.split(':'))
    h2, m2, s2 = map(int, time2.split(':'))
    
    return abs((h2 * 60 + m2) - (h1 * 60 + m1))

def get_best_punch_time_hourly(all_punch_times, time_range, is_earliest):
    """
    计时人员的打卡时间选择
    """
    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    start_minutes = start_hour * 60 + start_minute
    end_minutes = end_hour * 60 + end_minute
    
    # 放宽结束时间5分钟，允许有误差
    extended_end_minutes = end_minutes + 5
    
    for time_part, device in all_punch_times:
        try:
            hour = int(time_part[:2])
            minute = int(time_part[3:5])
            current_minutes = hour * 60 + minute

            # 放宽结束时间条件，允许超出5分钟
            if start_minutes <= current_minutes <= extended_end_minutes:
                filtered_times.append((time_part, device))
        except (ValueError, IndexError):
            # 跳过无效的时间格式
            continue

    if not filtered_times:
        return None
    
    if is_earliest:
        result = min(filtered_times, key=lambda x: x[0])
    else:
        result = max(filtered_times, key=lambda x: x[0])
        
    return result

def get_best_punch_time_night(all_punch_times, time_range, is_earliest):
    """
    夜班的打卡时间选择
    """
    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    
    for time_part, device in all_punch_times:
        try:
            hour = int(time_part[:2])
            minute = int(time_part[3:5])
            
            # 处理跨日的情况
            if start_hour > end_hour:  # 例如23:00-01:00
                if (hour >= start_hour) or (hour <= end_hour):
                    filtered_times.append((time_part, device))
            else:  # 正常情况，如06:00-09:00
                if start_hour <= hour <= end_hour:
                    filtered_times.append((time_part, device))
        except (ValueError, IndexError) as e:
            # 跳过无效的时间格式
            continue

    if not filtered_times:
        return None
    
    # 创建一个排序键函数，正确处理跨午夜的时间
    def get_sortable_time(time_str):
        try:
            hour = int(time_str[:2])
            minute = int(time_str[3:5])
            second = int(time_str[6:8]) if len(time_str) >= 8 else 0
            
            # 对于23:00-01:00这样的跨午夜范围的特殊处理
            if start_hour > end_hour:
                # 如果是寻找最早时间，我们希望23点时间排在0点前面
                if is_earliest:
                    # 23点时间保持较小值
                    if hour >= start_hour:
                        return hour * 3600 + minute * 60 + second
                    # 0-1点时间给一个很大的值，确保它们排在后面
                    elif hour <= end_hour:
                        return (24 + hour) * 3600 + minute * 60 + second
                # 如果是寻找最晚时间，我们希望23点时间排在0点后面
                else:
                    if hour >= start_hour:
                        return (24 + hour) * 3600 + minute * 60 + second
                    # 0-1点时间保持原值
                    elif hour <= end_hour:
                        return hour * 3600 + minute * 60 + second
            
            return hour * 3600 + minute * 60 + second
        except (ValueError, IndexError):
            # 返回一个非常大的值，确保无效的时间排在最后
            return float('inf') if not is_earliest else float('-inf')
    
    # 根据排序键函数排序
    if is_earliest:
        result = min(filtered_times, key=lambda x: get_sortable_time(x[0]))
    else:
        result = max(filtered_times, key=lambda x: get_sortable_time(x[0]))
    
    return result

def get_best_punch_time(all_punch_times, target_time, is_earliest):
    """
    从打卡时间列表中选择最合适的时间（月薪人员）
    """
    target_hour, target_minute = target_time
    filtered_times = []
    
    for time_part, device in all_punch_times:
        try:
            hour = int(time_part[:2])
            minute = int(time_part[3:5])
            current_minutes = hour * 60 + minute
            target_minutes = target_hour * 60 + target_minute

            # 根据不同的目标时间应用不同的过滤规则
            if target_hour == 9:  # 目标时间为9:00
                if is_earliest:  # 取9点之前的最早打卡
                    if hour <= 9:
                        filtered_times.append((time_part, device))
                else:  # 取9点之后的最晚打卡
                    if hour >= 9:
                        filtered_times.append((time_part, device))
            elif target_hour == 11:  # 目标时间为11:00 (午餐开始)
                if 11 <= hour <= 13:
                    filtered_times.append((time_part, device))
            elif target_hour == 12:  # 目标时间为12:00 (午餐结束)
                if 12 <= hour <= 14:
                    filtered_times.append((time_part, device))
            elif target_hour == 17 and target_minute == 30:  # 目标时间为17:30
                if hour >= 17:
                    filtered_times.append((time_part, device))
        except (ValueError, IndexError) as e:
            # 跳过无效的时间格式
            continue

    if not filtered_times:
        return None
    
    # 根据是否取最早选择返回结果
    if is_earliest:
        result = min(filtered_times, key=lambda x: x[0])
    else:
        result = max(filtered_times, key=lambda x: x[0])
        
    return result

def process_night_shift_record(punch_times):
    """
    处理夜班打卡记录
    """
    # 获取各个时间段的打卡记录
    
    # 打卡时间4：23:00-01:00 最早一次打卡记录
    punch_early_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), True)
    
    # 打卡时间5：23:00-01:00 最晚一次打卡记录
    punch_late_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), False)
    
    # 早上打卡：6:00-9:00 最晚一次打卡记录
    punch_morning = get_best_punch_time_night(punch_times, (6, 0, 9, 0), False)
    
    # 晚上打卡：17:00-22:00 最早一次打卡记录
    punch_evening = get_best_punch_time_night(punch_times, (17, 0, 22, 0), True)
    
    # 检查状态
    all_status_normal = True
    
    # 检查各时间点的状态
    if not punch_evening:
        all_status_normal = False
    elif int(punch_evening[0][:2]) >= 20:
        all_status_normal = False
    
    if not punch_early_night:
        all_status_normal = False
    
    if not punch_late_night:
        all_status_normal = False
    
    # 计算工时
    if all_status_normal:
        return '8', '3.5'
    else:
        # 放宽条件：只要有一个晚上打卡（17:00-01:00）和一个早上打卡（6:00-9:00），就给最低工时
        has_evening_or_night = punch_evening or punch_early_night or punch_late_night
        if has_evening_or_night and punch_morning:
            return '8', ''
            
        return '', ''

def process_monthly_salary_record(punch_times):
    """
    专门处理月薪人员的打卡记录，确保不会有加班时间
    """
    # 定义四个关键时间点：(目标时间, 是否取最早)
    time_points = [
        ((9, 0), True),     # 打卡时间1: 9:00前最早
        ((11, 0), True),    # 打卡时间2: 11:00-13:00最早
        ((12, 0), False),   # 打卡时间3: 12:00-14:00最晚
        ((17, 30), False)   # 打卡时间4: 17:30后最晚
    ]

    all_status_normal = True
    punch_results = []  # 存储每个时间点的打卡结果
    has_duplicate_punch = False  # 添加标志来跟踪是否有重复打卡
    
    for i, (target_time, is_earliest) in enumerate(time_points, 1):
        if i == 2:  # 打卡时间2
            best_punch = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), is_earliest)
        elif i == 3:  # 打卡时间3
            best_punch = get_best_punch_time_hourly(punch_times, (12, 0, 14, 0), is_earliest)
            
            # 特别处理：检查是否与打卡时间2使用了同一个打卡记录
            if best_punch and punch_results[1]:
                # 如果使用了完全相同的打卡记录
                if best_punch[0] == punch_results[1][0]:
                    best_punch = None
                    all_status_normal = False
                    has_duplicate_punch = True  # 标记有重复打卡
                # 即使不是完全相同的时间，也要检查是否是同一次打卡(时间差小于1分钟)
                elif best_punch and punch_results[1]:
                    time_diff = calculate_time_difference(best_punch[0], punch_results[1][0])
                    if time_diff < 2:  # 如果时间差小于2分钟，认为是同一次打卡
                        best_punch = None
                        all_status_normal = False
                        has_duplicate_punch = True  # 标记有重复打卡
        else:
            best_punch = get_best_punch_time(punch_times, target_time, is_earliest)
        
        punch_results.append(best_punch)  # 保存打卡结果
        
        if best_punch:
            time_part = best_punch[0]
            
            # 原有的时间检查逻辑
            if i == 1:  # 早上
                if time_part > '08:01:00':
                    all_status_normal = False
            elif i == 2:  # 中午
                if not('11:00:00' <= time_part <= '13:00:00'):
                    all_status_normal = False
            elif i == 3:  # 下午
                if time_part > '14:00:00':
                    all_status_normal = False
            elif i == 4:  # 晚上
                if time_part < '17:30:00':
                    all_status_normal = False
        else:
            all_status_normal = False

    # 月薪工时计算 - 月薪人员永远不会有加班时间
    if all_status_normal:
        return '8', ''  # 月薪人员不会有加班时间
    else:
        # 对于何亮特殊处理 - 但如果有重复打卡，不适用此规则
        if has_duplicate_punch:
            return '', ''  # 有重复打卡，不计算工时
            
        # 只有在非重复打卡的情况下，才应用特殊规则
        has_morning = False
        has_evening = False
        
        for time_part, _ in punch_times:
            hour = int(time_part[:2])
            # 早上有打卡(6-9点)
            if 6 <= hour <= 9:
                has_morning = True
            # 晚上有打卡(17点后)
            if hour >= 17:
                has_evening = True
        
        # 只要早上和晚上都有打卡，就给予基本工时
        if has_morning and has_evening:
            return '8', ''  # 月薪人员不会有加班时间
        
        return '', ''

def process_monthly_record(category, punch_times):
    """
    处理月薪人员打卡记录（包含经理、中班和普通月薪逻辑）
    """
    # 月薪人员单独处理，确保不会有加班时间
    if category == '月薪':
        return process_monthly_salary_record(punch_times)
        
    # 中班处理逻辑
    if category == '中班':
        # 打卡时间1：11:00-13:00 取最早，需在12:00前
        punch1 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
        status1 = '正常' if punch1 and punch1[0] <= '12:00:00' else '迟到' if punch1 else '缺卡'

        # 打卡时间2：17:00-18:00 取最早，需在17:30后
        punch2 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), True)
        status2 = '正常' if punch2 and punch2[0] >= '17:00:00' else '早退' if punch2 else '缺卡'

        # 打卡时间3：17:00-18:00 取最晚
        punch3 = get_best_punch_time_hourly(punch_times, (17, 0, 19, 0), False)
        
        # 检查打卡时间2和打卡时间3是否相同
        if punch3 and punch2 and punch3[0] == punch2[0]:
            punch3 = None
            status3 = '缺卡'
        else:
            status3 = '正常' if punch3 and punch1 and punch1[0] <= '18:00:00' else '迟到' if punch3 else '缺卡'

        # 打卡时间4：23:00-01:00（跨日）取最晚
        night_punches = [(time_part, device) for time_part, device in punch_times 
                        if (time_part >= '23:00:00' or time_part <= '01:00:00')]
        punch4 = max(night_punches, key=lambda x: x[0]) if night_punches else None
        
        hour = int(punch4[0][:2]) if punch4 else -1
        status4 = '正常' if hour == 0 or hour == 1 else '早退' if punch4 else '缺卡'

        all_status_normal = (status1 == '正常' and status2 == '正常' and 
                            status3 == '正常' and status4 == '正常')
        
        if all_status_normal:
            return '8', '3.5'
        else:
            return '', ''

    # 经理处理逻辑
    elif category in ['经理', '保洁', '帮厨']:
        # 打卡时间1：全天范围取最早，需在8:00前
        punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 12, 0), True)
        status1 = '正常' if punch1 and punch1[0] <= '08:01:00' else '迟到' if punch1 else '缺卡'

        # 打卡时间4：16:00-23:59取最晚，需在17:30后
        punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 23, 59), False)
        status4 = '正常' if punch4 and punch4[0] >= '17:30:00' else '早退' if punch4 else '缺卡'

        # 经理工时计算
        if status1 == '正常' and status4 == '正常':
            return '8', '0'
        else:
            return '', ''
            
    return '', ''

def process_hourly_record(punch_times):
    """
    处理计时人员打卡记录（白班）
    """
    time_rules = [
        ((0, 0, 9, 0), True),      # 打卡时间1: 9点之前最早
        ((11, 30, 13, 30), True),  # 打卡时间2: 11:30-13:30最早
        ((11, 30, 13, 30), False), # 打卡时间3: 11:30-13:30最晚
        ((16, 0, 18, 0), True),    # 打卡时间4: 16:00-18:00最早
        ((17, 0, 18, 0), False),   # 打卡时间5: 17:00-18:00最晚
        ((20, 0, 23, 59), False)   # 打卡时间6: 20:00后最晚
    ]

    punch_records = []
    all_status_normal = True
    actual_punches = []  # 存储实际打卡结果用于比较
    
    for i, (time_range, is_earliest) in enumerate(time_rules, 1):
        best_punch = get_best_punch_time_hourly(punch_times, time_range, is_earliest)
        
        # 检查与前一个打卡时间是否相同
        if best_punch and len(actual_punches) > 0 and best_punch[0] == actual_punches[-1][0]:
            # 如果与前一个打卡时间相同，则视为缺卡
            best_punch = None
            all_status_normal = False
        
        if best_punch:
            time_part, device = best_punch
            punch_records.append(time_part)
            actual_punches.append(best_punch)
            
            if i == 3:
                if len(punch_records) >= 2 and punch_records[1] and punch_records[1] <= '12:00:00':
                    # 如果打卡时间2在12点之前，打卡时间3需在打卡时间2后30分钟内
                    time_diff = calculate_time_difference(punch_records[1], time_part)
                    if time_diff > 30:
                        all_status_normal = False
                else:
                    # 如果打卡时间2在12点之后，打卡时间3需在13:30之前
                    if time_part > '13:31:00':
                        all_status_normal = False
            elif i == 5:
                if len(punch_records) >= 5 and punch_records[3] == time_part:
                    all_status_normal = False
                elif len(punch_records) >= 4 and punch_records[3]:
                    time_diff = calculate_time_difference(punch_records[3], time_part)
                    if time_diff > 30:
                        all_status_normal = False
        else:
            if i <= 4:  # 前四个打卡时间是必须的
                all_status_normal = False
            punch_records.append('')

    if all_status_normal:
        # 计算加班时间
        overtime_hours = 0
        
        # 规则1: 打卡时间2和打卡时间3相差30分钟记为1小时加班
        if len(punch_records) >= 3 and punch_records[1] and punch_records[2]:
            time_diff = calculate_time_difference(punch_records[1], punch_records[2])
            if time_diff <= 30:
                overtime_hours = 1
        
        # 规则2: 存在打卡时间5和6则加班总时长为3小时
        if len(punch_records) >= 6 and punch_records[4] and punch_records[5]:
            overtime_hours = 3
            
        return '8', str(overtime_hours) if overtime_hours > 0 else ''
    else:
        return '', ''

def process_white_ten_record(punch_times):
    """
    处理白十打卡记录
    """
    # 打卡时间1：9:00-11:00 取最早，需在10:00前
    punch1 = get_best_punch_time_hourly(punch_times, (9, 0, 11, 0), True)
    status1 = '正常' if punch1 and punch1[0] <= '10:01:00' else '迟到' if punch1 else '缺卡'

    # 打卡时间2：11:00-13:00 取最早
    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
    status2 = '正常' if punch2 and punch2[0] >= '11:30:00' else '早退' if punch2 else '缺卡'

    # 打卡时间3：11:30-13:00 取最晚
    punch3 = get_best_punch_time_hourly(punch_times, (11, 30, 13, 0), False)
    
    # 检查打卡时间2和打卡时间3是否相同
    if punch3 and punch2 and punch3[0] == punch2[0]:
        punch3 = None
        status3 = '缺卡'
    elif punch3 and punch2:
        time_diff = calculate_time_difference(punch2[0], punch3[0])
        status3 = '正常' if time_diff <= 30 else '迟到'
    else:
        status3 = '缺卡'

    # 打卡时间4：16:00-18:00 取最早
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    status4 = '正常' if punch4 and punch4[0] >= '17:00:00' else '早退' if punch4 else '缺卡'

    # 打卡时间5：17:00-18:00 取最晚
    punch5 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), False)
    
    # 检查打卡时间4和打卡时间5是否相同
    if punch5 and punch4 and punch5[0] == punch4[0]:
        punch5 = None
        status5 = '缺卡'
    elif punch5 and punch4:
        time_diff = calculate_time_difference(punch4[0], punch5[0])
        status5 = '正常' if time_diff <= 30 else '迟到'
    else:
        status5 = '缺卡'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 24, 0), False)
    status6 = '正常' if punch6 and punch6[0] >= '19:00:00' else '早退' if punch6 else '缺卡'

    # 工时计算
    all_status_normal = (status1 == '正常' and status2 == '正常' and 
                        status3 == '正常' and status4 == '正常' and 
                        status5 == '正常' and status6 == '正常')
    
    if all_status_normal:
        # 加班计算: 打卡时间6减去19:00再加上0.5小时
        overtime_hours = 0.5  # 默认值
        if punch6:
            try:
                # 将打卡时间6和19:00转换为datetime对象
                punch_time = datetime.strptime(punch6[0], '%H:%M:%S')
                base_time = datetime.strptime('19:00:00', '%H:%M:%S')
                
                if punch_time >= base_time:
                    # 计算时间差(小时)
                    time_diff = (punch_time - base_time).total_seconds() / 3600
                    # 加上0.5小时
                    overtime_hours = round(time_diff + 0.5, 1)
            except Exception as e:
                pass
                
        return '8', str(overtime_hours)
    else:
        return '', ''

def process_security_record(punch_times):
    """
    处理保安打卡记录
    """
    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    status1 = '正常' if punch1 and punch1[0] <= '07:01:00' else '迟到' if punch1 else '缺卡'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 21, 0), False)
    status6 = '正常' if punch6 and punch6[0] >= '19:00:00' else '早退' if punch6 else '缺卡'

    # 工时计算
    if status1 == '正常' and status6 == '正常':
        return '12', ''
    else:
        return '', ''

def process_b_security_record(punch_times):
    """
    处理夜班保安打卡记录
    """
    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    status1 = '正常' if punch1 and punch1[0] >= '07:00:00' else '早退' if punch1 else '缺卡'

    # 打卡时间6：19:00-21:00 取最早
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 21, 0), True)
    status6 = '正常' if punch6 and punch6[0] <= '19:00:00' else '迟到' if punch6 else '缺卡'

    # 工时计算
    if status1 == '正常' and status6 == '正常':
        return '12', ''
    else:
        return '', ''

def process_bai_jiu_record(punch_times):
    """
    处理白九打卡记录
    """
    # 打卡时间1取值为8点到10点的最早的打卡时间
    punch1 = get_best_punch_time_hourly(punch_times, (8, 0, 10, 0), True)
    status1 = '正常' if punch1 and punch1[0] <= '09:00:00' else '迟到' if punch1 else '缺卡'

    # 打卡时间2取值为11点到13点的最早的打卡时间
    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
    status2 = '正常' if punch2 and punch2[0] > '12:00:00' else '早退' if punch2 else '缺卡'

    # 打卡时间3取值为11点到13点的最晚的打卡时间
    punch3 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), False)
    
    # 检查打卡时间2和打卡时间3是否相同
    if punch3 and punch2 and punch3[0] == punch2[0]:
        punch3 = None
        status3 = '缺卡'
    elif punch3 and punch2:
        time_diff = calculate_time_difference(punch2[0], punch3[0])
        status3 = '正常' if time_diff <= 30 else '迟到'
    else:
        status3 = '缺卡'

    # 打卡时间4取值为16点到18点的最早的打卡时间
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    status4 = '正常' if punch4 and punch4[0] > '17:30:00' else '早退' if punch4 else '缺卡'

    # 打卡时间5取值为16点到18点的最晚的打卡时间
    punch5 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), False)
    
    # 检查打卡时间4和打卡时间5是否相同
    if punch5 and punch4 and punch5[0] == punch4[0]:
        punch5 = None
        status5 = '缺卡'
    elif punch5:
        if punch4:
            time_diff = calculate_time_difference(punch4[0], punch5[0])
            status5 = '正常' if time_diff <= 30 else '迟到'
        else:
            status5 = '缺卡'
    else:
        status5 = '缺卡'

    # 打卡时间6 取值为19点到22点的最晚的打卡时间
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 22, 0), False)
    status6 = '正常' if punch6 else '缺卡'

    # 若是打卡时间5不存在 且打卡时间6不存在 则打卡时间5和打卡时间6的状态记为正常
    if not punch5 and not punch6:
        status5 = '正常'
        status6 = '正常'
        
    # 状态全为正常的话 正班为8小时
    all_status_normal = (status1 == '正常' and status2 == '正常' and 
                        status3 == '正常' and status4 == '正常' and
                        status5 == '正常' and status6 == '正常')
    
    if all_status_normal:
        # 计算加班时间
        overtime_hours = 0
        
        # 规则1: 打卡时间2和打卡时间3相差30分钟记为1小时加班
        if punch2 and punch3:
            time_diff = calculate_time_difference(punch2[0], punch3[0])
            if time_diff <= 30:
                overtime_hours = 1
        
        # 规则2: 存在打卡时间5和6的 加班总时长为打卡时间6减去打卡时间5
        if punch5 and punch6:
            try:
                # 将时间字符串转换为datetime对象进行计算
                time5 = datetime.strptime(punch5[0], '%H:%M:%S')
                time6 = datetime.strptime(punch6[0], '%H:%M:%S')
                # 如果跨越午夜，需要调整时间
                if time6 < time5:
                    time6 += timedelta(days=1)
                
                time_diff = time6 - time5
                # 计算小时差，保留一位小数
                overtime_hours = round(time_diff.total_seconds() / 3600, 1)
            except Exception as e:
                pass
        
        return '8', str(overtime_hours) if overtime_hours > 0 else ''
    else:
        return '', ''

def process_no_punch_record():
    """
    处理无需打卡组别的记录
    """
    return '8', '0'

def process_attendance():
    """
    主函数，处理考勤数据
    """
    try:
        # 定义文件路径
        origin_file = r'D:\attendance\month\6\origin.xlsx'
        schedule_dir = r'D:\attendance\month\6\schedule'
        
        # 检查文件是否存在
        if not os.path.exists(origin_file):
            print(f"打卡记录文件不存在: {origin_file}")
            return
        if not os.path.exists(schedule_dir):
            print(f"排班目录不存在: {schedule_dir}")
            return
        
        # 查找schedule目录下的所有xlsx文件
        schedule_files = [f for f in os.listdir(schedule_dir) if f.endswith('.xlsx')]
        if not schedule_files:
            print(f"排班目录下没有找到xlsx文件: {schedule_dir}")
            return
            
        # 读取原始数据
        df_origin = pd.read_excel(origin_file)
        
        # 获取列名
        columns = df_origin.columns.tolist()
        name_column = columns[0]  # 姓名列名
        
        # 创建新的结果数据
        result_data = []
        
        # 先处理原始行，并添加正班和加班行
        for i in range(len(df_origin)):
            row = df_origin.iloc[i].copy()
            
            # 添加原始行数据
            orig_row = row.tolist()
            result_data.append(orig_row)
            
            # 添加空的正班行
            regular_row = [''] * len(orig_row)
            regular_row[0] = '正班'
            result_data.append(regular_row)
            
            # 添加空的加班行
            overtime_row = [''] * len(orig_row)
            overtime_row[0] = '加班'
            result_data.append(overtime_row)
        
        # 创建结果DataFrame
        df_result = pd.DataFrame(result_data, columns=df_origin.columns)
        
        # 处理每个排班文件
        for schedule_file in schedule_files:
            # 从文件名中提取日期数字（例如：16.xlsx -> 16）
            date_str = os.path.splitext(schedule_file)[0]
            print(f"处理排班文件: {schedule_file}, 日期: {date_str}")
            
            # 在origin的列名中查找对应的日期列
            target_column = None
            for i, col in enumerate(columns):
                if str(col) == date_str:
                    target_column = i
                    break
            
            if target_column is None:
                print(f"警告: 在考勤表中未找到对应的列名 '{date_str}'")
                continue
                
            s_column = columns[target_column]
            print(f"找到对应列: 索引={target_column}, 名称={s_column}")
            
            # 读取排班文件获取员工类别信息 - 直接使用固定列名，与out.py保持一致
            schedule_path = os.path.join(schedule_dir, schedule_file)
            df_schedule = pd.read_excel(schedule_path)
            
            # 尝试直接读取姓名和类别列
            try:
                # 假设排班表的第一列是姓名，第四列是类别
                employee_category = {}
                employee_department = {}  # 新增字典保存员工所属部门
                for _, row in df_schedule.iterrows():
                    if pd.notna(row.iloc[1]) and pd.notna(row.iloc[3]):  # 第2列是姓名，第4列是类别
                        name_str = str(row.iloc[1]).strip()
                        category_str = str(row.iloc[3]).strip()
                        if len(name_str) <= 5 and '/' not in name_str and '-' not in name_str:
                            employee_category[name_str] = category_str
                            
                            # 尝试读取部门信息（通常在第3列或其他列）
                            if pd.notna(row.iloc[0]):  # 假设部门在第1列
                                department_str = str(row.iloc[0]).strip()
                                employee_department[name_str] = department_str
                
                print(f"员工类别信息: {employee_category}")
                print(f"员工部门信息: {employee_department}")
                
                if not employee_category:
                    print("警告: 未能从排班表中读取到有效的员工类别信息，将使用默认类别'白班'")
            except Exception as e:
                print(f"读取排班表出错: {e}")
                employee_category = {}
                employee_department = {}
            
            # 处理所有员工行
            for i in range(len(df_origin)):
                name = df_origin.iloc[i][name_column] if not pd.isna(df_origin.iloc[i][name_column]) else None
                punch_data = df_origin.iloc[i][s_column] if not pd.isna(df_origin.iloc[i][s_column]) else None
                
                # 初始化正班和加班时间
                regular_hours, overtime_hours = '', ''
                
                if name and isinstance(name, str):  # 确保名字是字符串
                    # 获取员工类别
                    name_str = str(name).strip()
                    category = employee_category.get(name_str, '白班')  # 默认类别为白班
                    print(f"处理员工: {name_str}, 类别: {category}")
                    
                    # 夜班需要取下一列的数据
                    if category == '夜班':
                        next_column_index = target_column + 1
                        if next_column_index < len(columns):
                            next_column = columns[next_column_index]
                            next_punch_data = df_origin.iloc[i][next_column] if not pd.isna(df_origin.iloc[i][next_column]) else None
                            
                            # 确保下一列有打卡数据
                            if isinstance(next_punch_data, str):
                                print(f"夜班员工 {name_str} 使用下一列数据: {next_punch_data}")
                                punch_times = clean_time_cell(next_punch_data)
                                if punch_times:
                                    regular_hours, overtime_hours = process_night_shift_record(punch_times)
                            else:
                                print(f"夜班员工 {name_str} 下一列无打卡数据")
                                punch_times = []
                        else:
                            print(f"夜班员工 {name_str} 没有下一列")
                            punch_times = []
                    elif isinstance(punch_data, str):  # 其他类别使用当前列，确保有打卡数据
                        # 其他类别使用当前列
                        punch_times = clean_time_cell(punch_data)
                        
                        # 根据员工类别应用不同的处理函数
                        if category == '月薪':
                            # 月薪人员专用处理函数
                            regular_hours, overtime_hours = process_monthly_salary_record(punch_times)
                        elif category in ['经理', '保洁', '帮厨', '中班']:
                            regular_hours, overtime_hours = process_monthly_record(category, punch_times)
                        elif category == '白班' or category == '白 班':  # 处理可能的空格
                            regular_hours, overtime_hours = process_hourly_record(punch_times)
                        elif category == '白班十点' or category == ' 白班十点':  # 处理可能的空格
                            regular_hours, overtime_hours = process_white_ten_record(punch_times)
                        elif category == '保安':
                            regular_hours, overtime_hours = process_security_record(punch_times)
                        elif category == '夜班保安':
                            regular_hours, overtime_hours = process_b_security_record(punch_times)
                        elif category == '白班九点':
                            regular_hours, overtime_hours = process_bai_jiu_record(punch_times)
                        elif category == '无需打卡' or category == '无 需打卡' or category == '无需打 卡':  # 处理可能的空格
                            regular_hours, overtime_hours = process_no_punch_record()
                        elif '夜班' in category and '月薪' in category:  # 处理"夜班月薪"类别
                            # 夜班月薪特殊处理：使用夜班逻辑计算正班，但不计算加班
                            regular_hours, _ = process_night_shift_record(punch_times)
                            overtime_hours = ''
                    else:
                        print(f"员工 {name_str} 当前列无打卡数据")
                
                # 更新正班行
                df_result.iloc[i*3+1, target_column] = regular_hours
                
                # 更新加班行 - 确保月薪人员不会有加班时间
                if '月薪' in str(employee_category.get(str(name).strip(), '')):
                    overtime_hours = ''
                df_result.iloc[i*3+2, target_column] = overtime_hours
        
        # 获取需要保留的行索引（排除外来人员和总经办）
        keep_indices = []
        for i in range(len(df_origin)):
            name = df_origin.iloc[i][name_column] if not pd.isna(df_origin.iloc[i][name_column]) else None
            if name and isinstance(name, str):
                name_str = str(name).strip()
                # 获取部门信息
                department = employee_department.get(name_str, "")
                
                # 如果部门不是外来人员或总经办，保留该行及其对应的正班和加班行
                if "外来人员" not in department and "总经办" not in department:
                    keep_indices.extend([i*3, i*3+1, i*3+2])
        
        # 根据保留的行索引过滤结果DataFrame
        df_result_filtered = df_result.iloc[keep_indices]
        
        # 保存结果
        output_file = r'D:\attendance\month\6\origin_processed.xlsx'
        df_result_filtered.to_excel(output_file, index=False)
        
        # 添加单元格标红功能
        # 加载已保存的Excel文件
        wb = load_workbook(output_file)
        ws = wb.active
        
        # 红色字体样式
        red_font = Font(color="FF0000")
        
        # 找到列名为"1"的列开始的索引
        start_col_idx = None
        for col_idx, col_name in enumerate(columns, 1):  # Excel列索引从1开始
            if str(col_name) == "1":
                start_col_idx = col_idx
                break
        
        if start_col_idx is None:
            print("警告: 未找到列名为'1'的列，无法进行标红处理")
        else:
            # 遍历从列名为"1"开始的所有列
            for col_idx in range(start_col_idx, len(columns) + 1):  # +1是因为Excel的列索引从1开始
                # 遍历所有行，标记正班时间为空的单元格
                for row in range(2, ws.max_row + 1):  # 从第2行开始（跳过标题行）
                    if ws.cell(row=row, column=1).value == '正班':  # 检查是否是正班行
                        cell = ws.cell(row=row, column=col_idx)
                        if cell.value is None or cell.value == '':  # 如果正班时间为空
                            # 设置红色字体
                            cell.font = red_font
                            
                            # 同时标记原始打卡记录单元格字体为红色
                            original_row = row - 1  # 原始打卡记录在正班行的上一行
                            original_cell = ws.cell(row=original_row, column=col_idx)
                            original_cell.font = red_font
        
        # 保存修改后的文件
        wb.save(output_file)
        
        print(f"处理完成，结果已保存至: {output_file}")
        
    except Exception as e:
        print(f"处理出错: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    process_attendance()