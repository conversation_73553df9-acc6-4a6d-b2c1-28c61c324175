import pandas as pd
import os
from datetime import datetime, timedelta
import re
from openpyxl import load_workbook

def clean_time_cell(cell):
    """
    解析并清理单元格中的时间条目
    """
    if not isinstance(cell, str):
        return []
    
    # 使用正则表达式提取所有时间条目 (格式: HH:MM)
    time_pattern = r'\d{2}:\d{2}'
    times = re.findall(time_pattern, cell)
    
    # 转换为 (时间, 设备) 元组列表 - 设备为空，因为数据中没有提供
    return [(f"{t}:00", "") for t in times]

def calculate_time_difference(time1, time2):
    """
    计算两个时间之间的差值 (以分钟为单位)
    """
    if not time1 or not time2:
        return 0
    
    h1, m1, s1 = map(int, time1.split(':'))
    h2, m2, s2 = map(int, time2.split(':'))
    
    return abs((h2 * 60 + m2) - (h1 * 60 + m1))

def get_best_punch_time_hourly(all_punch_times, time_range, is_earliest):
    """
    计时人员的打卡时间选择
    """
    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    start_minutes = start_hour * 60 + start_minute
    end_minutes = end_hour * 60 + end_minute
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        current_minutes = hour * 60 + minute

        if start_minutes <= current_minutes <= end_minutes:
            filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    return min(filtered_times, key=lambda x: x[0]) if is_earliest else max(filtered_times, key=lambda x: x[0])

def get_best_punch_time_night(all_punch_times, time_range, is_earliest):
    """
    夜班的打卡时间选择
    """
    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        
        # 处理跨日的情况
        if start_hour > end_hour:  # 例如23:00-01:00
            if (hour >= start_hour) or (hour <= end_hour):
                filtered_times.append((time_part, device))
        else:  # 正常情况，如06:00-09:00
            if start_hour <= hour <= end_hour:
                filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    # 创建一个排序键函数，正确处理跨午夜的时间
    def get_sortable_time(time_str):
        hour = int(time_str[:2])
        minute = int(time_str[3:5])
        second = int(time_str[6:8]) if len(time_str) >= 8 else 0
        
        # 对于23:00-01:00这样的跨午夜范围的特殊处理
        if start_hour > end_hour:
            # 如果是寻找最早时间，我们希望23点时间排在0点前面
            if is_earliest:
                # 23点时间保持较小值
                if hour >= start_hour:
                    return hour * 3600 + minute * 60 + second
                # 0-1点时间给一个很大的值，确保它们排在后面
                elif hour <= end_hour:
                    return (24 + hour) * 3600 + minute * 60 + second
            # 如果是寻找最晚时间，我们希望23点时间排在0点后面
            else:
                if hour >= start_hour:
                    return (24 + hour) * 3600 + minute * 60 + second
                # 0-1点时间保持原值
                elif hour <= end_hour:
                    return hour * 3600 + minute * 60 + second
        
        return hour * 3600 + minute * 60 + second
    
    # 根据排序键函数排序
    if is_earliest:
        return min(filtered_times, key=lambda x: get_sortable_time(x[0]))
    else:
        return max(filtered_times, key=lambda x: get_sortable_time(x[0]))

def get_best_punch_time(all_punch_times, target_time, is_earliest):
    """
    从打卡时间列表中选择最合适的时间（月薪人员）
    """
    target_hour, target_minute = target_time
    filtered_times = []
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        current_minutes = hour * 60 + minute
        target_minutes = target_hour * 60 + target_minute

        if target_hour == 9:  # 9:00 - 取9点之前的打卡记录
            if hour <= 9:
                filtered_times.append((time_part, device))
        elif target_hour == 11:  # 11:00-13:00
            if 11 <= hour <= 13:
                filtered_times.append((time_part, device))
        elif target_hour == 12:  # 12:00-14:00
            if 12 <= hour <= 14:
                filtered_times.append((time_part, device))
        elif target_hour == 17 and target_minute == 30:  # 17:30
            if hour >= 17:
                filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    return min(filtered_times, key=lambda x: x[0]) if is_earliest else max(filtered_times, key=lambda x: x[0])

def process_night_shift_record(punch_times):
    """
    处理夜班打卡记录
    """
    # 获取各个时间段的打卡记录
    # 打卡时间4：23:00-01:00 最早一次打卡记录
    punch_early_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), True)
    # 打卡时间5：23:00-01:00 最晚一次打卡记录
    punch_late_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), False)
    # 早上打卡：6:00-9:00 最晚一次打卡记录
    punch_morning = get_best_punch_time_night(punch_times, (6, 0, 9, 0), False)
    # 晚上打卡：17:00-22:00 最早一次打卡记录
    punch_evening = get_best_punch_time_night(punch_times, (17, 0, 22, 0), True)
    
    # 检查状态
    all_status_normal = True
    
    # 检查各时间点的状态
    if not punch_evening or (punch_evening and int(punch_evening[0][:2]) >= 20):
        all_status_normal = False
    
    if not punch_early_night:
        all_status_normal = False
    
    if not punch_late_night:
        all_status_normal = False
    
    # 计算工时
    if all_status_normal:
        return '8', '3.5'
    else:
        return '', ''

def process_monthly_record(category, punch_times):
    """
    处理月薪人员打卡记录（包含经理、中班和普通月薪逻辑）
    """
    # 中班处理逻辑
    if category == '中班':
        # 打卡时间1：11:00-13:00 取最早，需在12:00前
        punch1 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
        status1 = '正常' if punch1 and punch1[0] <= '12:00:00' else '迟到' if punch1 else '缺卡'

        # 打卡时间2：17:00-18:00 取最早，需在17:30后
        punch2 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), True)
        status2 = '正常' if punch2 and punch2[0] >= '17:00:00' else '早退' if punch2 else '缺卡'

        # 打卡时间3：17:00-18:00 取最晚
        punch3 = get_best_punch_time_hourly(punch_times, (17, 0, 19, 0), False)
        status3 = '正常' if punch3 and punch3[0] <= '18:00:00' else '迟到' if punch3 else '缺卡'
        if punch3 and punch2 and punch3[0] == punch2[0]:
            status3 = '缺卡'

        # 打卡时间4：23:00-01:00（跨日）取最晚
        night_punches = [(time_part, device) for time_part, device in punch_times 
                        if (time_part >= '23:00:00' or time_part <= '01:00:00')]
        punch4 = max(night_punches, key=lambda x: x[0]) if night_punches else None
        
        hour = int(punch4[0][:2]) if punch4 else -1
        status4 = '正常' if hour == 0 or hour == 1 else '早退' if punch4 else '缺卡'

        all_status_normal = (status1 == '正常' and status2 == '正常' and 
                            status3 == '正常' and status4 == '正常')
        
        if all_status_normal:
            return '8', '3.5'
        else:
            return '', ''

    # 经理处理逻辑
    elif category in ['经理', '保洁', '帮厨']:
        # 打卡时间1：全天范围取最早，需在8:00前
        punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 12, 0), True)
        status1 = '正常' if punch1 and punch1[0] <= '08:01:00' else '迟到' if punch1 else '缺卡'

        # 打卡时间4：16:00-23:59取最晚，需在17:30后
        punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 23, 59), False)
        status4 = '正常' if punch4 and punch4[0] >= '17:30:00' else '早退' if punch4 else '缺卡'

        # 经理工时计算
        if status1 == '正常' and status4 == '正常':
            return '8', '0'
        else:
            return '', ''

    # 普通月薪处理逻辑
    elif category == '月薪':
        # 定义四个关键时间点：(目标时间, 是否取最早)
        time_points = [
            ((9, 0), True),     # 打卡时间1: 9:00前最早
            ((11, 0), True),    # 打卡时间2: 11:00-13:00最早
            ((12, 0), False),   # 打卡时间3: 12:00-14:00最晚
            ((17, 30), False)   # 打卡时间4: 17:30后最晚
        ]

        all_status_normal = True
        for i, (target_time, is_earliest) in enumerate(time_points, 1):
            if i == 2:  # 打卡时间2
                best_punch = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), is_earliest)
            elif i == 3:  # 打卡时间3
                best_punch = get_best_punch_time_hourly(punch_times, (12, 0, 14, 0), is_earliest)
            else:
                best_punch = get_best_punch_time(punch_times, target_time, is_earliest)
            
            if best_punch:
                time_part = best_punch[0]
                
                if i == 1:  # 早上
                    if time_part > '08:01:00':
                        all_status_normal = False
                elif i == 2:  # 中午
                    if not('11:00:00' <= time_part <= '13:00:00'):
                        all_status_normal = False
                elif i == 3:  # 下午
                    if time_part > '14:00:00':
                        all_status_normal = False
                elif i == 4:  # 晚上
                    if time_part < '17:30:00':
                        all_status_normal = False
            else:
                all_status_normal = False

        # 月薪工时计算
        if all_status_normal:
            return '8', '0'
        else:
            return '', ''
            
    return '', ''

def process_hourly_record(punch_times):
    """
    处理计时人员打卡记录（白班）
    """
    time_rules = [
        ((0, 0, 9, 0), True),      # 打卡时间1: 9点之前最早
        ((11, 30, 13, 30), True),  # 打卡时间2: 11:30-13:30最早
        ((11, 30, 13, 30), False), # 打卡时间3: 11:30-13:30最晚
        ((16, 0, 18, 0), True),    # 打卡时间4: 16:00-18:00最早
        ((17, 0, 18, 0), False),   # 打卡时间5: 17:00-18:00最晚
        ((20, 0, 23, 59), False)   # 打卡时间6: 20:00后最晚
    ]

    punch_records = []
    all_status_normal = True
    
    for i, (time_range, is_earliest) in enumerate(time_rules, 1):
        best_punch = get_best_punch_time_hourly(punch_times, time_range, is_earliest)
        if best_punch:
            time_part, device = best_punch
            punch_records.append(time_part)
            
            if i == 3:
                if len(punch_records) >= 2 and punch_records[1] and punch_records[1] <= '12:00:00':
                    # 如果打卡时间2在12点之前，打卡时间3需在打卡时间2后30分钟内
                    time_diff = calculate_time_difference(punch_records[1], time_part)
                    if time_diff > 30:
                        all_status_normal = False
                else:
                    # 如果打卡时间2在12点之后，打卡时间3需在13:30之前
                    if time_part > '13:31:00':
                        all_status_normal = False
            elif i == 5:
                if len(punch_records) >= 5 and punch_records[3] == time_part:
                    break
                elif len(punch_records) >= 4 and punch_records[3]:
                    time_diff = calculate_time_difference(punch_records[3], time_part)
                    if time_diff > 30:
                        all_status_normal = False
        else:
            if i <= 4:  # 前四个打卡时间是必须的
                all_status_normal = False
            punch_records.append('')

    if all_status_normal:
        # 计算加班时间
        overtime_hours = 0
        
        # 规则1: 打卡时间2和打卡时间3相差30分钟记为1小时加班
        if len(punch_records) >= 3 and punch_records[1] and punch_records[2]:
            time_diff = calculate_time_difference(punch_records[1], punch_records[2])
            if time_diff <= 30:
                overtime_hours = 1
        
        # 规则2: 存在打卡时间5和6则加班总时长为3小时
        if len(punch_records) >= 6 and punch_records[4] and punch_records[5]:
            overtime_hours = 3
            
        return '8', str(overtime_hours) if overtime_hours > 0 else ''
    else:
        return '', ''

def process_white_ten_record(punch_times):
    """
    处理白十打卡记录
    """
    # 打卡时间1：9:00-11:00 取最早，需在10:00前
    punch1 = get_best_punch_time_hourly(punch_times, (9, 0, 11, 0), True)
    status1 = '正常' if punch1 and punch1[0] <= '10:01:00' else '迟到' if punch1 else '缺卡'

    # 打卡时间2：11:00-13:00 取最早
    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
    status2 = '正常' if punch2 and punch2[0] >= '11:30:00' else '早退' if punch2 else '缺卡'

    # 打卡时间3：11:30-13:00 取最晚
    punch3 = get_best_punch_time_hourly(punch_times, (11, 30, 13, 0), False)
    if punch3 and punch2:
        time_diff = calculate_time_difference(punch2[0], punch3[0])
        status3 = '正常' if time_diff <= 30 else '迟到'
    else:
        status3 = '缺卡'

    # 打卡时间4：16:00-18:00 取最早
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    status4 = '正常' if punch4 and punch4[0] >= '17:00:00' else '早退' if punch4 else '缺卡'

    # 打卡时间5：17:00-18:00 取最晚
    punch5 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), False)
    if punch5 and punch4:
        time_diff = calculate_time_difference(punch4[0], punch5[0])
        status5 = '正常' if time_diff <= 30 else '迟到'
    else:
        status5 = '缺卡'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 24, 0), False)
    status6 = '正常' if punch6 and punch6[0] >= '19:00:00' else '早退' if punch6 else '缺卡'

    # 工时计算
    all_status_normal = (status1 == '正常' and status2 == '正常' and 
                        status3 == '正常' and status4 == '正常' and 
                        status5 == '正常' and status6 == '正常')
    
    if all_status_normal:
        # 加班计算: 打卡时间6减去19:00再加上0.5小时
        overtime_hours = 0.5  # 默认值
        if punch6:
            try:
                # 将打卡时间6和19:00转换为datetime对象
                punch_time = datetime.strptime(punch6[0], '%H:%M:%S')
                base_time = datetime.strptime('19:00:00', '%H:%M:%S')
                
                if punch_time >= base_time:
                    # 计算时间差(小时)
                    time_diff = (punch_time - base_time).total_seconds() / 3600
                    # 加上0.5小时
                    overtime_hours = round(time_diff + 0.5, 1)
            except Exception as e:
                print(f"计算白班十点加班时间出错: {e}")
                
        return '8', str(overtime_hours)
    else:
        return '', ''

def process_security_record(punch_times):
    """
    处理保安打卡记录
    """
    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    status1 = '正常' if punch1 and punch1[0] <= '07:01:00' else '迟到' if punch1 else '缺卡'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 21, 0), False)
    status6 = '正常' if punch6 and punch6[0] >= '19:00:00' else '早退' if punch6 else '缺卡'

    # 工时计算
    if status1 == '正常' and status6 == '正常':
        return '12', ''
    else:
        return '', ''

def process_b_security_record(punch_times):
    """
    处理夜班保安打卡记录
    """
    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    status1 = '正常' if punch1 and punch1[0] >= '07:00:00' else '早退' if punch1 else '缺卡'

    # 打卡时间6：19:00-21:00 取最早
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 21, 0), True)
    status6 = '正常' if punch6 and punch6[0] <= '19:00:00' else '迟到' if punch6 else '缺卡'

    # 工时计算
    if status1 == '正常' and status6 == '正常':
        return '12', ''
    else:
        return '', ''

def process_bai_jiu_record(punch_times):
    """
    处理白九打卡记录
    """
    # 打卡时间1取值为8点到10点的最早的打卡时间
    punch1 = get_best_punch_time_hourly(punch_times, (8, 0, 10, 0), True)
    status1 = '正常' if punch1 and punch1[0] <= '09:00:00' else '迟到' if punch1 else '缺卡'

    # 打卡时间2取值为11点到13点的最早的打卡时间
    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
    status2 = '正常' if punch2 and punch2[0] > '12:00:00' else '早退' if punch2 else '缺卡'

    # 打卡时间3取值为11点到13点的最晚的打卡时间
    punch3 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), False)
    if punch3 and punch2:
        if punch3[0] == punch2[0]:
            status3 = '缺卡'
        else:
            time_diff = calculate_time_difference(punch2[0], punch3[0])
            status3 = '正常' if time_diff <= 30 else '迟到'
    else:
        status3 = '缺卡'

    # 打卡时间4取值为16点到18点的最早的打卡时间
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    status4 = '正常' if punch4 and punch4[0] > '17:30:00' else '早退' if punch4 else '缺卡'

    # 打卡时间5取值为16点到18点的最晚的打卡时间
    punch5 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), False)
    if punch5:
        if punch4 and punch5[0] == punch4[0]:
            status5 = '异常'
        elif punch4:
            time_diff = calculate_time_difference(punch4[0], punch5[0])
            status5 = '正常' if time_diff <= 30 else '迟到'
        else:
            status5 = '缺卡'
    else:
        status5 = '缺卡'

    # 打卡时间6 取值为19点到22点的最晚的打卡时间
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 22, 0), False)
    status6 = '正常' if punch6 else '缺卡'

    # 若是打卡时间5不存在 且打卡时间6不存在 则打卡时间5和打卡时间6的状态记为正常
    if not punch5 and not punch6:
        status5 = '正常'
        status6 = '正常'
        
    # 状态全为正常的话 正班为8小时
    all_status_normal = (status1 == '正常' and status2 == '正常' and 
                        status3 == '正常' and status4 == '正常' and
                        status5 == '正常' and status6 == '正常')
    
    if all_status_normal:
        # 计算加班时间
        overtime_hours = 0
        
        # 规则1: 打卡时间2和打卡时间3相差30分钟记为1小时加班
        if punch2 and punch3:
            time_diff = calculate_time_difference(punch2[0], punch3[0])
            if time_diff <= 30:
                overtime_hours = 1
        
        # 规则2: 存在打卡时间5和6的 加班总时长为打卡时间6减去打卡时间5
        if punch5 and punch6:
            try:
                # 将时间字符串转换为datetime对象进行计算
                time5 = datetime.strptime(punch5[0], '%H:%M:%S')
                time6 = datetime.strptime(punch6[0], '%H:%M:%S')
                # 如果跨越午夜，需要调整时间
                if time6 < time5:
                    time6 += timedelta(days=1)
                
                time_diff = time6 - time5
                # 计算小时差，保留一位小数
                overtime_hours = round(time_diff.total_seconds() / 3600, 1)
            except Exception as e:
                print(f"计算加班时间出错: {e}")
        
        return '8', str(overtime_hours) if overtime_hours > 0 else ''
    else:
        return '', ''

def process_no_punch_record():
    """
    处理无需打卡组别的记录
    """
    return '8', '0'

def process_attendance():
    """
    主函数，处理考勤数据
    """
    try:
        # 定义文件路径
        origin_file = r'D:\attendance\month\6\origin.xlsx'
        schedule_file = r'D:\attendance\month\6\schedule\16.xlsx'
        
        # 检查文件是否存在
        if not os.path.exists(origin_file):
            print(f"打卡记录文件不存在: {origin_file}")
            return
        if not os.path.exists(schedule_file):
            print(f"排班文件不存在: {schedule_file}")
            return
        
        # 读取排班文件获取员工类别信息
        df_schedule = pd.read_excel(schedule_file)
        employee_category = dict(zip(df_schedule['姓名'], df_schedule['工号']))  # 使用工号列表示员工类别
        
        # 使用 openpyxl 加载工作簿以进行修改
        workbook = load_workbook(origin_file)
        sheet = workbook.active
        
        # 检查是否已经有正班和加班列，如果没有则添加
        has_regular_column = False
        has_overtime_column = False
        last_column = sheet.max_column
        
        # 获取最后一列和倒数第二列的列标题
        last_col_header = sheet.cell(row=1, column=last_column).value
        second_last_col_header = sheet.cell(row=1, column=last_column-1).value if last_column > 1 else None
        
        if last_col_header == '加班':
            has_overtime_column = True
            has_regular_column = second_last_col_header == '正班'
            overtime_col = last_column
            regular_col = last_column - 1 if has_regular_column else None
        elif last_col_header == '正班':
            has_regular_column = True
            regular_col = last_column
            
        # 如果需要，添加正班列
        if not has_regular_column:
            regular_col = last_column + 1
            sheet.cell(row=1, column=regular_col).value = '正班'
            
        # 如果需要，添加加班列
        if not has_overtime_column:
            overtime_col = regular_col + 1
            sheet.cell(row=1, column=overtime_col).value = '加班'
        
        # 遍历每一行从第2行开始
        for i in range(2, sheet.max_row + 1):
            # 获取当前员工信息
            name = sheet.cell(row=i, column=1).value  # 姓名列
            punch_data = sheet.cell(row=i, column=19).value  # S列数据
            
            if not name or not punch_data:
                continue
                
            # 获取员工类别 - 从排班表中查询
            category = '白班'  # 默认类别
            for index, row in df_schedule.iterrows():
                if row['姓名'] == name:
                    category = row['工种']  # 假设工种列表示员工类别
                    break
            
            # 解析打卡数据
            punch_times = clean_time_cell(punch_data)
            
            # 根据员工类别应用不同的处理函数
            regular_hours, overtime_hours = '', ''
            
            if category == '夜班':
                regular_hours, overtime_hours = process_night_shift_record(punch_times)
            elif category in ['月薪', '经理', '保洁', '帮厨', '中班']:
                regular_hours, overtime_hours = process_monthly_record(category, punch_times)
            elif category == '白班':
                regular_hours, overtime_hours = process_hourly_record(punch_times)
            elif category == '白班十点':
                regular_hours, overtime_hours = process_white_ten_record(punch_times)
            elif category == '保安':
                regular_hours, overtime_hours = process_security_record(punch_times)
            elif category == '夜班保安':
                regular_hours, overtime_hours = process_b_security_record(punch_times)
            elif category == '白班九点':
                regular_hours, overtime_hours = process_bai_jiu_record(punch_times)
            elif category == '无需打卡':
                regular_hours, overtime_hours = process_no_punch_record()
            
            # 更新正班和加班时间
            if regular_hours:
                sheet.cell(row=i, column=regular_col).value = regular_hours
            if overtime_hours:
                sheet.cell(row=i, column=overtime_col).value = overtime_hours
        
        # 保存修改后的文件
        output_file = r'D:\attendance\month\6\origin_processed.xlsx'
        workbook.save(output_file)
        print(f"处理完成，结果已保存至: {output_file}")
        
    except Exception as e:
        print(f"处理出错: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    process_attendance() 