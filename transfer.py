import openpyxl
from openpyxl import load_workbook

def replace_values(value):
    try:
        num = float(value)
    except (ValueError, TypeError):
        return value  # 如果不是数字，保持原样
    
    if 2.1 <= num <= 2.4:
        return 2
    elif 2.5 <= num <= 2.9:
        return 2.5
    elif 3.1<=num <= 3.4:
        return 3
    elif 3.5 <= num <= 3.9:
        return 3.5
    elif 4.1 <= num <= 4.4:
        return 4
    elif 4.5 <= num <= 4.9:
        return 4.5
    elif 5.0 <= num <= 5.4:
        return 5
    elif 5.5 <= num <= 5.9:
        return 5.5
    return num  # 保持其他值不变

def process_excel(input_file, output_file, start_col):
    # 加载工作簿
    wb = load_workbook(input_file)
    
    for sheet in wb.worksheets:
        # 遍历每一行，从第3行开始
        for row in sheet.iter_rows(min_row=3):
            # 遍历每一列，从指定列开始
            for cell in row[start_col-1:]:  # openpyxl列索引从1开始，但列表索引从0开始
                if cell.value is not None:
                    cell.value = replace_values(cell.value)
    
    # 保存修改后的工作簿
    wb.save(output_file)

# 使用你的文件路径
file_path = r'd:\attendance\month\all.xlsx'
output_path = r'd:\attendance\month\all_processed.xlsx'  # 输出文件路径
start_column = 13  # 从第3列开始处理 (N=3)

process_excel(file_path, output_path, start_column)
print(f"处理完成！结果已保存到: {output_path}")