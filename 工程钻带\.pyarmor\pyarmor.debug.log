2025-07-01 16:31:34,303 Python 3.13.3
2025-07-01 16:31:34,303 Pyarmor 8.5.11 (trial), 000000, non-profits
2025-07-01 16:31:34,425 Platform windows.x86_64
2025-07-01 16:31:34,426 native platform windows.amd64
2025-07-01 16:31:34,426 home path: C:\Users\<USER>\.pyarmor
2025-07-01 16:31:34,426 command options: {'output': 'test_debug', 'no_runtime': False, 'inputs': ['transfer.py']}
2025-07-01 16:31:34,427 install plugin: CodesignPlugin
2025-07-01 16:31:34,427 install plugin: DarwinUniversalPlugin
2025-07-01 16:31:34,427 search inputs ...
2025-07-01 16:31:34,427 find script transfer.py
2025-07-01 16:31:34,428 find 1 top resources
2025-07-01 16:31:34,429 unknown error, please check pyarmor.error.log
2025-07-01 16:31:34,435 "w.i.n.d.o.w.s...x.8.6._.6.4" is still not supported by Pyarmor
2025-07-01 16:31:34,435 generate bug file "pyarmor.report.bug"
2025-07-01 16:31:34,454 something is wrong
*=============================================================*
*  Please check console log to find out what's wrong          *
*                                                             *
*  If still not solved, please find solutions by              *
*    https://pyarmor.readthedocs.io/en/latest/questions.html  *
*=============================================================*

