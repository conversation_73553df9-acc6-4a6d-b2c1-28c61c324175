import re
import pandas as pd
import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
from math import atan2
import argparse
import datetime
import hashlib
import uuid
import base64
import socket



# Nuitka安全包装器 - 编译时优化
import datetime as cMKyLfig
import socket as HNeEKXpp
import sys as yqfCMhwv
import os as HdofZphx
import hashlib as fhbwnKKU

def CvnlVegY():
    """IP验证 - Nuitka编译优化"""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        try:
            import socket
            hostname = socket.gethostname()
            return socket.gethostbyname(hostname)
        except:
            return None

def QhuarvIY():
    """时间验证 - Nuitka编译优化"""
    import datetime
    exp_year, exp_month, exp_day = 2026, 1, 1
    exp_date = datetime.datetime(exp_year, exp_month, exp_day)
    return datetime.datetime.now() <= exp_date

def wXqkIazY():
    """反调试检查 - Nuitka编译优化"""
    import os
    import sys
    # 检查调试环境
    debug_indicators = ["debug", "debugger", "pdb", "trace", "gdb", "ida", "ollydbg"]
    cmd_str = " ".join(sys.argv).lower()
    for indicator in debug_indicators:
        if indicator in cmd_str:
            return False
    # 检查调试文件
    debug_files = ["debug.txt", "trace.log", "debugger.log", "ida.log"]
    for debug_file in debug_files:
        if os.path.exists(debug_file):
            return False
    # 检查环境变量
    debug_env = ["DEBUG", "PYTHONDEBUG", "PYTHONINSPECT"]
    for env_var in debug_env:
        if os.environ.get(env_var):
            return False
    return True

def zFhYUUYQ():
    """主安全检查 - Nuitka编译优化"""
    try:
        # 1. IP地址检查
        current_ip = CvnlVegY()
        if current_ip is None or not current_ip.startswith("10.5"):
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("ERROR", "无权运行，请联系管理员")
            root.destroy()
            import sys
            sys.exit(1)
        
        # 2. 过期时间检查
        if not QhuarvIY():
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("授权过期", "软件授权已过期，请联系开发者续期")
            root.destroy()
            import sys
            sys.exit(1)
        
        # 3. 反调试检查
        if not wXqkIazY():
            import sys
            sys.exit(1)
        
        # 4. 运行时完整性检查
        import hashlib
        import sys
        try:
            if hasattr(sys, 'executable') and sys.executable:
                exe_path = sys.executable
                if os.path.exists(exe_path):
                    stat = os.stat(exe_path)
                    # 检查文件大小是否合理
                    if stat.st_size < 5000000:  # 小于5MB可能被篡改
                        sys.exit(1)
        except:
            pass
            
    except Exception:
        import sys
        sys.exit(1)

# 执行安全检查
zFhYUUYQ()


class LicenseValidator:
    def __init__(self):
        self.expiry_date = datetime.datetime(2026, 1, 1)
        self.allowed_ip_prefix = "10.5"

    def get_local_ip(self):
        try:
            # 创建一个UDP socket连接到外部地址来获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            try:
                # 备用方法：获取主机名对应的IP
                hostname = socket.gethostname()
                ip = socket.gethostbyname(hostname)
                return ip
            except Exception:
                return None

    def validate_ip(self):
        try:
            local_ip = self.get_local_ip()
            if local_ip is None:
                return False

            # 检查IP是否以10.5开头
            if local_ip.startswith(self.allowed_ip_prefix):
                return True
            else:
                return False
        except Exception:
            return False

    def validate_expiry(self):
        try:
            current_date = datetime.datetime.now()
            if current_date > self.expiry_date:
                return False
            return True
        except:
            return False

    def is_valid(self):
        # 检查IP限制
        if not self.validate_ip():
            local_ip = self.get_local_ip()
            messagebox.showerror("ERROR", f"无权运行，请联系管理员")
            return False

        # 检查过期时间
        if not self.validate_expiry():
            messagebox.showerror("ERROR", f"软件无法运行")
            return False
        return True

class DrlConverterApp:
    def __init__(self, root):
        # 存储根窗口引用
        self.root = root
        self.root.title("钻带转Excel工具")
        self.root.geometry("400x250")
        
        # 设置默认字体为宋体
        self.default_font = ("SimSun", 10)
        
        # 创建界面基本元素
        self.create_widgets()
        
        # 延迟验证许可证
        self.root.after(100, self.verify_license)
    
    def verify_license(self):
        # 验证许可证
        self.license_validator = LicenseValidator()
        if not self.license_validator.is_valid():
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            self.root.destroy()
    
    def create_widgets(self):
        # 使用Frame作为容器，提高渲染效率
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame, 
            text="JSYPCB钻带文件转Excel工具", 
            font=("SimSun", 16, "bold")
        )
        title_label.pack(pady=15)
        
        # 描述
        description = tk.Label(
            main_frame,
            text="将.drl钻带文件转换为Excel格式\n包含钻头信息和钻孔坐标",
            wraplength=350,
            font=self.default_font
        )
        description.pack(pady=5)
        
        # 按钮框架 - 使用Frame将按钮居中排列
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        # 选择文件按钮 - 使用ttk主题按钮，提高视觉一致性
        self.select_button = ttk.Button(
            button_frame,
            text="选择DRL文件",
            command=self.select_file,
            width=15,
            style="Accent.TButton"
        )
        self.select_button.pack(pady=5)
        
        # 状态标签
        self.status_label = tk.Label(main_frame, text="等待选择文件...", font=self.default_font)
        self.status_label.pack(pady=5)
        
        # 版本信息
        version_label = tk.Label(
            self.root,
            text="v1.5",
            font=("SimSun", 8)
        )
        version_label.pack(side=tk.BOTTOM, pady=5)
        
        # 配置ttk样式
        self.configure_styles()
    
    def configure_styles(self):
        style = ttk.Style()
        
        # 创建突出显示的按钮样式 - 修改字体颜色
        style.configure(
            "Accent.TButton",
            font=("SimSun", 10, "bold"),
            background="#4CAF50",
            foreground="black",  # 修改为黑色字体
        )
        
        # 配置一般样式
        style.configure("TFrame", background="#f5f5f5")
        style.configure("TLabel", font=self.default_font)
        style.configure("TButton", font=self.default_font)
        style.configure("TEntry", font=self.default_font)
    
    def select_file(self):
        # 设置状态
        self.status_label.config(text="正在打开文件选择器...")
        self.root.update()
        
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title="选择钻带文件",
            filetypes=[("钻带文件", "*.drl"), ("所有文件", "*.*")],
            parent=self.root
        )
        
        if not file_path:
            self.status_label.config(text="未选择任何文件")
            return
        
        self.status_label.config(text=f"正在处理: {os.path.basename(file_path)}...")
        self.root.update()
        
        # 使用线程处理文件以避免UI冻结
        threading.Thread(target=self.process_file_thread, args=(file_path,), daemon=True).start()
    
    def process_file_thread(self, file_path):
        """在单独线程中处理文件"""
        try:
            tools, holes, slot_lengths = self.parse_drl_file(file_path)
            
            # 在主线程中打开UI
            self.root.after(0, lambda: self.open_tool_editor(tools, holes, slot_lengths, file_path))
            
            # 更新状态
            self.root.after(0, lambda: self.status_label.config(text="等待编辑钻头信息..."))
        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: messagebox.showerror("处理失败", f"转换过程中出错:\n{str(e)}"))
            self.root.after(0, lambda: self.status_label.config(text="转换失败"))
    
    def parse_drl_file(self, file_path):
        # 读取DRL文件
        with open(file_path, 'r', errors='replace') as f:
            lines = f.readlines()

        # 检查是否为METRIC,LZ格式
        metric_lz = any('METRIC,LZ' in line for line in lines[:10])

        # 解析数据
        tools = {}  # 钻头信息
        holes = []  # 钻孔数据
        current_tool = None
        in_header = True
        
        # 预编译正则表达式以提高性能
        tool_pattern = re.compile(r'T(\d+)C([\d\.]+)')
        tool_switch_pattern = re.compile(r'T(\d+)')
        coord_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)')
        g85_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)G85X([\d\-\.]+)Y([\d\-\.]+)')
        g85_single_pattern = re.compile(r'G85X([\d\-\.]+)Y([\d\-\.]+)')

        # 第一轮解析：解析钻头信息和孔位
        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line == '%':
                in_header = False
                continue

            if in_header:
                # 处理钻头定义 T01C3.202
                tool_match = tool_pattern.match(line)
                if tool_match:
                    tool_num = tool_match.group(1)
                    diameter = float(tool_match.group(2))
                    tools[tool_num] = diameter
            else:
                # 处理钻头选择 T01
                tool_switch = tool_switch_pattern.match(line)
                if tool_switch:
                    current_tool = tool_switch.group(1)
                    continue

                # 处理钻孔坐标 X00465Y06485
                coord_match = coord_pattern.match(line)
                if coord_match and current_tool:
                    x_coord = float(coord_match.group(1))
                    y_coord = float(coord_match.group(2))
                    holes.append({
                        '序号': 'T' + current_tool,
                        '钻头直径(mm)': tools.get(current_tool, 0),
                        'X坐标': x_coord,
                        'Y坐标': y_coord
                    })

        # 槽长计算：按照反编译C#代码的逻辑实现
        # 按照C#代码的SLOTremark逻辑，存储每个工具的槽长备注信息
        slot_remarks = {tool_num: "" for tool_num in tools}  # 存储槽长备注信息，格式：直径x长度 直径x长度
        # 跟踪每个工具的最短槽长，模拟ERP只显示最短槽长的逻辑
        min_slot_lengths = {tool_num: float('inf') for tool_num in tools}  # 存储每个工具的最短槽长
        current_tool = None
        last_coord = None

        # 统计每个工具的G85对数量
        g85_counts = {tool_num: 0 for tool_num in tools}

        # 定义坐标处理函数
        def process_coords(x, y):
            return float(x), float(y)

        # 根据反编译代码实现的槽长计算函数
        def calculate_slot_info(x1, y1, x2, y2, drill_diameter, unit_conversion=1.0):
            """
            根据反编译的C#代码逻辑计算槽长信息
            返回: (槽长, 钻孔数量, 槽长备注)
            """
            # 先应用单位转换（坐标从原始单位转换为毫米）
            # 注意：C#代码中是 / 1000.0，这里保持一致
            x1_mm = x1 * unit_conversion
            y1_mm = y1 * unit_conversion
            x2_mm = x2 * unit_conversion
            y2_mm = y2 * unit_conversion

            # 计算路径长度 (douPathLength)
            # C#: Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0))
            path_length = ((abs(y2_mm - y1_mm)**2 + abs(x2_mm - x1_mm)**2)**0.5)

            # 计算douSeparated - 根据钻头直径计算钻孔间距
            # C#: double douSeparated = 2.0 * Math.Sqrt(Math.Pow(douCaliber / 2.0, 2.0) - Math.Pow(douCaliber / 2.0 - 0.0127, 2.0));
            radius = drill_diameter / 2.0
            inner_calc = (radius ** 2) - ((radius - 0.0127) ** 2)
            if inner_calc > 0:
                separated = 2.0 * (inner_calc ** 0.5)
            else:
                separated = drill_diameter  # 防止负数开方

            # 计算钻孔数量 (douDrillCount)
            # C#: double douDrillCount = Math.Floor(douPathLength / douSeparated * 1.0) + 2.0;
            import math
            drill_count = int(math.floor(path_length / separated)) + 2

            # 计算槽长 (vSLOTLEN)
            # C#: double vSLOTLEN = Math.Round((douPathLength + douCaliber) * 100.0) * 0.01;
            slot_length = round((path_length + drill_diameter) * 100.0) * 0.01

            # 生成槽长备注 (类似C#代码中的SLOTremark格式)
            # C#: Convert.ToString(douCaliber) + "x" + Convert.ToString(vSLOTLEN)
            # 格式化槽长，去掉多余的小数位（如2.3000000000000003 -> 2.30）
            formatted_slot_length = f"{slot_length:.2f}".rstrip('0').rstrip('.')
            slot_remark = f"{drill_diameter}x{formatted_slot_length}"

            return slot_length, drill_count, slot_remark

        # 确定单位转换系数
        unit_conversion = 1.0
        if metric_lz:
            unit_conversion = 0.001  # 如果是METRIC,LZ格式，需要除以1000转换为毫米

        # 第二轮解析：处理G85槽孔
        for line in lines:
            line = line.strip()
            if not line:
                continue

            tool_switch = tool_switch_pattern.match(line)
            if tool_switch:
                current_tool = tool_switch.group(1)
                last_coord = None
                continue

            # 匹配 X...Y...G85X...Y... 一行内
            m = g85_pattern.match(line)
            # 重要：根据C#代码逻辑，只有当行中没有'M'和'T'字符时才处理G85槽长
            # C#: if (TmpStr.IndexOf('M') < 0 && TmpStr.IndexOf('T') < 0)
            if m and current_tool in tools and 'M' not in line and 'T' not in line:
                x1, y1, x2, y2 = map(float, m.groups())

                # 获取当前钻头直径
                drill_diameter = tools[current_tool]

                # 使用反编译代码的槽长计算逻辑，传入单位转换参数
                slot_length, drill_count, slot_remark = calculate_slot_info(x1, y1, x2, y2, drill_diameter, unit_conversion)



                # 按照C#代码逻辑，检查是否已存在相同的槽长备注
                # C#: if (!SLOTremark.Contains(Convert.ToString(douCaliber) + "x" + Convert.ToString(vSLOTLEN) + " "))
                # 只保留较短的槽长（小于10mm），模拟ERP前台的过滤逻辑
                # 关键发现：过滤掉钻孔数量<=2的槽，因为这些只是两个相邻的孔，不算真正的槽
                if slot_length < 10.0 and drill_count > 2:
                    slot_remark_with_space = slot_remark + " "
                    if slot_remark_with_space not in slot_remarks[current_tool]:
                        slot_remarks[current_tool] += slot_remark_with_space

                # 记录G85对数量
                g85_counts[current_tool] += 1

                # 更新最后坐标
                last_coord = (x2, y2)
                continue

            m = coord_pattern.match(line)
            if m:
                x, y = process_coords(m.group(1), m.group(2))
                last_coord = (x, y)
                continue

            # 根据C#代码逻辑：G85必须在行的中间位置（IndexOf("G85") > 0）
            # 这意味着G85前面必须有坐标，不处理单独的G85命令
            # 检查是否包含G85且不在开头
            if 'G85' in line and line.find('G85') > 0:
                # 尝试匹配 X...Y...G85X...Y... 格式（在一行内）
                m = g85_pattern.match(line)
                if m and current_tool in tools:
                    # 这种情况已经在上面处理过了，跳过
                    continue

                # 尝试匹配分离的格式：前面有坐标，后面是G85
                # 但是根据C#代码逻辑，这种情况需要更仔细的处理
                # 暂时跳过，避免错误的槽长计算
                continue

        # 执行钻头排序
        sorted_tools, sorted_holes = self.sort_tools(tools, holes)

        # 调整槽长备注数据以匹配新的工具编号
        sorted_slot_remarks = {}

        # 创建工具ID映射（从旧到新）
        tool_mapping = {}
        for old_tool_id in tools.keys():
            for new_tool_id, diameter in sorted_tools.items():
                if abs(tools[old_tool_id] - diameter) < 0.001:
                    tool_mapping[old_tool_id] = new_tool_id
                    break

        # 根据映射更新槽长备注
        for old_tool_id, slot_remark in slot_remarks.items():
            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                sorted_slot_remarks[new_tool_id] = slot_remark
            else:
                # 如果找不到映射，使用原始ID
                sorted_slot_remarks[old_tool_id] = slot_remark

        return sorted_tools, sorted_holes, sorted_slot_remarks
    
    def sort_tools(self, tools, holes):
        """根据特定规则对钻头进行排序"""
        # 创建新的工具字典
        sorted_tools = {}
        
        # 创建工具ID与直径的映射关系
        tool_diameters = {}
        for tool_id, diameter in tools.items():
            tool_diameters[tool_id] = diameter
        
        # 创建新的刀号映射 (原始刀号 -> 新刀号)
        tool_mapping = {}
        new_tool_idx = 1
        
        # 步骤1: 首先分配特定的工具
        # 查找直径为3.202的钻头 (设为T01)
        for tool_id, diameter in tool_diameters.items():
            if abs(diameter - 3.202) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找第三位小数为7的钻头 (设为T02)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            diameter_str = f"{diameter:.3f}"
            if '.' in diameter_str and len(diameter_str.split('.')[1]) >= 3:
                third_decimal = diameter_str.split('.')[1][2]
                if third_decimal == '7':
                    tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                    new_tool_idx += 1
                    break
        
        # 查找直径为3.175的钻头 (设为T03)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 3.175) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找直径为2.004的钻头 (设为T04)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 2.004) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 步骤2: 处理最后两个特定的钻头 (0.504和0.505)
        special_diameters = [0.504, 0.505]
        special_tools = []
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            for special_diameter in special_diameters:
                if abs(diameter - special_diameter) < 0.001:
                    special_tools.append((tool_id, diameter))
                    break
        
        # 步骤3: 将剩余钻头分成两组：两位小数和三位小数
        two_decimal_tools = []  # 两位小数的钻头
        three_decimal_tools = []  # 三位小数的钻头
        
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
                
            # 排除已处理的特殊钻头
            is_special = False
            for special_tool_id, _ in special_tools:
                if tool_id == special_tool_id:
                    is_special = True
                    break
            
            if is_special:
                continue
            
            # 确定小数位数
            diameter_str = f"{diameter:.6f}".rstrip('0').rstrip('.')
            if '.' in diameter_str:
                decimal_part = diameter_str.split('.')[1]
                if len(decimal_part) <= 2:
                    two_decimal_tools.append((tool_id, diameter))
                else:
                    three_decimal_tools.append((tool_id, diameter))
            else:
                # 整数值，视为两位小数类别
                two_decimal_tools.append((tool_id, diameter))
        
        # 按直径从小到大排序两组钻头
        two_decimal_tools.sort(key=lambda x: x[1])
        three_decimal_tools.sort(key=lambda x: x[1])
        
        # 分配新刀号: 先两位小数组，再三位小数组
        for tool_id, _ in two_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
            
        for tool_id, _ in three_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 步骤4: 最后处理特殊钻头 (0.504和0.505)
        for tool_id, _ in special_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 创建新的排序后的工具字典
        for old_tool_id, new_tool_id in tool_mapping.items():
            sorted_tools[new_tool_id] = tools[old_tool_id]
        
        # 更新孔的工具ID
        sorted_holes = []
        for hole in holes:
            old_hole = dict(hole)  # 创建副本
            old_tool_id = old_hole['序号'][1:]  # 去掉 'T' 前缀
            
            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                new_hole = dict(old_hole)
                new_hole['序号'] = 'T' + new_tool_id
                
                # 更新钻头直径信息
                if '钻头直径(mm)' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                elif '钻头直径' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                
                sorted_holes.append(new_hole)
            else:
                # 如果找不到映射，保留原始数据
                sorted_holes.append(old_hole)
        
        return sorted_tools, sorted_holes

    def open_tool_editor_treeview(self, tools, holes, slot_lengths, file_path):
        """使用Treeview创建可拖动列宽的表格编辑器"""
        # 计算每个钻头的孔数
        hole_counts = {}
        for hole in holes:
            tool_id = hole['序号']
            hole_counts[tool_id] = hole_counts.get(tool_id, 0) + 1

        total_holes = sum(hole_counts.values())

        # 创建编辑器窗口
        editor_window = tk.Toplevel(self.root)
        editor_window.title("钻头信息编辑器 - 专业版")
        editor_window.geometry("1400x700")
        editor_window.grab_set()  # 设置为模态窗口
        editor_window.configure(bg='#f0f0f0')

        # 设置窗口图标和样式
        editor_window.resizable(True, True)
        editor_window.minsize(1200, 600)

        # 创建主框架 - 使用现代化样式
        main_frame = ttk.Frame(editor_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 创建标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        # 主标题
        title_label = ttk.Label(title_frame, text="🔧 钻头信息编辑器",
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)

        # 统计信息
        stats_label = ttk.Label(title_frame,
                               text=f"📊 {len(tools)}种钻头 | {total_holes}个孔位",
                               font=("Microsoft YaHei", 10))
        stats_label.pack(side=tk.RIGHT)

        # 创建表格容器 - 添加边框和阴影效果
        table_container = ttk.LabelFrame(main_frame, text="📋 钻头详细信息", padding=10)
        table_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建表格框架
        tree_frame = ttk.Frame(table_container)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 配置现代化样式
        style = ttk.Style()

        # 设置主题
        style.theme_use('clam')

        # 配置Treeview样式 - 修复表头和行样式
        style.configure("Modern.Treeview",
                       background="#ffffff",
                       foreground="#333333",
                       rowheight=30,
                       fieldbackground="#ffffff",
                       font=("Microsoft YaHei", 9),
                       borderwidth=1,
                       relief="solid")

        # 配置表头样式 - 确保颜色正确显示
        style.configure("Modern.Treeview.Heading",
                       background="#2c3e50",
                       foreground="white",
                       font=("Microsoft YaHei", 10, "bold"),
                       relief="raised",
                       borderwidth=2)

        # 配置选中和悬停效果
        style.map("Modern.Treeview",
                 background=[('selected', '#e8f4fd'), ('active', '#f0f8ff')],
                 foreground=[('selected', '#2c3e50')])

        style.map("Modern.Treeview.Heading",
                 background=[('active', '#34495e'), ('pressed', '#1a252f')],
                 foreground=[('active', 'white'), ('pressed', 'white')])

        # 定义列
        columns = ("序号", "钻头直径", "孔数", "PTH", "成品孔径", "公差", "钻槽长度", "符号", "磨次", "备注")

        # 创建Treeview - 使用自定义样式
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings',
                           height=22, style="Modern.Treeview")

        # 设置列标题和宽度 - 优化列宽分配
        column_config = {
            "序号": {"width": 70, "anchor": "center", "text": "🔢 序号"},
            "钻头直径": {"width": 90, "anchor": "center", "text": "📏 钻头直径(mm)"},
            "孔数": {"width": 70, "anchor": "center", "text": "🔵 孔数"},
            "PTH": {"width": 60, "anchor": "center", "text": "🔗 PTH"},
            "成品孔径": {"width": 100, "anchor": "center", "text": "⚙️ 成品孔径"},
            "公差": {"width": 80, "anchor": "center", "text": "📐 公差"},
            "钻槽长度": {"width": 180, "anchor": "w", "text": "📊 钻槽长度"},
            "符号": {"width": 80, "anchor": "center", "text": "🔤 符号"},
            "磨次": {"width": 70, "anchor": "center", "text": "🔄 磨次"},
            "备注": {"width": 250, "anchor": "w", "text": "📝 备注"}
        }

        for col in columns:
            config = column_config[col]
            tree.heading(col, text=config["text"])
            tree.column(col, width=config["width"], minwidth=60, anchor=config["anchor"])

        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 放置Treeview和滚动条
        tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # 配置grid权重
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # 配置交替行颜色
        tree.tag_configure('oddrow', background='#f8f9fa')
        tree.tag_configure('evenrow', background='#ffffff')
        tree.tag_configure('slotrow', background='#e8f5e8', foreground='#2d5a2d')

        # 填充数据 - 添加颜色区分和数据格式化
        row_count = 0
        for k, diameter in sorted(tools.items(), key=lambda x: int(x[0]) if x[0].isdigit() else x[0]):
            tool_id = 'T' + k
            hole_count = hole_counts.get(tool_id, 0)

            # 获取槽长信息并格式化备注
            slot_remark_value = slot_lengths.get(k, "").strip()
            remark_value = f"SLOT {slot_remark_value}" if slot_remark_value else ""

            # 格式化直径显示
            diameter_display = f"{diameter:.3f}"

            # 格式化孔数显示
            hole_display = f"{hole_count:,}" if hole_count > 0 else "0"

            # 确定行标签
            if slot_remark_value:
                row_tag = 'slotrow'
                remark_display = f"🎯 {remark_value}"
            else:
                row_tag = 'evenrow' if row_count % 2 == 0 else 'oddrow'
                remark_display = remark_value

            # 插入数据行
            item = tree.insert('', 'end', values=(
                tool_id,                    # 序号
                diameter_display,           # 钻头直径
                hole_display,               # 孔数
                "",                         # PTH
                "",                         # 成品孔径
                "",                         # 公差
                slot_remark_value,          # 钻槽长度
                "",                         # 符号
                "",                         # 磨次
                remark_display              # 备注
            ), tags=(row_tag,))

            row_count += 1

        # 创建底部操作栏
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(15, 0))

        # 左侧状态信息
        status_frame = ttk.Frame(bottom_frame)
        status_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 状态信息标签
        status_text = f"📈 数据统计: {len(tools)}种钻头 | {total_holes}个孔位 | {row_count}行数据"
        status_label = ttk.Label(status_frame, text=status_text,
                                font=("Microsoft YaHei", 9), foreground="#666666")
        status_label.pack(side=tk.LEFT, pady=5)

        # 右侧按钮组
        button_frame = ttk.Frame(bottom_frame)
        button_frame.pack(side=tk.RIGHT)

        # 配置按钮样式
        style.configure("Action.TButton",
                       font=("Microsoft YaHei", 10),
                       padding=(20, 8))

        style.configure("Primary.TButton",
                       font=("Microsoft YaHei", 10, "bold"),
                       padding=(25, 10))

        def save_and_export():
            """保存并导出Excel"""
            try:
                # 获取所有数据
                data = []
                for item in tree.get_children():
                    values = tree.item(item, 'values')
                    # 清理备注中的emoji
                    remark = values[9].replace("🎯 ", "") if values[9] else ""
                    data.append({
                        '序号': values[0],
                        '钻头直径': values[1],
                        '孔数': values[2],
                        'PTH': values[3],
                        '成品孔径': values[4],
                        '公差': values[5],
                        '钻槽长度': values[6],
                        '符号': values[7],
                        '磨次': values[8],
                        '备注': remark
                    })

                # 导出Excel
                output_path = file_path.replace('.drl', '_钻头信息.xlsx')
                self.export_data_to_excel(data, holes, output_path)

                messagebox.showinfo("✅ 导出成功",
                                  f"Excel文件已成功保存到:\n{output_path}\n\n包含 {len(data)} 行数据")
                editor_window.destroy()

            except Exception as e:
                messagebox.showerror("❌ 导出失败", f"保存过程中发生错误:\n{str(e)}")

        # 添加现代化按钮
        cancel_btn = ttk.Button(button_frame, text="❌ 取消",
                               command=editor_window.destroy, style="Action.TButton")
        cancel_btn.pack(side=tk.RIGHT, padx=(0, 10))

        export_btn = ttk.Button(button_frame, text="📊 导出Excel",
                               command=save_and_export, style="Primary.TButton")
        export_btn.pack(side=tk.RIGHT)

    def export_data_to_excel(self, data, holes, output_path):
        """导出数据到Excel文件 - 简化版本避免卡顿"""
        try:
            import pandas as pd

            # 创建DataFrame
            tools_df = pd.DataFrame(data)
            holes_df = pd.DataFrame(holes)

            # 快速保存到Excel，不添加复杂样式
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                tools_df.to_excel(writer, sheet_name='钻头信息', index=False)
                holes_df.to_excel(writer, sheet_name='钻孔数据', index=False)

        except Exception as e:
            raise Exception(f"Excel导出失败: {str(e)}")

    def open_tool_editor(self, tools, holes, slot_lengths, file_path):
        # 计算每个钻头的孔数
        hole_counts = {}
        for hole in holes:
            tool_id = hole['序号']
            hole_counts[tool_id] = hole_counts.get(tool_id, 0) + 1

        total_holes = sum(hole_counts.values())
        
        # 创建编辑器窗口
        editor_window = tk.Toplevel(self.root)
        editor_window.title("编辑钻头信息")
        editor_window.geometry("900x500")
        editor_window.grab_set()  # 设置为模态窗口
        
        # 创建主框架
        main_frame = ttk.Frame(editor_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        ttk.Label(main_frame, text="钻头信息编辑", font=("SimSun", 14, "bold")).pack(pady=10)
        
        # 创建带滚动条的画布 - 使用高效渲染
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        canvas = tk.Canvas(canvas_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        
        # 配置画布和滚动条
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置画布和滚动条
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        def _on_mousewheel(event):
                # 检查事件源是否是Combobox
            widget = event.widget
            if isinstance(widget, ttk.Combobox):
                return "break"  # 如果是Combobox，完全阻止事件处理
            
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        

        
        # 移除全局绑定，改为只绑定到canvas和scrollable_frame
        canvas.bind("<MouseWheel>", _on_mousewheel)
        scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
        
        # 为所有非Combobox的控件绑定鼠标滚轮事件，而不是全局绑定
        def bind_mousewheel_to_children(widget):
            for child in widget.winfo_children():
                if not isinstance(child, ttk.Combobox):  # 不绑定到Combobox
                    child.bind("<MouseWheel>", _on_mousewheel)
                    if hasattr(child, 'winfo_children'):
                        bind_mousewheel_to_children(child)
        
        # 递归绑定到所有非Combobox子控件
        bind_mousewheel_to_children(scrollable_frame)
        
        frame = ttk.Frame(scrollable_frame)
        frame.pack(fill=tk.BOTH, expand=True)
        frame.bind("<MouseWheel>", _on_mousewheel)  # 确保frame也能响应滚轮
        columns = ["序号", "钻头直径", "孔数", "PTH", "成品孔径", "公差", "钻槽长度", "符号", "磨次", "备注"]
        for i, col in enumerate(columns):
            ttk.Label(frame, text=col, font=("SimSun", 10, "bold")).grid(
                row=0, column=i, padx=5, pady=5, sticky="nsew"
            )
        # 设置表格样式
        style = ttk.Style()
        style.configure("TFrame", font=("SimSun", 10))
        style.configure("TLabel", font=("SimSun", 10))
        style.configure("TEntry", font=("SimSun", 10))
        style.configure("TButton", font=("SimSun", 10))
        style.configure("TCombobox", font=("SimSun", 10))
        for i in range(10):  # 10列
            frame.grid_columnconfigure(i, weight=1)
        tool_entries = {}
        row_idx = 1
        for k, diameter in sorted(tools.items(), key=lambda x: int(x[0]) if x[0].isdigit() else x[0]):
            tool_id = 'T' + k
            tool_entries[tool_id] = {}
            
            # 序号
            ttk.Label(frame, text=tool_id, font=("SimSun", 10)).grid(
                row=row_idx, column=0, padx=5, pady=2, sticky="nsew"
            )

            diameter_var = tk.StringVar(value=str(diameter))
            diameter_entry = ttk.Entry(frame, width=10, textvariable=diameter_var, font=("SimSun", 10))
            diameter_entry.grid(row=row_idx, column=1, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['diameter'] = diameter_var

            hole_count = hole_counts.get(tool_id, 0)
            ttk.Label(frame, text=str(hole_count), font=("SimSun", 10)).grid(
                row=row_idx, column=2, padx=5, pady=2, sticky="nsew"
            )

            pth_var = tk.StringVar(value="")
            pth_combo = ttk.Combobox(frame, width=5, textvariable=pth_var, values=["Y", "N", ""],
                                    state="readonly", font=("SimSun", 10))
            pth_combo.grid(row=row_idx, column=3, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['pth'] = pth_var

            finished_var = tk.StringVar()
            finished_entry = ttk.Entry(frame, width=10, textvariable=finished_var,
                                     font=("SimSun", 10))
            finished_entry.grid(row=row_idx, column=4, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['finished'] = finished_var

            tolerance_var = tk.StringVar()
            tolerance_entry = ttk.Entry(frame, width=15, textvariable=tolerance_var,
                                      font=("SimSun", 10))
            tolerance_entry.grid(row=row_idx, column=5, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['tolerance'] = tolerance_var
            slot_remark_value = slot_lengths.get(k, "").strip()
            length_var = tk.StringVar(value=slot_remark_value)
            length_entry = ttk.Entry(frame, width=15, textvariable=length_var, font=("SimSun", 10))
            length_entry.grid(row=row_idx, column=6, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['length'] = length_var
            symbol_var = tk.StringVar()
            symbol_entry = ttk.Entry(frame, width=10, textvariable=symbol_var, font=("SimSun", 10))
            symbol_entry.grid(row=row_idx, column=7, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['symbol'] = symbol_var
            grind_var = tk.StringVar()
            grind_values = ["", "M0", "M1", "M2", "M3", "M4"]
            grind_combo = ttk.Combobox(frame, width=5, textvariable=grind_var, values=grind_values,
                                      state="readonly", font=("SimSun", 10))
            grind_combo.grid(row=row_idx, column=8, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['grind'] = grind_var
            # 模拟C#代码中的数据库查询逻辑：drills_remark表
            # 根据钻头直径查询预设备注，如果有预设备注则优先使用，否则使用SLOT信息
            drill_diameter_remarks = {
                # 根据您提供的实际ERP数据库drills_remark表内容配置
                # RKEY  DRILL_DIA  DRILL_REMARK
                0.505: "机台孔",        # RKEY=1
                2.002: "定位工具孔",    # RKEY=2
                2.003: "激光定位孔",    # RKEY=3
                3.175: "工具孔",        # RKEY=4
                3.176: "防呆孔",        # RKEY=5
                3.202: "靶孔",          # RKEY=6
                0.504: "料号孔",        # RKEY=7
                2.004: "工具孔",        # RKEY=8
                0.076: "复合靶",        # RKEY=9
                0.1: "激光钻孔",        # RKEY=10
            }

            # 备注默认值逻辑：根据C#代码drillRadioGroup1=2的逻辑
            remark_default_value = ""

            # 根据数据库映射获取预定义备注
            predefined_remark = drill_diameter_remarks.get(diameter, "")

            # 根据C#代码逻辑：只有当strremark包含"SLOT"时才处理槽长
            # 即：只有有G85命令的钻头才显示槽长，其他钻头只显示预定义备注
            if slot_remark_value:  # 有G85命令，strremark = "SLOT"
                # C#: if (strremark.Contains("SLOT")) 分支
                if predefined_remark:
                    # C#: dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString()
                    remark_default_value = f"{predefined_remark} SLOT {slot_remark_value}"
                else:
                    # C#: dr29["REMARK"] = strremark + " " + List4[k].ToString()
                    remark_default_value = f"SLOT {slot_remark_value}"
            else:  # 没有G85命令，strremark是其他值
                # C#: else分支，只设置预定义备注
                remark_default_value = predefined_remark if predefined_remark else ""

            remark_var = tk.StringVar(value=remark_default_value)
            remark_values = ["", "过孔", "料号孔", "BGA区域过孔", "近孔", "邮票孔", "工艺孔", "机台孔", "清尘槽", "SLOT", "靶孔", "试钻孔", "长槽", "短槽"]
            remark_combo = ttk.Combobox(frame, width=35, textvariable=remark_var, values=remark_values,
                                       font=("SimSun", 10))
            remark_combo.grid(row=row_idx, column=9, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['remark'] = remark_var
            
            # 设置默认值和绑定更新事件
            def update_values(tool_id=tool_id, diameter_var=diameter_var, pth_var=pth_var, 
                             finished_var=finished_var, tolerance_var=tolerance_var, remark_var=remark_var):
                try:
                    diameter = float(diameter_var.get())
                    pth = pth_var.get()
                    
                    # 自动设置备注 - 仅当用户选择了PTH时
                    if pth:
                        remark = ""
                        if abs(diameter - 3.202) < 0.001:
                            remark = "靶孔"
                        elif abs(diameter - 0.307) < 0.001:
                            remark = "试钻孔"
                        else:
                            # 检查小数点后第三位
                            third_decimal = int((diameter * 1000) % 10)
                            if third_decimal == 1:
                                remark = "长槽"
                            elif third_decimal == 2:
                                remark = "短槽"
                        
                        # 只有在备注为空时才设置
                        if not remark_var.get():
                            remark_var.set(remark)
                    
                    # PTH切换时始终刷新成品孔径和公差
                    if pth == "Y":
                        finished = round(diameter - 0.1, 3)
                        tolerance = "+0.075/-0.075"
                    elif pth == "N":
                        finished = round(diameter - 0.05, 3)
                        tolerance = "+0.05/-0.05"
                    else:  # pth为空
                        finished = ""
                        tolerance = ""
                    finished_var.set(str(finished) if finished else "")
                    tolerance_var.set(tolerance)
                except ValueError:
                    pass
            
            # 绑定事件
            diameter_var.trace_add("write", lambda *args, t=tool_id, d=diameter_var, p=pth_var, 
                                 f=finished_var, tol=tolerance_var, r=remark_var: 
                                 update_values(t, d, p, f, tol, r))
            pth_var.trace_add("write", lambda *args, t=tool_id, d=diameter_var, p=pth_var, 
                            f=finished_var, tol=tolerance_var, r=remark_var: 
                            update_values(t, d, p, f, tol, r))
            
            # 添加针对Combobox的特殊处理，防止滚动时干扰
            def prevent_scroll_interference(combo):
                    # 阻止下拉框接收鼠标滚轮事件
                combo.bind("<MouseWheel>", lambda e: "break")
                
                # 确保下拉框关闭时的值不会丢失
                combo.bind("<<ComboboxSelected>>", lambda e: scrollable_frame.focus_set())
                
            # 应用到所有下拉框
            prevent_scroll_interference(pth_combo)
            prevent_scroll_interference(grind_combo)
            prevent_scroll_interference(remark_combo)
            
            row_idx += 1
        
        # 添加合计行
        ttk.Separator(frame, orient='horizontal').grid(row=row_idx, column=0, columnspan=11, sticky='ew', pady=5)
        row_idx += 1
        
        ttk.Label(frame, text="合计", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=0, padx=5, pady=2, sticky="nsew"
        )
        
        ttk.Label(frame, text=f"{total_holes}", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=2, padx=5, pady=2, sticky="nsew"
        )
        
        # 为每个单元格添加边框效果
        for child in frame.winfo_children():
            child.grid_configure(padx=3, pady=3)
        
        # 为新创建的控件绑定鼠标滚轮事件
        bind_mousewheel_to_children(frame)
        
        # 为所有下拉框控件应用特殊处理
        for child in frame.winfo_children():
            if isinstance(child, ttk.Combobox):
                prevent_scroll_interference(child)
        
        # 添加底部操作区
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10, fill=tk.X)
        
        # 保存按钮
        save_button = ttk.Button(
            button_frame, 
            text="保存并导出Excel", 
            command=lambda: self.save_and_export(editor_window, tools, holes, tool_entries, hole_counts, total_holes, file_path),
            style="Accent.TButton"
        )
        save_button.pack(side=tk.RIGHT, padx=10)
        
        # 取消按钮
        cancel_button = ttk.Button(
            button_frame, 
            text="取消", 
            command=editor_window.destroy
        )
        cancel_button.pack(side=tk.RIGHT, padx=10)
    
    def save_and_export(self, editor_window, tools, holes, tool_entries, hole_counts, total_holes, file_path):
        # 创建钻头信息表
        tool_data = []
        for tool_id, entries in tool_entries.items():
            try:
                # 处理成品孔径可能为空的情况
                finished_val = entries['finished'].get()
                if finished_val and finished_val.strip():
                    finished_val = float(finished_val)
                else:
                    finished_val = None
                    
                tool_data.append({
                    '序号': tool_id,
                    '钻头直径': float(entries['diameter'].get()),
                    '孔数': hole_counts.get(tool_id, 0),
                    'PTH': entries['pth'].get(),
                    '成品孔径': finished_val,
                    '公差': entries['tolerance'].get(),
                    '钻槽长度': entries['length'].get(),
                    '符号': entries['symbol'].get(),
                    '磨次': entries['grind'].get(),
                    '备注': entries['remark'].get()
                })
            except ValueError as e:
                messagebox.showerror("错误", f"工具 {tool_id} 数据格式错误: {str(e)}")
                return
        
        # 添加合计行
        tool_data.append({
            '序号': '合计',
            '钻头直径': None,
            '孔数': total_holes,
            'PTH': None,
            '成品孔径': None,
            '公差': None,
            '钻槽长度': None,
            '符号': None,
            '磨次': None,
            '备注': None
        })
        
        # 更新孔数据
        for hole in holes:
            tool_id = hole['序号']
            
            if '钻头直径(mm)' in hole:
                hole['钻头直径'] = hole.pop('钻头直径(mm)')
            
            if tool_id in tool_entries:
                if '钻头直径' in hole:
                    pass  # 已经是正确的键名
                
                hole['PTH'] = tool_entries[tool_id]['pth'].get()
                hole['成品孔径'] = tool_entries[tool_id]['finished'].get()
                hole['公差'] = tool_entries[tool_id]['tolerance'].get()
                hole['钻槽长度'] = tool_entries[tool_id]['length'].get()
                hole['符号'] = tool_entries[tool_id]['symbol'].get()
                hole['磨次'] = tool_entries[tool_id]['grind'].get()
                hole['备注'] = tool_entries[tool_id]['remark'].get()
        
        # 创建数据框
        tools_df = pd.DataFrame(tool_data)
        holes_df = pd.DataFrame(holes)
        
        # 创建保存进度窗口
        progress_window = tk.Toplevel(editor_window)
        progress_window.title("保存中")
        progress_window.geometry("300x100")
        progress_window.transient(editor_window)
        progress_window.grab_set()
        
        progress_label = ttk.Label(progress_window, text="正在保存Excel文件...", font=("SimSun", 10))
        progress_label.pack(pady=10)
        
        progress_bar = ttk.Progressbar(progress_window, orient="horizontal", length=250, mode="indeterminate")
        progress_bar.pack(pady=10)
        progress_bar.start(10)
        
        progress_window.update()
        
        # 设置输出路径
        output_path = os.path.splitext(file_path)[0] + '.xlsx'
        
        # 快速保存Excel文件 - 避免卡顿
        def save_excel_file():
            try:
                # 简化保存，不添加复杂样式
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    tools_df.to_excel(writer, sheet_name='钻头信息', index=False)
                    holes_df.to_excel(writer, sheet_name='钻孔数据', index=False)

                
                # 关闭进度窗口
                progress_window.destroy()
                
                # 关闭编辑器窗口
                editor_window.destroy()
                
                # 显示成功消息并询问是否打开文件
                result = messagebox.askyesno(
                    "处理成功", 
                    f"文件已转换为Excel格式:\n{output_path}\n\n是否立即打开该文件？"
                )
                
                # 如果用户选择打开文件
                if result:
                    try:
                        os.startfile(output_path)
                    except:
                        messagebox.showinfo("提示", f"请手动打开文件:\n{output_path}")
                
                # 更新状态
                self.status_label.config(text="转换完成")
                
            except Exception as e:
                # 关闭进度窗口并显示错误
                if progress_window.winfo_exists():
                    progress_window.destroy()
                messagebox.showerror("保存失败", f"导出Excel时出错: {str(e)}")
        
        # 启动保存线程
        save_thread = threading.Thread(target=save_excel_file)
        save_thread.daemon = True
        save_thread.start()

def main():
    import tkinter as tk
    from tkinter import messagebox
    
    # 命令行参数处理
    parser = argparse.ArgumentParser(description='DRL文件转Excel工具')
    parser.add_argument('--file', '-f', help='要处理的DRL文件路径')
    args = parser.parse_args()
    
    # 如果提供了文件参数，直接处理文件
    if args.file:
        try:
            # 创建根窗口但不显示
            root = tk.Tk()
            root.withdraw()
            
            # 验证许可证
            validator = LicenseValidator()
            if not validator.is_valid():
                sys.exit(1)

            # 创建应用实例
            app = DrlConverterApp(root)

            # 处理文件
            tools, holes, slot_lengths = app.parse_drl_file(args.file)

            # 退出
            root.destroy()
            return
        except Exception as e:
            return
    
    # 捕获未处理的异常
    def handle_exception(exc_type, exc_value, exc_traceback):
        import traceback
        # 获取异常信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        # 显示错误对话框
        messagebox.showerror("程序错误", f"程序遇到未处理的异常:\n{error_msg}")
        sys.exit(1)
    
    # 设置异常处理器
    sys.excepthook = handle_exception
    
    try:
        # 创建主窗口前优化Tkinter设置
        root = tk.Tk()
        
        # 设置DPI感知
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
        
        # 设置窗口属性
        root.title("JSYPCB钻带转Excel工具")
        root.resizable(True, True)  # 允许调整大小
        
        # 设置窗口居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = 400
        window_height = 250
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 验证许可证
        validator = LicenseValidator()
        if not validator.is_valid():
            root.withdraw()
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            exit(1)
        
        # 创建应用实例
        app = DrlConverterApp(root)
        
        # 配置应用样式
        style = ttk.Style()
        if "vista" in style.theme_names():
            style.theme_use("vista")
        elif "clam" in style.theme_names():
            style.theme_use("clam")
        
        # 在Windows上配置按钮样式
        if os.name == 'nt':
            style.configure("Accent.TButton", 
                           background="#4CAF50", 
                           foreground="black",  # 修改为黑色字体
                           font=("SimSun", 10, "bold"))
        
        # 设置关闭窗口处理
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出程序吗?"):
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动主循环
        root.mainloop()
    
    except Exception as e:
        # 显示错误对话框
        if 'root' in locals():
            root.withdraw()
        messagebox.showerror("启动错误", f"程序启动时出错:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()