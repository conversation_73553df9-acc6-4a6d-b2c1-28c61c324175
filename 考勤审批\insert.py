import json
from sqlalchemy import create_engine, Column, Integer, String, Boolean
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker

# 数据库连接配置
DATABASE_URI = 'mysql+pymysql://root:123456@localhost/lego-admin'

Base = declarative_base()

class User(Base):
    __tablename__ = 'manager_hikiot_dept'
    id = Column(Integer, primary_key=True)
    code = Column('code', String(255), nullable=False)
    team_no = Column('team_No', String(255))

    path = Column('path', String(255))
    path_name = Column('path_name', String(255))
    level = Column('level', Integer)
    is_leaf = Column('is_leaf', Integer)
    leader_id = Column('leader_id', String(255))
    parent_id = Column('parent_id', String(255))
    recent_path_name = Column('recent_path_name', String(255))
    seq = Column('seq', Integer)
    name = Column('name', String(255))

def load_json_data(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data['data']['teamDepartVOs']

def insert_users(session, users_data):
    for item in users_data:
        try:
            item['isLeaf'] = int(item['isLeaf'])
            if 'leader' not in item:
                item['leader'] = ""
                
            user = User(
                id=item['id'],
                code=item['departNo'],
                name=item['departName'],
                path=item['path'],
                path_name=item['pathName'],
                team_no=item['teamNo'],
                level=item['level'],
                is_leaf=item['isLeaf'],
                leader_id=item['leader'],
                parent_id=item['parentId'],
                recent_path_name=item['recentPathName'],
                seq=item['seq']
            )
            session.add(user)
        except Exception as e:
            print(f"插入失败：{e}\n问题数据：{item}")
    session.commit()

def main():
    engine = create_engine(DATABASE_URI)
    Base.metadata.create_all(engine)

    Session = sessionmaker(bind=engine)
    session = Session()

    file_path = 'd:\\海康威视\\考勤审批\\detailDept.js'
    users_data = load_json_data(file_path)
    
    print(f"共加载 {len(users_data)} 条部门数据")
    insert_users(session, users_data)

    session.close()
    print("✅ 数据导入完成！")

if __name__ == '__main__':
    main()