#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试sync.py的登录功能和日志记录
"""

def test_login_with_logging():
    """测试登录功能的日志记录"""
    print("🧪 测试登录功能的日志记录")
    print("=" * 50)
    
    try:
        # 导入sync.py中的登录函数和日志
        from sync import login_and_get_token, logger, log_setup
        
        print("✅ 成功导入登录函数和日志模块")
        print(f"📝 日志文件路径: {log_setup.log_file_path}")
        
        # 测试登录功能
        logger.info("🚀 开始测试登录功能")
        print("🔐 开始登录测试...")
        
        result = login_and_get_token()
        
        if result:
            token, other_token = result
            logger.info("✅ 登录测试成功")
            print("✅ 登录测试成功")
            print(f"Token: {token[:10]}...{token[-10:] if len(token) > 20 else token}")
        else:
            logger.error("❌ 登录测试失败")
            print("❌ 登录测试失败")
        
        logger.info("🏁 登录测试完成")
        
        # 检查日志文件
        if log_setup.log_file_path and log_setup.log_file_path.exists():
            file_size = log_setup.log_file_path.stat().st_size
            print(f"\n📊 日志文件大小: {file_size} 字节")
            
            # 读取日志内容
            with open(log_setup.log_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                print(f"📄 日志文件总行数: {len(lines)}")
                
                print("\n📄 完整日志内容:")
                print("=" * 80)
                print(content)
                print("=" * 80)
        else:
            print("❌ 日志文件不存在")
        
        return result is not None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 关闭日志系统
        try:
            log_setup.close_logger()
        except:
            pass

if __name__ == "__main__":
    success = test_login_with_logging()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    input("按回车键退出...")
