import pandas as pd
import hashlib

def md5_encrypt(text):
    md5 = hashlib.md5()
    md5.update(text.encode('utf-8'))
    return md5.hexdigest().upper() 
file_path = r'C:\Users\<USER>\Desktop\cs.xlsx'
df = pd.read_excel(file_path)

print("Excel 表头列名：", df.columns.tolist())  

column_name = 'code'    
df['password'] = df[column_name].apply(lambda x: md5_encrypt(f"{x}jsy123"))
df.to_excel(file_path, index=False)

print("✅ 密码列已成功添加并保存！")