# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['D:\海康威视\工程钻带\dist_icon_fixed\transfer_secure_fixed.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('D:\海康威视\工程钻带\converted_icon.ico', '.'),  # 将图标文件嵌入到根目录
    ],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='钻带转Excel工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='D:\海康威视\工程钻带\converted_icon.ico',  # 设置exe文件图标
    distpath='D:\海康威视\工程钻带\dist_icon_fixed',
)
