('D:\\海康威视\\build\\setup\\PYZ-00.pyz',
 [('JsyUpload', 'D:\\海康威视\\JsyUpload.py', 'PYMODULE'),
  ('__future__', 'D:\\environment\\python\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\environment\\python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'D:\\environment\\python\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\environment\\python\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\environment\\python\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\environment\\python\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\environment\\python\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'D:\\environment\\python\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata',
   'D:\\environment\\python\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\environment\\python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\environment\\python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\environment\\python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\environment\\python\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\environment\\python\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\environment\\python\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'D:\\environment\\python\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\environment\\python\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'D:\\environment\\python\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'D:\\environment\\python\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\environment\\python\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\environment\\python\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'D:\\environment\\python\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'D:\\environment\\python\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'D:\\environment\\python\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'D:\\environment\\python\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'D:\\environment\\python\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'D:\\environment\\python\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\environment\\python\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'D:\\environment\\python\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'D:\\environment\\python\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\environment\\python\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\environment\\python\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'D:\\environment\\python\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\environment\\python\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\environment\\python\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\environment\\python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\environment\\python\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\environment\\python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\environment\\python\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\environment\\python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\environment\\python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\environment\\python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\environment\\python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\environment\\python\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\environment\\python\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\environment\\python\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\environment\\python\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\environment\\python\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\environment\\python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\environment\\python\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\environment\\python\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\environment\\python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins',
   'D:\\environment\\python\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\environment\\python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\environment\\python\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\environment\\python\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\environment\\python\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\environment\\python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\environment\\python\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\environment\\python\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\environment\\python\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\environment\\python\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\environment\\python\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\environment\\python\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\environment\\python\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\environment\\python\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\environment\\python\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\environment\\python\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\environment\\python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\environment\\python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\environment\\python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\environment\\python\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\environment\\python\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\environment\\python\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\environment\\python\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\environment\\python\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\environment\\python\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\environment\\python\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('code', 'D:\\environment\\python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\environment\\python\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\environment\\python\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\environment\\python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\environment\\python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\environment\\python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\environment\\python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\environment\\python\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\environment\\python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\environment\\python\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\environment\\python\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\environment\\python\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\environment\\python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\environment\\python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\environment\\python\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\environment\\python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\environment\\python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\environment\\python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\environment\\python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\environment\\python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\environment\\python\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses', 'D:\\environment\\python\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key',
   'D:\\environment\\python\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\environment\\python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\environment\\python\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\environment\\python\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\environment\\python\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\environment\\python\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\environment\\python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\environment\\python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\environment\\python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\environment\\python\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\environment\\python\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\environment\\python\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\environment\\python\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\environment\\python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\environment\\python\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\environment\\python\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\environment\\python\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\environment\\python\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\environment\\python\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\environment\\python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\environment\\python\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\environment\\python\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\environment\\python\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\environment\\python\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\environment\\python\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\environment\\python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\environment\\python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\environment\\python\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\environment\\python\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\environment\\python\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\environment\\python\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\environment\\python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\environment\\python\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\environment\\python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\environment\\python\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\environment\\python\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\environment\\python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\environment\\python\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\environment\\python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\environment\\python\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\environment\\python\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\environment\\python\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server', 'D:\\environment\\python\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'D:\\environment\\python\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\environment\\python\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\environment\\python\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\environment\\python\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\environment\\python\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\environment\\python\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\environment\\python\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\environment\\python\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\environment\\python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\environment\\python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\environment\\python\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\environment\\python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\environment\\python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\environment\\python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\environment\\python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\environment\\python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\environment\\python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\environment\\python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\environment\\python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\environment\\python\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\environment\\python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\environment\\python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\environment\\python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\environment\\python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\environment\\python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\environment\\python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\environment\\python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\environment\\python\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\environment\\python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\environment\\python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'D:\\environment\\python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\environment\\python\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\environment\\python\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\environment\\python\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging', 'D:\\environment\\python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\environment\\python\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\environment\\python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\environment\\python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\environment\\python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\environment\\python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\environment\\python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\environment\\python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\environment\\python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\environment\\python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\environment\\python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\environment\\python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\environment\\python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\environment\\python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\environment\\python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\environment\\python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\environment\\python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\environment\\python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\environment\\python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\environment\\python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\environment\\python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\environment\\python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\environment\\python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\environment\\python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\environment\\python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\environment\\python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\environment\\python\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\environment\\python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\environment\\python\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\environment\\python\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\environment\\python\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\environment\\python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc',
   'D:\\environment\\python\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\environment\\python\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pickle', 'D:\\environment\\python\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\environment\\python\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\environment\\python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\environment\\python\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'D:\\environment\\python\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\environment\\python\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\environment\\python\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\environment\\python\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\environment\\python\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\environment\\python\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'D:\\environment\\python\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'D:\\environment\\python\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\environment\\python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\environment\\python\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\environment\\python\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\environment\\python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\environment\\python\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\environment\\python\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\environment\\python\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\environment\\python\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\environment\\python\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\environment\\python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\environment\\python\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\environment\\python\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'D:\\environment\\python\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\environment\\python\\Lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'D:\\environment\\python\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\environment\\python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\environment\\python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\environment\\python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\environment\\python\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig',
   'D:\\environment\\python\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile', 'D:\\environment\\python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\environment\\python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\environment\\python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\environment\\python\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\environment\\python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\environment\\python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\environment\\python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'D:\\environment\\python\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re', 'D:\\environment\\python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types',
   'D:\\environment\\python\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\environment\\python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\environment\\python\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\environment\\python\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\environment\\python\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\environment\\python\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\environment\\python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\environment\\python\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\environment\\python\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\environment\\python\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\environment\\python\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\environment\\python\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\environment\\python\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\environment\\python\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\environment\\python\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\environment\\python\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'D:\\environment\\python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'D:\\environment\\python\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\environment\\python\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\environment\\python\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\environment\\python\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\environment\\python\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\environment\\python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('win32con',
   'D:\\environment\\python\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\environment\\python\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win32serviceutil',
   'D:\\environment\\python\\Lib\\site-packages\\win32\\lib\\win32serviceutil.py',
   'PYMODULE'),
  ('win32timezone',
   'D:\\environment\\python\\Lib\\site-packages\\win32\\lib\\win32timezone.py',
   'PYMODULE'),
  ('winerror',
   'D:\\environment\\python\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'D:\\environment\\python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\environment\\python\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\environment\\python\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\environment\\python\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\environment\\python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\environment\\python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\environment\\python\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\environment\\python\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\environment\\python\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\environment\\python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'D:\\environment\\python\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\environment\\python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\environment\\python\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\environment\\python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\environment\\python\\Lib\\zipimport.py', 'PYMODULE')])
