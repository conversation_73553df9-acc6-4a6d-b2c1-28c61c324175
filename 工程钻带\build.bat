@echo off
setlocal enabledelayedexpansion

echo ======================================================
echo 钻带转Excel工具 - 编译选择器
echo ======================================================
echo.
echo 请选择编译方式:
echo.
echo 1. PyInstaller简易编译 (推荐，最稳定)
echo 2. PyInstaller安全编译 (带IP限制和过期检查)
echo 3. Nuitka高级编译 (C++级别编译，可能不稳定)
echo 4. 退出
echo.

set /p choice=请输入选项 (1-4): 

if "%choice%"=="1" (
    echo.
    echo 正在执行PyInstaller简易编译...
    echo.
    python simple_build.py
) else if "%choice%"=="2" (
    echo.
    echo 正在执行PyInstaller安全编译...
    echo.
    python secure_build.py
) else if "%choice%"=="3" (
    echo.
    echo 正在执行Nuitka高级编译...
    echo.
    python nuitka_advanced_build.py
) else if "%choice%"=="4" (
    echo 退出程序
    goto :end
) else (
    echo 无效选项，请重新运行
)

:end
echo.
echo ======================================================
echo 按任意键退出...
pause > nul 