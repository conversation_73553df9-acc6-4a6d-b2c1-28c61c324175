USE [test3]
GO

/****** Object:  Table [dbo].[Data0192]    Script Date: 2025-7-1 14:07:10 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Data0192](
	[RKEY] [int] IDENTITY(1,1) NOT NULL,
	[FEDTAXRATE] [float] NOT NULL,
	[CUSTFEDSHIP] [int] NOT NULL,
	[CUSTFEDPROD] [varchar](12) NULL,
	[CUSTFEDTOOL] [varchar](12) NULL,
	[CUSTSTASHIP] [char](1) NULL,
	[CUSTSTAPROD] [char](1) NULL,
	[CUS<PERSON><PERSON>TOOL] [char](1) NULL,
	[CUSTCOSHIP] [char](1) NULL,
	[CUSTCOPROD] [char](1) NULL,
	[CUSTCOTOOL] [char](1) NULL,
	[CUSTCISHIP] [char](1) NULL,
	[CUSTCIPROD] [char](1) NULL,
	[CUS<PERSON><PERSON>OOL] [char](1) NULL,
	[SUPLFEDSHIP] [varchar](30) NULL,
	[SUPLFEDMAT] [varchar](50) NULL,
	[SUPLFEDMISC] [varchar](30) NULL,
	[SUPLSTASHIP] [varchar](50) NULL,
	[SUPLSTAMAT] [char](1) NULL,
	[SUPLSTAMISC] [char](1) NULL,
	[SUPLCOSHIP] [char](1) NULL,
	[SUPLCOMAT] [char](1) NULL,
	[SUPLCOMISC] [char](1) NULL,
	[SUPLCISHIP] [char](1) NULL,
	[SUPLCIMAT] [char](1) NULL,
	[SUPLCIMISC] [char](1) NULL,
	[ARTAXONTAX] [char](1) NULL,
	[APTAXONTAX] [char](1) NULL,
	[TAXSTRUCT] [char](1) NULL,
	[LOCAL_TAX_ID] [char](15) NULL,
	[unit_ptr] [int] NOT NULL,
	[PPC_BY_SO] [char](1) NULL,
	[PPC_BY_CUSTP] [char](1) NULL,
	[ENG_AUDIT_FLAG] [char](1) NULL,
	[STOCK_IN_FLAG] [char](1) NULL,
	[ENG_MI_FILE_NO] [varchar](20) NULL,
	[PRIOR_DAYS] [numeric](6, 2) NULL,
	[BALA_DAYS] [numeric](6, 2) NULL,
	[STOP_TIME_1] [int] NULL,
	[STOP_TIME_2] [int] NULL,
	[STOP_TIME_3] [int] NULL,
	[STOP_TIME_4] [int] NULL,
	[START_TIME_1] [int] NULL,
	[START_TIME_2] [int] NULL,
	[START_TIME_3] [int] NULL,
	[START_TIME_4] [int] NULL,
	[SALESANALYSIS] [tinyint] NOT NULL,
	[PPC_CONTROL1] [tinyint] NOT NULL,
	[PPC_CONTROL2] [tinyint] NULL,
	[PPC_CONTROL3] [tinyint] NULL,
	[PPC_CONTROL4] [tinyint] NULL,
	[PPC_CONTROL5] [tinyint] NULL,
	[PART_LOCATION] [varchar](50) NULL,
	[ENG_CONTROL1] [tinyint] NULL,
	[ENG_CONTROL2] [tinyint] NULL,
	[MRP_CONTROL1] [tinyint] NULL,
	[PPC_CONTROL6] [tinyint] NULL,
	[PPC_CONTROL7] [tinyint] NULL,
	[PPC_CTL_SEED_1] [char](1) NULL,
	[PPC_CTL_SEED_2] [char](1) NULL,
	[PPC_CTL_SEED_3] [char](1) NULL,
	[PPC_CTL_SEED_4] [char](1) NULL,
	[PPC_CTL_PNL_1] [int] NULL,
	[PPC_CTL_PNL_2] [int] NULL,
	[PPC_CTL_PNL_3] [int] NULL,
	[PPC_CONTROL8] [tinyint] NULL,
	[FG_SCP_DEPT_PTR] [int] NULL,
	[SFG_SCP_DEPT_PTR] [int] NULL,
	[PPC_CONTROL9] [tinyint] NULL,
	[PPC_CONTROL10] [tinyint] NULL,
	[PPC_CONTROL11] [tinyint] NULL,
	[QA_CONTROL1] [tinyint] NULL,
	[ENG_CONTROL3] [tinyint] NULL,
	[ENG_CONTROL4] [tinyint] NULL,
	[ENG_CONTROL5] [tinyint] NULL,
	[ENG_CONTROL6] [tinyint] NULL,
	[COST_CONTROL1] [tinyint] NULL,
	[CLOSE_DAYTH] [int] NULL,
	[CLOSE_HOURTH] [int] NULL,
	[QUOTE_STD_LGTH] [float] NULL,
	[QUOTE_STD_wdTH] [float] NULL,
	[QTE_CONTROL1] [tinyint] NULL,
	[ENG_CONTROL7] [tinyint] NULL,
	[PPC_CONTROL12] [tinyint] NULL,
	[COST_CONTROL2] [tinyint] NULL,
	[QTE_DISC_PCNT_FM_1] [float] NULL,
	[QTE_DISC_PCNT_FM_2] [float] NULL,
	[QTE_DISC_PCNT_FM_3] [float] NULL,
	[QTE_DISC_PCNT_FM_4] [float] NULL,
	[QTE_DISC_PCNT_FM_5] [float] NULL,
	[QTE_DISC_PCNT_FM_6] [float] NULL,
	[QTE_DISC_PCNT_REDJ_1] [float] NULL,
	[QTE_DISC_PCNT_REDJ_2] [float] NULL,
	[QTE_DISC_PCNT_REDJ_3] [float] NULL,
	[QTE_DISC_PCNT_REDJ_4] [float] NULL,
	[QTE_DISC_PCNT_REDJ_5] [float] NULL,
	[QTE_DISC_PCNT_REDJ_6] [float] NULL,
	[ENG_CONTROL8] [tinyint] NULL,
	[PPC_CONTROL13] [tinyint] NULL,
	[QTE_CONTROL2] [tinyint] NULL,
	[CUT_UNIT_TP] [tinyint] NULL,
	[CUT_L1_MAX] [numeric](7, 1) NULL,
	[CUT_L1_MIN] [numeric](7, 1) NULL,
	[CUT_W1_MAX] [numeric](7, 1) NULL,
	[CUT_W1_MIN] [numeric](7, 1) NULL,
	[CUT_L2_MAX] [numeric](7, 1) NULL,
	[CUT_L2_MIN] [numeric](7, 1) NULL,
	[CUT_W2_MAX] [numeric](7, 1) NULL,
	[CUT_W2_MIN] [numeric](7, 1) NULL,
	[Border_L_Min] [numeric](8, 3) NULL,
	[Border_W_Min] [numeric](8, 3) NULL,
	[SPACE_L_Min] [numeric](8, 3) NULL,
	[SPACE_W_Min] [numeric](8, 3) NULL,
	[ENG_CONTROL9] [tinyint] NULL,
	[QTE_CONTROL3] [tinyint] NULL,
	[QTE_CONTROL4] [float] NULL,
	[PPC_CONTROL14] [tinyint] NULL,
	[PPC_CONTROL15] [tinyint] NULL,
	[COST_CONTROL3] [tinyint] NULL,
	[PPC_CONTROL16] [tinyint] NULL,
	[PPC_CONTROL17] [tinyint] NULL,
	[ENG_CONTROL11] [tinyint] NULL,
	[ENG_CONTROL12] [tinyint] NULL,
	[ENG_CONTROL13] [tinyint] NULL,
	[ENG_CONTROL10] [tinyint] NULL,
	[ENG_CONTROL14] [tinyint] NULL,
	[ENG_CONTROL15] [tinyint] NULL,
	[EP000] [tinyint] NULL,
	[PPC_CONTROL18] [tinyint] NULL,
	[ENG_CONTROL16] [tinyint] NULL,
	[PPC_CONTROL19] [varchar](20) NULL,
	[ISSUSE_AREA_DAY] [float] NULL,
	[PPC_CONTROL20] [tinyint] NULL,
	[PPC_CONTROL21] [tinyint] NULL,
	[ENG_CONTROL20] [tinyint] NULL,
	[PPC_CONTROL22] [tinyint] NULL,
	[ENG_CONTROL18] [tinyint] NULL,
	[ENG_CONTROL21] [tinyint] NULL,
	[PPC_CONTROL23] [tinyint] NULL,
	[PPC_CONTROL24] [tinyint] NULL,
	[PPC_CONTROL25] [tinyint] NULL,
	[PPC_CONTROL26] [float] NULL,
	[FIN_PRECH1] [varchar](5) NULL,
	[FIN_PRECH2] [varchar](5) NULL,
	[FIN_PRECH3] [varchar](5) NULL,
	[ecn_nr1] [varchar](8) NULL,
	[ecn_nr2] [varchar](8) NULL,
	[ecn_nr3] [varchar](8) NULL,
	[ecn_nr4] [varchar](8) NULL,
	[QTE_CONTROL5] [char](1) NULL,
	[PPC_CONTROL_27] [float] NULL,
	[FIN_CONTROL1] [char](1) NULL,
	[FIN_CONTROL2] [char](1) NULL,
	[fin_CONTROL3] [char](1) NULL,
	[SAMPLE_CONTROL] [tinyint] NULL,
	[ppc_control28] [char](1) NULL,
	[ppc_control29] [char](1) NULL,
	[pmc_control1] [char](1) NULL,
	[pmc_control2] [char](1) NULL,
	[cost_control4] [tinyint] NULL,
	[cost_control5] [char](1) NULL,
	[fin_CONTROL4] [char](1) NULL,
	[eng_control22] [tinyint] NULL,
	[cost_control6] [tinyint] NULL,
	[eng_control23] [int] NULL,
	[eng_control24] [tinyint] NULL,
	[rma_control] [tinyint] NULL,
	[cost_control7] [tinyint] NULL,
	[SALES_QUOT_CTRL1] [tinyint] NOT NULL,
	[SALES_QUOT_CTRL2] [tinyint] NOT NULL,
	[SALES_QUOT_CTRL3] [tinyint] NOT NULL,
	[REPORT_LOCATION] [varchar](50) NULL,
	[SO_NOTYPE_LIST] [varchar](500) NULL,
	[eng_control25] [tinyint] NOT NULL,
	[eng_control30] [tinyint] NOT NULL,
	[eng_control31] [tinyint] NULL,
	[eng_control32] [tinyint] NULL,
	[Prdept] [tinyint] NULL,
	[IsHUb] [tinyint] NULL,
	[eng_control33] [tinyint] NULL,
	[fin_control5] [tinyint] NULL,
	[SALES_EP032_VER_FLAG] [tinyint] NOT NULL,
	[CUST_GROUP_DESC] [varchar](2000) NULL,
	[qte_desc1] [varchar](50) NULL,
	[qte_desc2] [varchar](50) NULL,
	[qte_desc3] [varchar](50) NULL,
	[qte_desc4] [varchar](50) NULL,
	[qte_desc5] [varchar](50) NULL,
	[RTN_SO] [tinyint] NOT NULL,
	[QA_CONTROL2] [int] NOT NULL,
	[YearNumber] [int] NOT NULL,
	[Pur_Cons_Auth] [bit] NOT NULL,
	[WF_Local_Auth_Flag] [bit] NOT NULL,
	[WF_Cross_EXDept] [bit] NOT NULL,
	[GoodTimeLimit] [int] NOT NULL,
	[waifa_rate] [decimal](3, 1) NOT NULL,
	[ppc_control30] [tinyint] NULL,
	[qte_desc6] [varchar](50) NULL,
	[qte_desc7] [varchar](50) NULL,
	[qte_desc8] [varchar](50) NULL,
	[GREEN_DESC_LIST] [nvarchar](500) NULL,
	[RTN_SO_NOFLAG] [tinyint] NOT NULL,
	[SUPP_GROUP_DESC_LIST] [varchar](200) NULL,
	[FIN_CONTROL6] [tinyint] NULL,
	[qte_expire_days] [int] NOT NULL,
	[PCBCUT_FLAG] [tinyint] NOT NULL,
	[qte_unit] [tinyint] NOT NULL,
	[qte_desc9] [varchar](50) NULL,
	[qte_desc10] [varchar](50) NULL,
	[qte_desc11] [varchar](50) NULL,
	[qte_desc12] [varchar](50) NULL,
	[qte_desc13] [varchar](50) NULL,
	[qte_desc14] [varchar](50) NULL,
	[qte_desc15] [varchar](50) NULL,
	[qte_desc16] [varchar](50) NULL,
	[Mtr_RTUser_ControlType] [tinyint] NOT NULL,
	[eng_control34] [tinyint] NOT NULL,
	[ppc_control31] [tinyint] NOT NULL,
	[eng_control35] [tinyint] NOT NULL,
	[PWConvert_FLAG] [bit] NOT NULL,
	[stock_initialized] [tinyint] NOT NULL,
	[ppc_control34] [tinyint] NOT NULL,
	[eng_control38] [int] NOT NULL,
	[ppc_control33] [tinyint] NOT NULL,
	[GR_NoCheck] [bit] NOT NULL,
	[PO_AllowJumpAudit] [bit] NOT NULL,
	[PR_PriceMode] [tinyint] NOT NULL,
	[ppc_control32] [tinyint] NOT NULL,
	[pcbcut_pnl_type] [tinyint] NOT NULL,
	[ecn_control2] [tinyint] NOT NULL,
	[ECN_CONTROL1] [tinyint] NOT NULL,
	[qte_desc1_list] [varchar](2000) NULL,
	[qte_desc2_list] [varchar](250) NULL,
	[qte_desc3_list] [varchar](250) NULL,
	[qte_desc4_list] [varchar](250) NULL,
	[qte_desc5_list] [varchar](250) NULL,
	[qte_desc6_list] [varchar](250) NULL,
	[qte_desc7_list] [varchar](250) NULL,
	[qte_desc8_list] [varchar](250) NULL,
	[qte_desc9_list] [varchar](250) NULL,
	[qte_desc10_list] [varchar](250) NULL,
	[qte_desc11_list] [varchar](250) NULL,
	[qte_desc12_list] [varchar](250) NULL,
	[qte_desc13_list] [varchar](250) NULL,
	[qte_desc14_list] [varchar](250) NULL,
	[qte_desc15_list] [varchar](250) NULL,
	[qte_desc16_list] [varchar](250) NULL,
	[Allow_RePrtGRN] [bit] NOT NULL,
	[eng_control37] [tinyint] NOT NULL,
	[IsAssignClose] [tinyint] NOT NULL,
	[IsVisibleCalcBtn] [tinyint] NOT NULL,
	[eng_control36] [tinyint] NOT NULL,
	[StkOutForm] [tinyint] NOT NULL,
	[strStkOutForms] [varchar](100) NULL,
	[strStkOutFormDesc] [varchar](100) NULL,
	[IsModifyDate] [tinyint] NOT NULL,
	[IsIncluGroup] [tinyint] NOT NULL,
	[SALES_CTRL_SMP_PRICECHARGE_ZERO] [tinyint] NOT NULL,
	[crp_control1] [tinyint] NOT NULL,
	[mi_spc_1_list] [varchar](500) NULL,
	[mi_spc_2_list] [varchar](200) NULL,
	[mi_spc_3_list] [varchar](200) NULL,
	[mi_spc_4_list] [varchar](2000) NULL,
	[mi_spc_5_list] [varchar](200) NULL,
	[mi_spc_6_list] [varchar](300) NULL,
	[big_dept_flag] [tinyint] NULL,
	[ecn_list_number] [int] NOT NULL,
	[top_layer_flag] [tinyint] NOT NULL,
	[CUT_SET_TITLE1] [varchar](20) NULL,
	[CUT_SET_TITLE2] [varchar](20) NULL,
	[CUT_SET_TITLE3] [varchar](20) NULL,
	[CUT_SET_TITLE4] [varchar](20) NULL,
	[CUT_L1_MAX3] [numeric](7, 1) NULL,
	[CUT_L1_MIN3] [numeric](7, 1) NULL,
	[CUT_W1_MAX3] [numeric](7, 1) NULL,
	[CUT_W1_MIN3] [numeric](7, 1) NULL,
	[CUT_L2_MAX3] [numeric](7, 1) NULL,
	[CUT_L2_MIN3] [numeric](7, 1) NULL,
	[CUT_W2_MAX3] [numeric](7, 1) NULL,
	[CUT_W2_MIN3] [numeric](7, 1) NULL,
	[Border_L_Min3] [numeric](7, 1) NULL,
	[Border_W_Min3] [numeric](7, 1) NULL,
	[SPACE_L_Min3] [numeric](7, 1) NULL,
	[SPACE_W_Min3] [numeric](7, 1) NULL,
	[CUT_L1_MAX4] [numeric](7, 1) NULL,
	[CUT_L1_MIN4] [numeric](7, 1) NULL,
	[CUT_W1_MAX4] [numeric](7, 1) NULL,
	[CUT_W1_MIN4] [numeric](7, 1) NULL,
	[CUT_L2_MAX4] [numeric](7, 1) NULL,
	[CUT_L2_MIN4] [numeric](7, 1) NULL,
	[CUT_W2_MAX4] [numeric](7, 1) NULL,
	[CUT_W2_MIN4] [numeric](7, 1) NULL,
	[Border_L_Min4] [numeric](7, 1) NULL,
	[Border_W_Min4] [numeric](7, 1) NULL,
	[SPACE_L_Min4] [numeric](7, 1) NULL,
	[SPACE_W_Min4] [numeric](7, 1) NULL,
	[SALES_CHG_PYTYPE_FLAG] [tinyint] NOT NULL,
	[SALES_TOAUTH_CONF_FLAG] [tinyint] NOT NULL,
	[CUT_L1_MAX2] [numeric](7, 1) NULL,
	[CUT_L1_MIN2] [numeric](7, 1) NULL,
	[CUT_W1_MAX2] [numeric](7, 1) NULL,
	[CUT_W1_MIN2] [numeric](7, 1) NULL,
	[CUT_L2_MAX2] [numeric](7, 1) NULL,
	[CUT_L2_MIN2] [numeric](7, 1) NULL,
	[CUT_W2_MAX2] [numeric](7, 1) NULL,
	[CUT_W2_MIN2] [numeric](7, 1) NULL,
	[Border_L_Min2] [numeric](7, 1) NULL,
	[Border_W_Min2] [numeric](7, 1) NULL,
	[SPACE_L_Min2] [numeric](7, 1) NULL,
	[SPACE_W_Min2] [numeric](7, 1) NULL,
	[IsVisibleSupplyer] [tinyint] NOT NULL,
	[VisibleSelfDepartment] [tinyint] NOT NULL,
	[ChangeAcceptDept] [tinyint] NOT NULL,
	[AssignSoNo] [tinyint] NOT NULL,
	[RandomJump] [tinyint] NOT NULL,
	[OnlyDateCode] [tinyint] NOT NULL,
	[D700_AllAuth] [bit] NOT NULL,
	[SALES_LASTEST_FOB_FLAG] [tinyint] NOT NULL,
	[EP266_CONTROL] [tinyint] NOT NULL,
	[EP017_CONTROL] [tinyint] NOT NULL,
	[SALE_PRINT_CONTROL] [tinyint] NOT NULL,
	[REVIEW_FLAG] [tinyint] NOT NULL,
	[QTE_CONTROL6] [tinyint] NOT NULL,
	[APPLY_FLAG] [bit] NOT NULL,
	[EVALUE_DATE_TIME] [varchar](4) NOT NULL,
	[SCH_DATE_LEAD_DAYS] [tinyint] NULL,
	[LOADQTYMORETHANACTIVEQTYCANNEXT] [bit] NULL,
	[OnlyApplySelfDept] [tinyint] NOT NULL,
	[ANALYSIS_CODE_1] [varchar](20) NULL,
	[ANALYSIS_CODE_2] [varchar](20) NULL,
	[ANALYSIS_CODE_3] [varchar](20) NULL,
	[ANALYSIS_CODE_4] [varchar](20) NULL,
	[ANALYSIS_CODE_5] [varchar](20) NULL,
	[ISSTOCKDATACTRLS] [bit] NOT NULL,
	[ShowOutListDetail] [tinyint] NOT NULL,
	[PPC_POPriceUPCtrls] [bit] NULL,
	[ifreturncheck] [tinyint] NULL,
	[SUPLSTAMAT2] [char](1) NOT NULL,
	[CUSTFEDSHIP_loss] [int] NULL,
	[inv_alter_if] [int] NULL,
	[AcceptAlterMaterial] [tinyint] NOT NULL,
	[ANALYSIS_CODE_6] [varchar](20) NULL,
	[VisibleSelfPoBuyer] [tinyint] NOT NULL,
	[mattype_bind_buyer] [tinyint] NOT NULL,
	[Pur_Auto_Appro] [tinyint] NOT NULL,
	[IsHubCant] [tinyint] NOT NULL,
	[PO_PRICEUP_PERCENT] [bit] NULL,
	[PO_PRICEUP_PERCENTVALUE] [decimal](16, 2) NULL,
	[PO_MATTYPEPO_value_flag] [bit] NULL,
	[PO_MATTYPEPO_LIST_flag] [bit] NULL,
	[PO_MATTYPEPO_LIST] [varchar](100) NULL,
	[PO_MATTYPEPO_Deiail_flag] [bit] NULL,
	[PO_MATTYPEPO_CONFIRT_flag] [bit] NULL,
	[PO_MATTYPE_value_flag] [bit] NULL,
	[PO_MATTYPE_LIST_flag] [bit] NULL,
	[PO_MATTYPE_LIST] [varchar](100) NULL,
	[PO_MATTYPEPO_CONTROL_NUMBER] [tinyint] NOT NULL,
	[PO_MATTYPEPO_LIST_NUMBER] [varchar](50) NULL,
	[PO_AllowPoAdjust] [tinyint] NOT NULL,
	[PrBind_Byer] [tinyint] NOT NULL,
	[PrBind_Byer0] [tinyint] NOT NULL,
	[PrBind_Byer1] [tinyint] NOT NULL,
	[PrBind_Byer2] [tinyint] NOT NULL,
	[PrBind_Byera] [tinyint] NOT NULL,
	[PrBind_Byerb] [tinyint] NOT NULL,
	[PrBind_Byerc] [tinyint] NOT NULL,
	[reply_datedata] [tinyint] NOT NULL,
	[CUST_PO_PATH] [varchar](500) NULL,
	[close_PRPOPrice] [int] NOT NULL,
	[PO_PRINTMANU_COUNT] [int] NOT NULL,
	[buy_number_YearMonth] [tinyint] NOT NULL,
	[po_number_YearMonth] [tinyint] NOT NULL,
	[buy_reason_AllAlter] [tinyint] NOT NULL,
	[buy_MATTYPE_AllAlter] [tinyint] NOT NULL,
	[POANALYSIS_CODE45_TODATA23] [tinyint] NOT NULL,
	[buy_reason_black] [tinyint] NOT NULL,
	[caneditprice] [int] NOT NULL,
	[send_messageToConf_flag] [tinyint] NOT NULL,
	[po_inport_NotAllowMatVor] [tinyint] NOT NULL,
	[SHIPPING_METHOD] [varchar](50) NULL,
	[PoPr_Factory_Default] [varchar](24) NULL,
	[PoPr_Factory_allowMaterial] [tinyint] NOT NULL,
	[po_Billnumber_allowAlater] [tinyint] NOT NULL,
	[allow_alter_EP075209] [tinyint] NOT NULL,
	[allow_Expand_EP075209] [tinyint] NOT NULL,
	[allow_delete_EP075209] [tinyint] NOT NULL,
	[Po_Parch_FtpUp] [tinyint] NOT NULL,
	[Po_Import_Merge] [tinyint] NOT NULL,
	[Pur_Expand_AuditMan] [tinyint] NOT NULL,
	[Pur_Expire_AuditMan] [tinyint] NOT NULL,
	[poFbz_inport_NotAllowMatVor] [tinyint] NOT NULL,
	[NoStand_ReasonBlack] [tinyint] NOT NULL,
	[FaxVerdonNoStandPo] [tinyint] NOT NULL,
	[Po_Approv_MyBill] [tinyint] NOT NULL,
	[Po_Print_continuous] [tinyint] NOT NULL,
	[Po_Period_ValidDay] [tinyint] NOT NULL,
	[request_approve_AlterNum] [tinyint] NOT NULL,
	[request_Dept_SpecialM] [tinyint] NOT NULL,
	[po_stock] [tinyint] NOT NULL,
	[po_ZaiTSl] [tinyint] NOT NULL,
	[po_avgqty] [tinyint] NOT NULL,
	[po_oneQty] [tinyint] NOT NULL,
	[Po_Approv_AlterMemo] [tinyint] NOT NULL,
	[add_statusDShenhe_EP075209] [tinyint] NOT NULL,
	[allow_RejRespEdit_EP075209] [tinyint] NOT NULL,
	[allow_RejRespBlack_EP075209] [tinyint] NOT NULL,
	[AcceptAlterQuantity] [tinyint] NOT NULL,
	[Pur_Expire_SubmitAuth] [tinyint] NOT NULL,
	[PoBill_tblAdd_Hide] [tinyint] NOT NULL,
	[non_payment_credit_flag] [int] NOT NULL,
	[PO_AlterCurrency] [int] NOT NULL,
	[ZzpPd_EP209_075] [int] NOT NULL,
	[vendor_Appro_Nvalid] [int] NOT NULL,
	[Purchase_PO_PATH] [varchar](120) NULL,
	[Confirm_AcpPurchase_Detial] [int] NOT NULL,
	[Pur_TwoClass_Appro] [int] NOT NULL,
	[MakePoNumber01] [int] NOT NULL,
	[AdressP1] [varchar](8) NULL,
	[AdressP2] [varchar](8) NULL,
	[Accept_Purchase_Detial] [int] NOT NULL,
	[PO_MATTYPEPO_Fatch_MatIDCode] [tinyint] NOT NULL,
	[PO_MATTYPEPO_LIST_Report] [varchar](80) NULL,
	[PO_MATTYPEPO_LIST_ReportO] [varchar](80) NULL,
	[PO_MATPO_LIST_ReportFlag] [tinyint] NOT NULL,
	[SALES_COPI02_VER_FLAG] [tinyint] NOT NULL,
	[Lldept] [tinyint] NOT NULL,
	[qte_desc17] [varchar](50) NULL,
	[qte_desc18] [varchar](50) NULL,
	[qte_desc19] [varchar](50) NULL,
	[qte_desc20] [varchar](50) NULL,
	[qte_desc21] [varchar](50) NULL,
	[qte_desc22] [varchar](50) NULL,
	[qte_desc23] [varchar](50) NULL,
	[qte_desc24] [varchar](50) NULL,
	[qte_desc25] [varchar](50) NULL,
	[qte_desc26] [varchar](50) NULL,
	[company_made_key] [int] NOT NULL,
	[ECNFILE_LOCATION] [varchar](100) NULL,
	[voucher_billsn_flag] [tinyint] NOT NULL,
	[wip_flow_flag] [tinyint] NOT NULL,
	[Check_initialize_flag] [tinyint] NOT NULL,
	[PWtrans_FLAG] [bit] NOT NULL,
	[CREDIT_BEGIN_DATE] [datetime] NOT NULL,
	[monthly_statement] [tinyint] NOT NULL,
	[Document_time] [tinyint] NOT NULL,
	[isCostaccounting] [tinyint] NOT NULL,
	[NewSys_ExcelToFile] [int] NOT NULL,
	[ap_check_method] [tinyint] NOT NULL,
	[ar_check_method] [tinyint] NOT NULL,
	[Analysis_code_custom_1] [varchar](100) NULL,
	[Analysis_code_custom_2] [varchar](100) NULL,
	[Analysis_code_custom_3] [varchar](100) NULL,
	[Analysis_code_custom_4] [varchar](100) NULL,
	[Analysis_code_custom_5] [varchar](100) NULL,
	[outer_hair] [tinyint] NOT NULL,
	[The_last_price] [tinyint] NOT NULL,
	[BCODE10_Whouse_Flag] [bit] NOT NULL,
	[BCODE10_Whole_Bak_Flag] [bit] NOT NULL,
	[BCODE10_Month_Add_Flag] [bit] NOT NULL,
	[BCODE10_YYMM_Flag] [int] NOT NULL,
	[EP882_Stock_Flag] [bit] NOT NULL,
	[D354_commit_flag] [bit] NOT NULL,
	[Pack_Value1] [varchar](200) NULL,
	[Pack_Weight_Flag] [bit] NOT NULL,
	[EP200A_EP882_Flag] [bit] NOT NULL,
	[BCODE03_Number_Flag] [bit] NOT NULL,
	[BCODE03_EP882_Flag] [bit] NOT NULL,
	[BCODE03_Message_Flag] [bit] NOT NULL,
	[EP877_Bag_Flag] [bit] NULL,
	[EP877_Customer_Flag] [bit] NOT NULL,
	[output_count_day] [int] NOT NULL,
	[Payment_method] [varchar](200) NULL,
	[Pack_Remark1] [varchar](200) NULL,
	[Pack_Remark2] [varchar](200) NULL,
	[Pack_Remark3] [varchar](200) NULL,
	[Pack_Remark4] [varchar](200) NULL,
	[Pack_Remark5] [varchar](200) NULL,
	[Pack_Remark6] [varchar](200) NULL,
	[Mod_Pack] [varchar](600) NULL,
	[QTYPACK_NOT_SCRAP] [int] NOT NULL,
	[cust_private_flag] [tinyint] NOT NULL,
	[EP877_Sales_WoDate_Flag] [bit] NOT NULL,
	[EP877_Pack] [varchar](600) NULL,
	[FCB_mod] [int] NOT NULL,
	[Group_mode_flag] [tinyint] NOT NULL,
	[sys_ttype] [tinyint] NOT NULL,
	[Production_list_print] [tinyint] NOT NULL,
	[cut_setspcs_readonly] [tinyint] NOT NULL,
	[eq_email_flag] [tinyint] NOT NULL,
	[SUPLFEDport] [int] NULL,
	[MI_font_size] [tinyint] NOT NULL,
	[PcbCutMode] [tinyint] NOT NULL,
	[ENGEQ_LOCATION] [varchar](100) NULL,
	[eq_template_flag] [tinyint] NOT NULL,
	[CUTTEXTLIST] [varchar](300) NULL,
	[SETWO_SPLIT_TYPE] [tinyint] NOT NULL,
	[subcon_cost_flag] [int] NOT NULL,
	[voucher_billsn_check] [int] NOT NULL,
	[INVI66buildinstock] [int] NOT NULL,
	[voucher_audit_same] [int] NOT NULL,
	[spec_flag] [int] NOT NULL,
	[Group_cost_flag] [int] NOT NULL,
	[TRANS_WFSTEP_CHECK] [tinyint] NOT NULL,
	[TRANS_ORDER_NOTIN_WF] [tinyint] NOT NULL,
	[trans_input_check] [tinyint] NOT NULL,
	[trans_times] [tinyint] NOT NULL,
	[Barcode_Trans_MI] [tinyint] NOT NULL,
	[MFIFO_FLAG] [tinyint] NOT NULL,
	[rptopo_itemjoin] [int] NOT NULL,
	[StockDataCtrls_old] [bit] NOT NULL,
	[outorderstatus] [tinyint] NOT NULL,
	[poreq_status_flag] [tinyint] NOT NULL,
	[ENGMI_GERBER_FLAG] [tinyint] NOT NULL,
	[Group_Distri_flag] [tinyint] NOT NULL,
	[PTHNULL_NOTOPERATE] [tinyint] NOT NULL,
	[CUT_AREA_FLAG] [tinyint] NOT NULL,
	[ENG_CUSTCODE_SEARCH] [tinyint] NOT NULL,
	[Po_Import_MultiVendor] [int] NOT NULL,
	[Po_Import_ChoosePoDate] [int] NOT NULL,
	[PO_BackPro_MEGUser] [int] NOT NULL,
	[PO_BackPro_MEGApproBefor] [int] NOT NULL,
	[PO_Back_MEGUser] [int] NOT NULL,
	[PO_Back_MEGApproBefor] [int] NOT NULL,
	[po_amount_PerSign] [int] NOT NULL,
	[NotAccept_Topo_ep054] [tinyint] NOT NULL,
	[MoreAudit_EP223] [int] NOT NULL,
	[Ifshow_overdueProd] [int] NOT NULL,
	[fg_scp_dept_ptrG] [varchar](32) NULL,
	[EP781_Whouse_Flag] [bit] NOT NULL,
	[EP781_Month_Add_Flag] [bit] NOT NULL,
	[EP200A_Number_Flag] [bit] NULL,
	[DeletePr_Owner] [int] NOT NULL,
	[CUSTFEDSHIP_loss2] [int] NULL,
	[ck_WaitAudit] [int] NOT NULL,
	[ck_Recived] [int] NOT NULL,
	[ck_Save] [int] NOT NULL,
	[ck_PutOff] [int] NOT NULL,
	[ck_NotPresent] [int] NOT NULL,
	[ck_Finshed] [int] NOT NULL,
	[ck_cancel] [int] NOT NULL,
	[ck_Backed] [int] NOT NULL,
	[ck_Audit] [int] NOT NULL,
	[buy_MATTYPE_BlackNoToPo] [int] NOT NULL,
	[Bill_OwnerFactory_stock] [int] NOT NULL,
	[Bak_Remark2] [varchar](500) NULL,
	[Bak_Remark1] [varchar](500) NULL,
	[AddPro_Facth_Vendor] [int] NOT NULL,
	[BatchSDefect_EP075209] [tinyint] NOT NULL,
	[AddLineCHeck_EP075209] [int] NOT NULL,
	[AddLineCHeck_EP056] [int] NOT NULL,
	[AddLineCHeck_EP051] [int] NOT NULL,
	[Add_FaultNo_PoBill] [int] NOT NULL,
	[Add_Black_PoBill] [int] NOT NULL,
	[add_AutoShenhePnl_EP075209] [decimal](18, 6) NOT NULL,
	[add_AutoShenhe_EP075209] [tinyint] NOT NULL,
	[MOUT_DEPT_FLAG] [tinyint] NOT NULL,
	[assign_delivery_no_flag] [tinyint] NOT NULL,
	[SALES_COPI23_VER_FLAG] [tinyint] NOT NULL,
	[program_menu_vflag] [tinyint] NOT NULL,
	[EditInvtCheck] [int] NOT NULL,
	[costing_method] [bit] NOT NULL,
	[scrap_cost_flag] [tinyint] NOT NULL,
	[rework_cost_flag] [tinyint] NOT NULL,
	[Group_account_flag] [tinyint] NOT NULL,
	[first_account_type] [tinyint] NOT NULL,
	[qte_rkey_27] [int] NULL,
	[UT_L2_MIN2] [numeric](18, 5) NULL,
	[ap_check_in_flag] [tinyint] NOT NULL,
	[COPI04_SEND_MSG] [int] NOT NULL,
	[MI_RSPEC_FLAG_99] [bit] NOT NULL,
	[mi_rspec_option_99] [bit] NOT NULL,
	[mi_rspec_option_90] [bit] NOT NULL,
	[mi_rspec_option_100] [bit] NOT NULL,
	[mi_rspec_option_110] [bit] NOT NULL,
	[mi_rspec_option_120] [bit] NOT NULL,
	[mi_rspec_option_200] [bit] NOT NULL,
	[mi_rspec_option_91] [bit] NOT NULL,
	[MI_RSPEC_FLAG_91] [bit] NOT NULL,
	[mi_rspec_option_101] [bit] NOT NULL,
	[MI_RSPEC_OPTION_35] [bit] NOT NULL,
	[surplus_QUAN_ON_HAND] [tinyint] NOT NULL,
	[prodout_flag] [tinyint] NOT NULL,
	[CUST_CODE_FLAG] [tinyint] NULL,
	[drillslot_itemcount] [tinyint] NOT NULL,
	[IdNo_flag] [tinyint] NOT NULL,
	[DefaultFlowByCust] [tinyint] NOT NULL,
	[drillslot_diacount] [tinyint] NOT NULL,
	[REQ_COMPLETE_DATE_FLAG] [tinyint] NOT NULL,
	[FILE_NAME_FLAG] [tinyint] NOT NULL,
 CONSTRAINT [PK_DATA0192] PRIMARY KEY CLUSTERED 
(
	[RKEY] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__SALES___48FF7F11]  DEFAULT ((0)) FOR [SALES_EP032_VER_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__RTN_SO__191B61C5]  DEFAULT ((0)) FOR [RTN_SO]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__QA_CON__31E70F8F]  DEFAULT ((1)) FOR [QA_CONTROL2]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__YearNu__32DB33C8]  DEFAULT ((2)) FOR [YearNumber]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__Pur_Co__40352EE6]  DEFAULT ((0)) FOR [Pur_Cons_Auth]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__WF_Loc__1CB6E87F]  DEFAULT ((1)) FOR [WF_Local_Auth_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__WF_Cro__1DAB0CB8]  DEFAULT ((0)) FOR [WF_Cross_EXDept]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__GoodTi__264052B9]  DEFAULT ((0)) FOR [GoodTimeLimit]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__waifa___273476F2]  DEFAULT ((80)) FOR [waifa_rate]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ppc_co__28289B2B]  DEFAULT ((0)) FOR [ppc_control30]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__RTN_SO__6531A96B]  DEFAULT ((0)) FOR [RTN_SO_NOFLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__FIN_CO__79C2B1FD]  DEFAULT ((1)) FOR [FIN_CONTROL6]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__qte_ex__09F919C6]  DEFAULT ((60)) FOR [qte_expire_days]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__PCBCUT__0AED3DFF]  DEFAULT ((0)) FOR [PCBCUT_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__qte_un__0BE16238]  DEFAULT ((0)) FOR [qte_unit]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__Mtr_RT__4F976E07]  DEFAULT ((1)) FOR [Mtr_RTUser_ControlType]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__eng_co__508B9240]  DEFAULT ((0)) FOR [eng_control34]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ppc_co__517FB679]  DEFAULT ((0)) FOR [ppc_control31]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__eng_co__5273DAB2]  DEFAULT ((0)) FOR [eng_control35]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__PWConv__7899839A]  DEFAULT ((0)) FOR [PWConvert_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__stock___7C6A147E]  DEFAULT ((1)) FOR [stock_initialized]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ppc_co__7D5E38B7]  DEFAULT ((0)) FOR [ppc_control34]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__eng_co__7E525CF0]  DEFAULT ((0)) FOR [eng_control38]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ppc_co__7F468129]  DEFAULT ((0)) FOR [ppc_control33]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__GR_NoC__003AA562]  DEFAULT ((1)) FOR [GR_NoCheck]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__PO_All__012EC99B]  DEFAULT ((0)) FOR [PO_AllowJumpAudit]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__PR_Pri__0222EDD4]  DEFAULT ((0)) FOR [PR_PriceMode]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ppc_co__0317120D]  DEFAULT ((0)) FOR [ppc_control32]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__pcbcut__040B3646]  DEFAULT ((0)) FOR [pcbcut_pnl_type]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ecn_co__04FF5A7F]  DEFAULT ((0)) FOR [ecn_control2]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ECN_CO__05F37EB8]  DEFAULT ((1)) FOR [ECN_CONTROL1]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__Allow___06E7A2F1]  DEFAULT ((0)) FOR [Allow_RePrtGRN]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__eng_co__07DBC72A]  DEFAULT ((0)) FOR [eng_control37]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__IsAssi__08CFEB63]  DEFAULT ((1)) FOR [IsAssignClose]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__IsVisi__09C40F9C]  DEFAULT ((0)) FOR [IsVisibleCalcBtn]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__eng_co__0AB833D5]  DEFAULT ((0)) FOR [eng_control36]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__StkOut__0BAC580E]  DEFAULT ((0)) FOR [StkOutForm]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__IsModi__0CA07C47]  DEFAULT ((0)) FOR [IsModifyDate]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__IsIncl__0D94A080]  DEFAULT ((0)) FOR [IsIncluGroup]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__SALES___0E88C4B9]  DEFAULT ((0)) FOR [SALES_CTRL_SMP_PRICECHARGE_ZERO]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__crp_co__284896BC]  DEFAULT ((0)) FOR [crp_control1]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__big_de__1F493C67]  DEFAULT ((0)) FOR [big_dept_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ecn_li__429278A4]  DEFAULT ((2)) FOR [ecn_list_number]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__top_la__456EE54F]  DEFAULT ((0)) FOR [top_layer_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__SALES___46630988]  DEFAULT ((0)) FOR [SALES_CHG_PYTYPE_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__SALES___47572DC1]  DEFAULT ((0)) FOR [SALES_TOAUTH_CONF_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__IsVisi__484B51FA]  DEFAULT ((1)) FOR [IsVisibleSupplyer]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__Visibl__493F7633]  DEFAULT ((1)) FOR [VisibleSelfDepartment]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__Change__4A339A6C]  DEFAULT ((0)) FOR [ChangeAcceptDept]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__Assign__4B27BEA5]  DEFAULT ((0)) FOR [AssignSoNo]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__Random__4C1BE2DE]  DEFAULT ((0)) FOR [RandomJump]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__OnlyDa__37DFE007]  DEFAULT ((0)) FOR [OnlyDateCode]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__D700_A__5FB8C737]  DEFAULT ((1)) FOR [D700_AllAuth]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__SALES___2A90D1E1]  DEFAULT ((0)) FOR [SALES_LASTEST_FOB_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__EP266___2B84F61A]  DEFAULT ((0)) FOR [EP266_CONTROL]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__EP017___2C791A53]  DEFAULT ((0)) FOR [EP017_CONTROL]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__SALE_P__2D6D3E8C]  DEFAULT ((0)) FOR [SALE_PRINT_CONTROL]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__REVIEW__2E6162C5]  DEFAULT ((0)) FOR [REVIEW_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__QTE_CO__2F5586FE]  DEFAULT ((0)) FOR [QTE_CONTROL6]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__APPLY___3049AB37]  DEFAULT ((0)) FOR [APPLY_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__EVALUE__313DCF70]  DEFAULT ('0830') FOR [EVALUE_DATE_TIME]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__DATA0192__LOADQT__67C4C8AA]  DEFAULT ((0)) FOR [LOADQTYMORETHANACTIVEQTYCANNEXT]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__OnlyAp__1E40D4EC]  DEFAULT ((0)) FOR [OnlyApplySelfDept]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF_Data0192_ISSTOCKDATACTRLS]  DEFAULT ((0)) FOR [ISSTOCKDATACTRLS]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__data0192__ShowOu__2B50B747]  DEFAULT ((0)) FOR [ShowOutListDetail]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ifretu__1E8BB906]  DEFAULT ((0)) FOR [ifreturncheck]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__SUPLST__67FA9E2B]  DEFAULT ('Y') FOR [SUPLSTAMAT2]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Accept__33A79CD4]  DEFAULT ((0)) FOR [AcceptAlterMaterial]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Visibl__5FDB24CD]  DEFAULT ((0)) FOR [VisibleSelfPoBuyer]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mattyp__60CF4906]  DEFAULT ((0)) FOR [mattype_bind_buyer]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Pur_Au__01B119E4]  DEFAULT ((0)) FOR [Pur_Auto_Appro]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__IsHubC__381021F8]  DEFAULT ((0)) FOR [IsHubCant]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_PRI__1359F94D]  DEFAULT ((0)) FOR [PO_PRICEUP_PERCENT]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_PRI__144E1D86]  DEFAULT ((0)) FOR [PO_PRICEUP_PERCENTVALUE]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__154241BF]  DEFAULT ((0)) FOR [PO_MATTYPEPO_value_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__163665F8]  DEFAULT ((0)) FOR [PO_MATTYPEPO_LIST_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__172A8A31]  DEFAULT ((0)) FOR [PO_MATTYPEPO_Deiail_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__181EAE6A]  DEFAULT ((0)) FOR [PO_MATTYPEPO_CONFIRT_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__1912D2A3]  DEFAULT ((0)) FOR [PO_MATTYPE_value_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__1A06F6DC]  DEFAULT ((0)) FOR [PO_MATTYPE_LIST_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__0947AE19]  DEFAULT ((0)) FOR [PO_MATTYPEPO_CONTROL_NUMBER]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_All__0D183EFD]  DEFAULT ((0)) FOR [PO_AllowPoAdjust]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PrBind__0FF4ABA8]  DEFAULT ((0)) FOR [PrBind_Byer]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PrBind__10E8CFE1]  DEFAULT ((1)) FOR [PrBind_Byer0]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PrBind__11DCF41A]  DEFAULT ((0)) FOR [PrBind_Byer1]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PrBind__12D11853]  DEFAULT ((0)) FOR [PrBind_Byer2]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PrBind__13C53C8C]  DEFAULT ((0)) FOR [PrBind_Byera]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PrBind__14B960C5]  DEFAULT ((0)) FOR [PrBind_Byerb]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PrBind__15AD84FE]  DEFAULT ((0)) FOR [PrBind_Byerc]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__reply___1889F1A9]  DEFAULT ((0)) FOR [reply_datedata]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__close___197E15E2]  DEFAULT ((0)) FOR [close_PRPOPrice]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_PRI__1A723A1B]  DEFAULT ((0)) FOR [PO_PRINTMANU_COUNT]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__buy_nu__01B4A6A5]  DEFAULT ((0)) FOR [buy_number_YearMonth]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_num__02A8CADE]  DEFAULT ((0)) FOR [po_number_YearMonth]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__buy_re__039CEF17]  DEFAULT ((0)) FOR [buy_reason_AllAlter]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__buy_MA__04911350]  DEFAULT ((0)) FOR [buy_MATTYPE_AllAlter]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__POANAL__05853789]  DEFAULT ((0)) FOR [POANALYSIS_CODE45_TODATA23]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__buy_re__06795BC2]  DEFAULT ((0)) FOR [buy_reason_black]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__canedi__3B23C4D9]  DEFAULT ((0)) FOR [caneditprice]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__send_m__3C835E1B]  DEFAULT ((0)) FOR [send_messageToConf_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_inp__3E6BA68D]  DEFAULT ((1)) FOR [po_inport_NotAllowMatVor]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PoPr_F__3F5FCAC6]  DEFAULT ((0)) FOR [PoPr_Factory_allowMaterial]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_Bil__423C3771]  DEFAULT ((0)) FOR [po_Billnumber_allowAlater]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__allow___161467C7]  DEFAULT ((0)) FOR [allow_alter_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__allow___17088C00]  DEFAULT ((0)) FOR [allow_Expand_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__allow___17FCB039]  DEFAULT ((0)) FOR [allow_delete_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_Par__10284CBF]  DEFAULT ((0)) FOR [Po_Parch_FtpUp]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_Imp__111C70F8]  DEFAULT ((0)) FOR [Po_Import_Merge]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Pur_Ex__12109531]  DEFAULT ((0)) FOR [Pur_Expand_AuditMan]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Pur_Ex__1304B96A]  DEFAULT ((0)) FOR [Pur_Expire_AuditMan]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__poFbz___14ED01DC]  DEFAULT ((1)) FOR [poFbz_inport_NotAllowMatVor]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__NoStan__1D01F1D6]  DEFAULT ((0)) FOR [NoStand_ReasonBlack]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__FaxVer__1DF6160F]  DEFAULT ((0)) FOR [FaxVerdonNoStandPo]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_App__20D282BA]  DEFAULT ((0)) FOR [Po_Approv_MyBill]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_Pri__22BACB2C]  DEFAULT ((0)) FOR [Po_Print_continuous]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_Per__23AEEF65]  DEFAULT ((2)) FOR [Po_Period_ValidDay]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__reques__61DB627F]  DEFAULT ((0)) FOR [request_approve_AlterNum]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__reques__67943BD5]  DEFAULT ((0)) FOR [request_Dept_SpecialM]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_sto__697C8447]  DEFAULT ((1)) FOR [po_stock]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_Zai__6A70A880]  DEFAULT ((1)) FOR [po_ZaiTSl]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_avg__6B64CCB9]  DEFAULT ((1)) FOR [po_avgqty]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_one__6C58F0F2]  DEFAULT ((1)) FOR [po_oneQty]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_App__6D4D152B]  DEFAULT ((0)) FOR [Po_Approv_AlterMemo]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__add_st__58C6F991]  DEFAULT ((0)) FOR [add_statusDShenhe_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__allow___59BB1DCA]  DEFAULT ((0)) FOR [allow_RejRespEdit_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__allow___5AAF4203]  DEFAULT ((0)) FOR [allow_RejRespBlack_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Accept__5C978A75]  DEFAULT ((0)) FOR [AcceptAlterQuantity]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Pur_Ex__5D8BAEAE]  DEFAULT ((0)) FOR [Pur_Expire_SubmitAuth]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PoBill__0CA132B3]  DEFAULT ((0)) FOR [PoBill_tblAdd_Hide]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__non_pa__557882FD]  DEFAULT ((0)) FOR [non_payment_credit_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_Alt__566CA736]  DEFAULT ((0)) FOR [PO_AlterCurrency]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ZzpPd___5760CB6F]  DEFAULT ((0)) FOR [ZzpPd_EP209_075]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__vendor__60EA35A9]  DEFAULT ((0)) FOR [vendor_Appro_Nvalid]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Confir__61DE59E2]  DEFAULT ((0)) FOR [Confirm_AcpPurchase_Detial]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Pur_Tw__62D27E1B]  DEFAULT ((0)) FOR [Pur_TwoClass_Appro]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MakePo__63C6A254]  DEFAULT ((0)) FOR [MakePoNumber01]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Accept__64BAC68D]  DEFAULT ((0)) FOR [Accept_Purchase_Detial]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__66A30EFF]  DEFAULT ((0)) FOR [PO_MATTYPEPO_Fatch_MatIDCode]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_MAT__67973338]  DEFAULT ((0)) FOR [PO_MATPO_LIST_ReportFlag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__SALES___011116F5]  DEFAULT ((0)) FOR [SALES_COPI02_VER_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Lldept__2148DC5D]  DEFAULT ((0)) FOR [Lldept]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__compan__223E9744]  DEFAULT ((0)) FOR [company_made_key]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__vouche__0FC321B2]  DEFAULT ((0)) FOR [voucher_billsn_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__wip_fl__4515FF91]  DEFAULT ((0)) FOR [wip_flow_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Check___047C578F]  DEFAULT ((1)) FOR [Check_initialize_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PWtran__7659EC18]  DEFAULT ((0)) FOR [PWtrans_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__CREDIT__2417607E]  DEFAULT ('2000-01-01') FOR [CREDIT_BEGIN_DATE]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__monthl__3EEC1DA2]  DEFAULT ((0)) FOR [monthly_statement]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Docume__0163EC75]  DEFAULT ((0)) FOR [Document_time]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__isCost__025810AE]  DEFAULT ((0)) FOR [isCostaccounting]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__NewSys__01AED08F]  DEFAULT ((0)) FOR [NewSys_ExcelToFile]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ap_che__39F329B2]  DEFAULT ((0)) FOR [ap_check_method]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ar_che__64C94475]  DEFAULT ((0)) FOR [ar_check_method]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__outer___0A3AC046]  DEFAULT ((1)) FOR [outer_hair]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__The_la__0B2EE47F]  DEFAULT ((0)) FOR [The_last_price]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BCODE1__0E0B512A]  DEFAULT ((0)) FOR [BCODE10_Whouse_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BCODE1__0EFF7563]  DEFAULT ((0)) FOR [BCODE10_Whole_Bak_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BCODE1__0FF3999C]  DEFAULT ((0)) FOR [BCODE10_Month_Add_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BCODE1__10E7BDD5]  DEFAULT ((0)) FOR [BCODE10_YYMM_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__EP882___11DBE20E]  DEFAULT ((0)) FOR [EP882_Stock_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__D354_c__1E41B8F3]  DEFAULT ((0)) FOR [D354_commit_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Pack_W__1F35DD2C]  DEFAULT ((0)) FOR [Pack_Weight_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__EP200A__211E259E]  DEFAULT ((0)) FOR [EP200A_EP882_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BCODE0__221249D7]  DEFAULT ((0)) FOR [BCODE03_Number_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BCODE0__23066E10]  DEFAULT ((0)) FOR [BCODE03_EP882_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BCODE0__24EEB682]  DEFAULT ((0)) FOR [BCODE03_Message_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__EP877___2E7820BC]  DEFAULT ((0)) FOR [EP877_Customer_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__output__5B809CB4]  DEFAULT ((31)) FOR [output_count_day]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__QTYPAC__604A47A5]  DEFAULT ((1)) FOR [QTYPACK_NOT_SCRAP]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__cust_p__5D58CC61]  DEFAULT ((0)) FOR [cust_private_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__EP877___43A4B2AD]  DEFAULT ((1)) FOR [EP877_Sales_WoDate_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__FCB_mo__4498D6E6]  DEFAULT ((0)) FOR [FCB_mod]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Group___4483C84D]  DEFAULT ((0)) FOR [Group_mode_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__sys_tt__6C493792]  DEFAULT ((0)) FOR [sys_ttype]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Produc__466F3E1B]  DEFAULT ((0)) FOR [Production_list_print]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__cut_se__391A6A9B]  DEFAULT ((0)) FOR [cut_setspcs_readonly]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__eq_ema__4BD6D470]  DEFAULT ((0)) FOR [eq_email_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MI_fon__518FADC6]  DEFAULT ((8)) FOR [MI_font_size]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PcbCut__69673757]  DEFAULT ((0)) FOR [PcbCutMode]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__eq_tem__78EC9F65]  DEFAULT ((0)) FOR [eq_template_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__SETWO___7EBF1565]  DEFAULT ((0)) FOR [SETWO_SPLIT_TYPE]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__subcon__2A52AF5A]  DEFAULT ((0)) FOR [subcon_cost_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__vouche__2B46D393]  DEFAULT ((0)) FOR [voucher_billsn_check]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__INVI66__2C3AF7CC]  DEFAULT ((0)) FOR [INVI66buildinstock]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__vouche__2D2F1C05]  DEFAULT ((0)) FOR [voucher_audit_same]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__spec_f__2E23403E]  DEFAULT ((0)) FOR [spec_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Group___2F176477]  DEFAULT ((0)) FOR [Group_cost_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__TRANS___300B88B0]  DEFAULT ((1)) FOR [TRANS_WFSTEP_CHECK]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__TRANS___30FFACE9]  DEFAULT ((1)) FOR [TRANS_ORDER_NOTIN_WF]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__trans___3994F2EA]  DEFAULT ((0)) FOR [trans_input_check]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__trans___3C715F95]  DEFAULT ((1)) FOR [trans_times]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Barcod__47E31241]  DEFAULT ((1)) FOR [Barcode_Trans_MI]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MFIFO___4238533F]  DEFAULT ((0)) FOR [MFIFO_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__rptopo__432C7778]  DEFAULT ((0)) FOR [rptopo_itemjoin]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__StockD__4608E423]  DEFAULT ((0)) FOR [StockDataCtrls_old]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__outord__47F12C95]  DEFAULT ((0)) FOR [outorderstatus]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__poreq___526EBB08]  DEFAULT ((0)) FOR [poreq_status_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ENGMI___1710C3B1]  DEFAULT ((0)) FOR [ENGMI_GERBER_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Group___538042D4]  DEFAULT ((1)) FOR [Group_Distri_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PTHNUL__3732E0F3]  DEFAULT ((0)) FOR [PTHNULL_NOTOPERATE]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__CUT_AR__3827052C]  DEFAULT ((0)) FOR [CUT_AREA_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ENG_CU__7C7CFAD0]  DEFAULT ((0)) FOR [ENG_CUSTCODE_SEARCH]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_Imp__515D92A1]  DEFAULT ((0)) FOR [Po_Import_MultiVendor]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Po_Imp__5251B6DA]  DEFAULT ((0)) FOR [Po_Import_ChoosePoDate]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_Bac__5345DB13]  DEFAULT ((0)) FOR [PO_BackPro_MEGUser]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_Bac__5439FF4C]  DEFAULT ((0)) FOR [PO_BackPro_MEGApproBefor]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_Bac__552E2385]  DEFAULT ((0)) FOR [PO_Back_MEGUser]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__PO_Bac__562247BE]  DEFAULT ((0)) FOR [PO_Back_MEGApproBefor]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__po_amo__57166BF7]  DEFAULT ((0)) FOR [po_amount_PerSign]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__NotAcc__580A9030]  DEFAULT ((0)) FOR [NotAccept_Topo_ep054]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MoreAu__58FEB469]  DEFAULT ((0)) FOR [MoreAudit_EP223]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Ifshow__59F2D8A2]  DEFAULT ((0)) FOR [Ifshow_overdueProd]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__EP781___5AE6FCDB]  DEFAULT ((0)) FOR [EP781_Whouse_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__EP781___5BDB2114]  DEFAULT ((0)) FOR [EP781_Month_Add_Flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Delete__5CCF454D]  DEFAULT ((0)) FOR [DeletePr_Owner]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Wai__5DC36986]  DEFAULT ((1)) FOR [ck_WaitAudit]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Rec__5EB78DBF]  DEFAULT ((0)) FOR [ck_Recived]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Sav__5FABB1F8]  DEFAULT ((0)) FOR [ck_Save]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Put__609FD631]  DEFAULT ((0)) FOR [ck_PutOff]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Not__6193FA6A]  DEFAULT ((1)) FOR [ck_NotPresent]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Fin__62881EA3]  DEFAULT ((0)) FOR [ck_Finshed]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_can__637C42DC]  DEFAULT ((0)) FOR [ck_cancel]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Bac__64706715]  DEFAULT ((1)) FOR [ck_Backed]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ck_Aud__65648B4E]  DEFAULT ((0)) FOR [ck_Audit]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__buy_MA__6658AF87]  DEFAULT ((0)) FOR [buy_MATTYPE_BlackNoToPo]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Bill_O__674CD3C0]  DEFAULT ((0)) FOR [Bill_OwnerFactory_stock]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__AddPro__6840F7F9]  DEFAULT ((0)) FOR [AddPro_Facth_Vendor]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__BatchS__69351C32]  DEFAULT ((0)) FOR [BatchSDefect_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__AddLin__6A29406B]  DEFAULT ((0)) FOR [AddLineCHeck_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__AddLin__6B1D64A4]  DEFAULT ((0)) FOR [AddLineCHeck_EP056]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__AddLin__6C1188DD]  DEFAULT ((0)) FOR [AddLineCHeck_EP051]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Add_Fa__6D05AD16]  DEFAULT ((0)) FOR [Add_FaultNo_PoBill]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Add_Bl__6DF9D14F]  DEFAULT ((0)) FOR [Add_Black_PoBill]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__add_Au__6EEDF588]  DEFAULT ((0)) FOR [add_AutoShenhePnl_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__add_Au__6FE219C1]  DEFAULT ((0)) FOR [add_AutoShenhe_EP075209]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MOUT_D__6BDC7EB3]  DEFAULT ((0)) FOR [MOUT_DEPT_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__assign__6CD0A2EC]  DEFAULT ((1)) FOR [assign_delivery_no_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__SALES___6DC4C725]  DEFAULT ((0)) FOR [SALES_COPI23_VER_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__progra__6EB8EB5E]  DEFAULT ((0)) FOR [program_menu_vflag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__EditIn__6FAD0F97]  DEFAULT ((0)) FOR [EditInvtCheck]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__costin__70A133D0]  DEFAULT ((0)) FOR [costing_method]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__scrap___7565E8ED]  DEFAULT ((0)) FOR [scrap_cost_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__rework__765A0D26]  DEFAULT ((0)) FOR [rework_cost_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__Group___7A2A9E0A]  DEFAULT ((0)) FOR [Group_account_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__first___7FE37760]  DEFAULT ((0)) FOR [first_account_type]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__ap_che__712B43B5]  DEFAULT ((0)) FOR [ap_check_in_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__COPI04__4B7A9C19]  DEFAULT ((0)) FOR [COPI04_SEND_MSG]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MI_RSP__5ABCDFA9]  DEFAULT ((0)) FOR [MI_RSPEC_FLAG_99]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__5E8D708D]  DEFAULT ((0)) FOR [mi_rspec_option_99]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__5F8194C6]  DEFAULT ((0)) FOR [mi_rspec_option_90]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__6075B8FF]  DEFAULT ((0)) FOR [mi_rspec_option_100]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__6169DD38]  DEFAULT ((0)) FOR [mi_rspec_option_110]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__625E0171]  DEFAULT ((0)) FOR [mi_rspec_option_120]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__635225AA]  DEFAULT ((0)) FOR [mi_rspec_option_200]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__644649E3]  DEFAULT ((0)) FOR [mi_rspec_option_91]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MI_RSP__653A6E1C]  DEFAULT ((0)) FOR [MI_RSPEC_FLAG_91]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__mi_rsp__662E9255]  DEFAULT ((0)) FOR [mi_rspec_option_101]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__MI_RSP__6722B68E]  DEFAULT ((0)) FOR [MI_RSPEC_OPTION_35]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__surplu__5D4F3391]  DEFAULT ((1)) FOR [surplus_QUAN_ON_HAND]
GO

ALTER TABLE [dbo].[Data0192] ADD  CONSTRAINT [DF__Data0192__prodou__20111B27]  DEFAULT ((0)) FOR [prodout_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  DEFAULT ((0)) FOR [drillslot_itemcount]
GO

ALTER TABLE [dbo].[Data0192] ADD  DEFAULT ((0)) FOR [IdNo_flag]
GO

ALTER TABLE [dbo].[Data0192] ADD  DEFAULT ((0)) FOR [DefaultFlowByCust]
GO

ALTER TABLE [dbo].[Data0192] ADD  DEFAULT ((0)) FOR [drillslot_diacount]
GO

ALTER TABLE [dbo].[Data0192] ADD  DEFAULT ((0)) FOR [REQ_COMPLETE_DATE_FLAG]
GO

ALTER TABLE [dbo].[Data0192] ADD  DEFAULT ((0)) FOR [FILE_NAME_FLAG]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新交付排程是否要审核，0表示不要，1要' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Data0192', @level2type=N'COLUMN',@level2name=N'Pur_Cons_Auth'
GO

