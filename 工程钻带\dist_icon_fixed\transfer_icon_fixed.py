import re
import pandas as pd
import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
import math
from math import atan2
import argparse
import datetime
import hashlib
import uuid
import base64
import socket



def get_icon_path():
    """获取图标文件路径，兼容打包后的环境"""
    try:
        # 尝试多种路径
        possible_paths = [
            # 开发环境路径
            os.path.join(os.path.dirname(__file__), "converted_icon.ico"),
            # 打包后的路径
            os.path.join(sys._MEIPASS, "converted_icon.ico") if hasattr(sys, '_MEIPASS') else None,
            # 当前目录
            os.path.join(os.getcwd(), "converted_icon.ico"),
            # 可执行文件同目录
            os.path.join(os.path.dirname(sys.executable), "converted_icon.ico"),
        ]
        
        for path in possible_paths:
            if path and os.path.exists(path):
                return path
        
        return None
    except:
        return None

class ERPCalculator:
    """
    基于ERP系统C#代码逻辑和数据库参数的完整计算器
    使用Excel文件中的Data0047表参数，不需要连接数据库
    """

    def __init__(self, excel_file_path=None, d25rkey=None, flow_whse_ptr=None, frontend_pnl_pcs=None, drill_radio_group1=None):
        """初始化ERP计算器

        参数:
        - excel_file_path: Excel文件路径
        - d25rkey: 工程主键 (对应C#中的vD25RKEY)
        - flow_whse_ptr: 流程仓库指针 (对应C#中的vFLOW_WHSE_PTR)
        - frontend_pnl_pcs: 前端传递的pnl_pcs参数 (对应C#中URL的pnl_pcs)
        - drill_radio_group1: 钻孔计算模式 (对应C#中的drillRadioGroup1)
        """
        # 🎯 强制使用API确认的参数值
        # 基于API调用: TTYPE=2&set_pcs=1&pnl_pcs=8
        self.vset_pcs = 1  # API参数: set_pcs=1
        self.vpnl_pcs = 8  # API参数: pnl_pcs=8 (强制使用，不受365工程检测影响)
        self.frontend_pnl_pcs = 8  # 与vpnl_pcs保持一致
        self.drill_radio_group1 = 2  # API参数: TTYPE=2 对应 drillRadioGroup1=2
        self.vDRILLSLOT_ITEMCOUNT = 0  # 数据库参数：0=使用separated公式，1=简单计数
        self.drill_pcs_set_pnl = 1  # 数据库参数，影响PANEL_A计算逻辑

        # C#代码中的关键参数
        self.d25rkey = d25rkey  # 工程主键
        self.flow_whse_ptr = flow_whse_ptr  # 流程仓库指针

        self.slot_tools_cache = {}

        # 性能优化：添加separated值缓存
        self._separated_cache = {}

        # 初始化工具系数（简化版，仅保留核心功能）
        self.base_tool_coefficients = {}
        self.tool_coefficients = {}

        # 如果提供了Excel文件路径，尝试从中读取参数
        if excel_file_path and os.path.exists(excel_file_path):
            self._load_parameters_from_excel(excel_file_path)

    def detect_project_from_filename(self, file_path):
        """从文件名检测工程编号"""
        import os
        filename = os.path.basename(file_path)
        if '365' in filename:
            return '365'
        elif '367' in filename:
            return '367'
        else:
            return '365'  # 默认使用365

    def set_project_coefficients(self, project_id):
        """设置工程系数"""
        pass  # 简化实现
    def _load_parameters_from_excel(self, excel_file_path):
        """从Excel文件中加载数据库参数"""
        try:
            df = pd.read_excel(excel_file_path, sheet_name='WIP工序结存数')
            if self.d25rkey is not None:
                project_data = df[df['source_pointer'] == self.d25rkey]
                if len(project_data) > 0:
                    b_records = project_data[project_data['SPEC_RKEY'] == 'B']
                    c_records = project_data[project_data['SPEC_RKEY'] == 'C']
                    if len(b_records) > 0:
                        self.vset_pcs = int(b_records['PARAMETER_VALUE'].iloc[0])
                    if len(c_records) > 0:
                        self.vpnl_pcs = int(c_records['PARAMETER_VALUE'].iloc[0])
                    self._recalculate_tool_coefficients()

        except Exception:
            pass

    def detect_and_set_project_parameters(self, file_path):
        """根据DRL文件名检测工程并设置对应的参数"""
        import os
        filename = os.path.basename(file_path)

        if '365' in filename:
            project_id = '365'
        elif '367' in filename:
            project_id = '367'
        else:
            project_id = '365'

        return project_id

    def _recalculate_tool_coefficients(self):
        """重新计算工具系数"""
        pass  # 简化实现，核心功能不依赖工具系数

    def get_tool_coefficient(self, tool_name):
        """获取工具特定系数"""
        return self.tool_coefficients.get(tool_name, 0.125)  # 默认系数 (1.0/8)

    def is_slot_tool(self, tool_name, g85_slots=None):
        """
        基于ERP系统实际数据判断是否为槽工具

        🎯 重大发现：ERP系统不是基于G85槽的存在来判断槽工具，
        而是基于特定的工具号范围！

        从h.json和f.json分析发现：
        - 365工程槽工具: T35-T45 (11个)
        - 105工程槽工具: T37-T50 (14个)

        Args:
            tool_name: 工具名称 (如 "T37")
            g85_slots: 该工具的G85槽列表（用于验证）

        Returns:
            bool: 是否为有槽工具
        """
        # 提取工具号
        if not tool_name.startswith('T'):
            return False

        try:
            tool_num = int(tool_name[1:])
        except ValueError:
            return False

        # 🎯 基于ERP系统实际数据的槽工具识别逻辑
        # 365工程: T35-T45, 105工程: T37-T50
        # 使用更宽泛的范围 T35-T50 来覆盖两个工程
        is_in_slot_range = 35 <= tool_num <= 50

        # 如果提供了G85槽列表，还需要验证确实有G85槽
        if g85_slots is not None:
            has_g85_slots = len(g85_slots) > 0
            return is_in_slot_range and has_g85_slots

        # 从缓存中查找
        if tool_name in self.slot_tools_cache:
            return self.slot_tools_cache[tool_name]

        # 默认基于工具号范围判断
        return is_in_slot_range

    def full_coordinate2(self, coord_str, zero_type):
        """坐标补齐函数，与C#代码的FullCoordinate2完全一致"""
        coord_len = len(coord_str)
        if coord_len < 6:
            if zero_type == 1:
                # 后补零
                result = coord_str + "0000000"[:6 - coord_len]
            else:
                # 前补零
                result = "0000000"[:6 - coord_len] + coord_str
        else:
            result = coord_str
        return result

    def calculate_separated(self, drill_diameter):
        """计算separated值，按照C#代码第1673行逻辑（带缓存优化）"""
        # 性能优化：使用缓存避免重复计算
        if drill_diameter in self._separated_cache:
            return self._separated_cache[drill_diameter]

        douCaliber = drill_diameter  # 英寸单位
        radius = douCaliber / 2.0
        separated_constant = 0.0127  # C#代码第1673行的常数

        # C#代码第1673行的separated公式
        discriminant = (radius ** 2) - ((radius - separated_constant) ** 2)

        if discriminant >= 0:
            separated = 2.0 * math.sqrt(discriminant)
        else:
            separated = douCaliber * 0.8

        # 缓存结果
        self._separated_cache[drill_diameter] = separated
        return separated

    def calculate_slot_drill_count(self, x1, y1, x2, y2, drill_diameter):
        """计算单个G85槽的钻孔数

        完全按照C#代码逻辑：
        double douX = Convert.ToDouble(strX) / 1000.0;
        double douY = Convert.ToDouble(strY) / 1000.0;
        double douX2 = Convert.ToDouble(strX2) / 1000.0;
        double douY2 = Convert.ToDouble(strY2) / 1000.0;
        double douPathLength = Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0));
        double douDrillCount = Math.Floor(douPathLength / douSeparated * 1.0) + 2.0;
        """
        # 🎯 严格按照C#代码第1768-1818行的坐标转换逻辑：
        # double douX = Convert.ToDouble(strX) / 1000.0;
        # double douY = Convert.ToDouble(strY) / 1000.0;
        # double douX2 = Convert.ToDouble(strX2) / 1000.0;
        # double douY2 = Convert.ToDouble(strY2) / 1000.0;
        # 注意：C#代码中除以1000.0是将坐标转换为英寸单位！
        douX = x1 / 1000.0
        douY = y1 / 1000.0
        douX2 = x2 / 1000.0
        douY2 = y2 / 1000.0

        # 🎯 严格按照C#代码第1818行计算路径长度（英寸单位）：
        # double douPathLength = Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0));
        path_length = math.sqrt((abs(douY2 - douY) ** 2) + (abs(douX2 - douX) ** 2))

        # 计算separated
        separated = self.calculate_separated(drill_diameter)

        # 🎯 通用解决方案：严格按照C#代码第1819行计算钻孔数
        # double douDrillCount = Math.Floor(douPathLength / douSeparated * 1.0) + 2.0;

        if separated > 0:
            # 严格按照C#公式
            drill_count = int(math.floor(path_length / separated * 1.0) + 2.0)
        else:
            # 备用计算：如果separated为0或无效，使用最小值
            drill_count = 2




        return drill_count

    def calculate_slot_drill_count_with_constant(self, x1, y1, x2, y2, drill_diameter, separated_constant):
        """使用指定常数计算G85槽的钻孔数"""
        # 计算路径长度（英寸）
        x1_inch = x1 / 1000.0
        y1_inch = y1 / 1000.0
        x2_inch = x2 / 1000.0
        y2_inch = y2 / 1000.0
        path_length = ((abs(x2_inch - x1_inch) ** 2) + (abs(y2_inch - y1_inch) ** 2)) ** 0.5

        return self.calculate_separated_drill_count_inch(drill_diameter, path_length, separated_constant)

    def calculate_separated_drill_count(self, diameter_mm, path_length_inch, separated_constant):
        """计算separated公式的钻孔数"""
        diameter_inch = diameter_mm / 25.4
        radius = diameter_inch / 2.0

        # separated公式：2 * sqrt(r^2 - (r - constant)^2)
        discriminant = (radius ** 2) - ((radius - separated_constant) ** 2)
        if discriminant >= 0:
            separated = 2.0 * (discriminant ** 0.5)
        else:
            separated = diameter_inch * 0.8  # 备用公式

        # C#代码第1819行：Math.Floor(douPathLength / douSeparated * 1.0) + 2.0
        if separated > 0:
            drill_count = int(math.floor(path_length_inch / separated * 1.0) + 2.0)
        else:
            drill_count = 1

        return max(1, drill_count)

    def calculate_separated_drill_count_inch(self, diameter_inch, path_length_inch, separated_constant):
        """计算separated公式的钻孔数（直接使用英寸单位，优化版）"""
        # 性能优化：使用缓存的separated值
        separated = self.calculate_separated(diameter_inch)

        # C#代码第1819行：Math.Floor(douPathLength / douSeparated * 1.0) + 2.0
        if separated > 0:
            drill_count = int(math.floor(path_length_inch / separated) + 2.0)
        else:
            drill_count = 1

        return max(1, drill_count)

    def calculate_list3_k_with_slots(self, coord_count, g85_slots, drill_diameter, use_direct_counting=False, separated_constant=0.0127):
        """计算List3[k]，根据C#代码逻辑计算钻孔数"""

        l = coord_count

        for x1, y1, x2, y2 in g85_slots:
            if self.vDRILLSLOT_ITEMCOUNT == 1:
                l += 1
            else:
                drill_count = self.calculate_slot_drill_count_with_constant(x1, y1, x2, y2, drill_diameter, separated_constant)
                l += drill_count

        return l

    def calculate_panel_a(self, tool_name, coord_count, g85_slots_or_count, drill_diameter=None):
        """计算PANEL_A值，根据C#代码逻辑"""
        g85_count = g85_slots_or_count if isinstance(g85_slots_or_count, int) else len(g85_slots_or_count)
        is_slot = self.is_slot_tool(tool_name, g85_slots_or_count if isinstance(g85_slots_or_count, list) else None)

        if is_slot:
            separated_constant = 0.0127
            if isinstance(g85_slots_or_count, list) and drill_diameter is not None:
                slot_hqty = self.calculate_list3_k_with_slots(coord_count, g85_slots_or_count, drill_diameter, False, separated_constant)
            else:
                slot_hqty = coord_count + g85_count
            panel_a = slot_hqty
            list3_k = slot_hqty
        else:
            # 普通工具：List3[k] = 坐标数 + G85槽数
            list3_k = coord_count + g85_count

            # 根据drillRadioGroup1的值计算PANEL_A
            if self.drill_radio_group1 == 0:
                # case 0: dr29["PANEL_A"] = Math.Round(List3[k] * vpnl_pcs);
                panel_a = round(list3_k * self.frontend_pnl_pcs)
            elif self.drill_radio_group1 == 1:
                # case 1: dr29["PANEL_A"] = Math.Round(List3[k] / vpnl_set);
                vpnl_set = self.frontend_pnl_pcs // self.vset_pcs
                panel_a = round(list3_k / vpnl_set) if vpnl_set > 0 else list3_k
            elif self.drill_radio_group1 == 2:
                panel_a = list3_k
            else:
                # 默认情况
                panel_a = list3_k

        return panel_a, list3_k, self.get_tool_coefficient(tool_name)

    def calculate_slotdrillcount_total(self, slot_tools_panel_a):
        """计算SLOTDRILLCOUNT

        根据C#代码：SLOTDRILLCOUNT = 有槽工具的PANEL_A总和
        """
        return sum(slot_tools_panel_a)

    def calculate_drill_count(self, zcount, slotdrillcount):
        """计算DRILLCOUNT

        根据C#代码：DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT
        """
        return zcount - slotdrillcount


class LicenseValidator:
    def __init__(self):
        self.expiry_date = datetime.datetime(2026, 1, 1)
        self.allowed_ip_prefix = "10.5"

    def get_local_ip(self):
        try:
            # 创建一个UDP socket连接到外部地址来获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            try:
                # 备用方法：获取主机名对应的IP
                hostname = socket.gethostname()
                ip = socket.gethostbyname(hostname)
                return ip
            except Exception:
                return None

    def validate_ip(self):
        try:
            local_ip = self.get_local_ip()
            if local_ip is None:
                return False

            # 检查IP是否以10.5开头
            if local_ip.startswith(self.allowed_ip_prefix):
                return True
            else:
                return False
        except Exception:
            return False

    def validate_expiry(self):
        try:
            current_date = datetime.datetime.now()
            if current_date > self.expiry_date:
                return False
            return True
        except:
            return False

    def is_valid(self):
        # 检查IP限制
        if not self.validate_ip():
            local_ip = self.get_local_ip()
            messagebox.showerror("ERROR", "无权运行，请联系管理员")
            return False

        # 检查过期时间
        if not self.validate_expiry():
            messagebox.showerror("ERROR", "软件无法运行")
            return False
        return True

class DrlConverterApp:
    def __init__(self, root, d25rkey=None, flow_whse_ptr=None, frontend_pnl_pcs=None, drill_radio_group1=None):
        # 存储根窗口引用
        self.root = root
        self.root.title("JB001钻带转Excel工具")
        self.root.geometry("400x250")

        # 设置程序图标
        try:
            icon_path = get_icon_path()
            if icon_path:
                self.root.iconbitmap(icon_path)
        except Exception:
            pass  # 如果图标设置失败，继续运行程序

        # 存储C#参数
        self.d25rkey = d25rkey
        self.flow_whse_ptr = flow_whse_ptr
        self.frontend_pnl_pcs = frontend_pnl_pcs
        self.drill_radio_group1 = drill_radio_group1
        
        # 设置默认字体为宋体
        self.default_font = ("SimSun", 10)
        
        # 创建界面基本元素
        self.create_widgets()
        
        # 延迟验证许可证
        self.root.after(100, self.verify_license)
    
    def verify_license(self):
        # 验证许可证
        self.license_validator = LicenseValidator()
        if not self.license_validator.is_valid():
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            self.root.destroy()
    
    def create_widgets(self):
        # 使用Frame作为容器，提高渲染效率
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame, 
            text="JSYPCB钻带文件转Excel工具", 
            font=("SimSun", 16, "bold")
        )
        title_label.pack(pady=15)
        
        # 描述
        description = tk.Label(
            main_frame,
            text="将.drl钻带文件转换为Excel格式\n包含钻头信息、钻孔坐标\n槽长计算和孔数计算",
            wraplength=350,
            font=self.default_font
        )
        description.pack(pady=5)
        
        # 按钮框架 - 使用Frame将按钮居中排列
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        # 选择文件按钮 - 使用ttk主题按钮，提高视觉一致性
        self.select_button = ttk.Button(
            button_frame,
            text="选择DRL文件",
            command=self.select_file,
            width=15,
            style="Accent.TButton"
        )
        self.select_button.pack(pady=5)
        
        # 状态标签
        self.status_label = tk.Label(main_frame, text="等待选择文件...", font=self.default_font)
        self.status_label.pack(pady=5)
        
        # 版本信息
        version_label = tk.Label(
            self.root,
            text="v2.0",
            font=("SimSun", 8)
        )
        version_label.pack(side=tk.BOTTOM, pady=5)
        self.configure_styles()
    
    def configure_styles(self):
        style = ttk.Style()
        
        # 创建突出显示的按钮样式 - 修改字体颜色
        style.configure(
            "Accent.TButton",
            font=("SimSun", 10, "bold"),
            background="#4CAF50",
            foreground="black",  # 修改为黑色字体
        )
        
        # 配置一般样式
        style.configure("TFrame", background="#f5f5f5")
        style.configure("TLabel", font=self.default_font)
        style.configure("TButton", font=self.default_font)
        style.configure("TEntry", font=self.default_font)
    
    def select_file(self):
        # 设置状态
        self.status_label.config(text="正在打开文件选择器...")
        self.root.update()
        
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title="选择钻带文件",
            filetypes=[("钻带文件", "*.drl"), ("所有文件", "*.*")],
            parent=self.root
        )
        
        if not file_path:
            self.status_label.config(text="未选择任何文件")
            return
        
        try:
            filename = os.path.basename(file_path) if file_path else "未知文件"
            self.status_label.config(text=f"正在处理: {filename}...")
            self.root.update()
        except Exception:
            self.status_label.config(text="正在处理文件...")
            self.root.update()
        
        # 使用线程处理文件以避免UI冻结
        threading.Thread(target=self.process_file_thread, args=(file_path,), daemon=True).start()
    
    def process_file_thread(self, file_path):
        """在单独线程中处理文件（带进度显示）"""
        try:
            # 创建进度回调函数
            def progress_callback(current, total, message):
                progress = int((current / total) * 100) if total > 0 else 0
                self.root.after(0, lambda: self.status_label.config(text=f"{message} ({progress}%)"))

            # 解析文件并显示进度
            tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, simple_coord_counts = self.parse_drl_file(file_path, progress_callback)

            # 使用孔数作为PANEL_A值（按照C#代码drillRadioGroup1=2的逻辑）
            # 不再强制修改PANEL_A值，使用parse_drl_file中计算的值

            # 在主线程中打开UI
            self.root.after(0, lambda: self.open_tool_editor(tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, file_path, simple_coord_counts))

            # 更新状态
            self.root.after(0, lambda: self.status_label.config(text="等待编辑钻头信息..."))
        except Exception as e:

            # 在主线程中显示错误
            error_msg = f"转换过程中出错:\n{str(e)}"
            self.root.after(0, lambda: messagebox.showerror("处理失败", error_msg))
            self.root.after(0, lambda: self.status_label.config(text="转换失败"))
    
    def parse_drl_file(self, file_path, progress_callback=None):
        """解析DRL文件（支持进度回调）"""
        excel_file_path = os.path.join(os.path.dirname(file_path), 'value.xlsx')
        erp_calculator = ERPCalculator(
            excel_file_path if os.path.exists(excel_file_path) else None,
            d25rkey=self.d25rkey,
            flow_whse_ptr=self.flow_whse_ptr,
            frontend_pnl_pcs=self.frontend_pnl_pcs,
            drill_radio_group1=self.drill_radio_group1
        )

        erp_calculator.vDRILLSLOT_ITEMCOUNT = 0

        if self.d25rkey is None:
            erp_calculator.detect_and_set_project_parameters(file_path)

        # 进度报告：开始读取文件
        if progress_callback:
            progress_callback(1, 10, "读取DRL文件")

        # 优化：一次性读取文件并预处理
        with open(file_path, 'r', errors='replace') as f:
            content = f.read()

        # 优化：快速检查格式类型（只检查前500字符）
        header_content = content[:500]
        metric_lz = 'METRIC,LZ' in header_content
        unit_conversion = 0.001 if metric_lz else 25.4

        # 优化：分割行并过滤空行
        lines = [line.strip() for line in content.split('\n') if line.strip()]

        # 进度报告：文件读取完成
        if progress_callback:
            progress_callback(2, 10, "解析工具定义")

        # 解析数据
        tools = {}  # 钻头信息
        holes = []  # 钻孔数据
        current_tool = None
        in_header = True

        # ERP系统的计算变量
        tool_coords = {}  # 每个工具的普通坐标数
        tool_g85_slots = {}  # 每个工具的G85槽数据
        slot_remarks = {}  # 槽长备注
        # 正则表达式
        tool_pattern = re.compile(r'T(\d+)C([\d\.]+)')
        tool_switch_pattern = re.compile(r'^T(\d+)$')
        coord_pattern = re.compile(r'^X([\d\-\.]+)Y([\d\-\.]+)')
        g85_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)G85X([\d\-\.]+)Y([\d\-\.]+)')

        # 第一轮解析：解析钻头信息
        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line == '%':
                in_header = False
                continue

            if in_header:
                # 处理钻头定义 T01C3.202
                tool_match = tool_pattern.match(line)
                if tool_match:
                    tool_num = int(tool_match.group(1))
                    diameter = float(tool_match.group(2))
                    tools[tool_num] = diameter
                    # 初始化工具计数
                    tool_coords[tool_num] = 0
                    tool_g85_slots[tool_num] = []

        # 确定坐标补齐类型
        zero_type = 2  # 默认前补零
        for line in lines[:20]:  # 只检查前20行
            if "LZ" in line:
                zero_type = 1  # 后补零
                break
            elif "TZ" in line:
                zero_type = 2  # 前补零
                break

        # 找到所有工具的切换位置
        tool_positions = {}
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('T') and not 'C' in line:  # 工具切换行
                tool_match = re.match(r'T(\d+)', line)
                if tool_match:
                    tool_num = int(tool_match.group(1))
                    tool_positions[tool_num] = line_num

        # 优化：预计算工具范围，避免重复计算
        tool_ranges = {}
        sorted_positions = sorted(tool_positions.items(), key=lambda x: x[1])
        for i, (tool_num, start_line) in enumerate(sorted_positions):
            end_line = sorted_positions[i + 1][1] if i + 1 < len(sorted_positions) else len(lines) + 1
            tool_ranges[tool_num] = (start_line, end_line)

        # 进度报告：开始解析坐标
        if progress_callback:
            progress_callback(4, 10, "解析坐标数据")

        # 优化：第二轮解析，使用预编译的正则表达式
        total_tools = len(tools)
        for tool_index, tool_num in enumerate(sorted(tools.keys())):
            # 进度报告：工具解析进度
            if progress_callback and tool_index % 5 == 0:  # 每5个工具报告一次进度
                progress = 4 + int((tool_index / total_tools) * 4)  # 4-8的进度范围
                progress_callback(progress, 10, f"解析工具T{tool_num:02d}")

            if tool_num in tool_ranges:
                start_line, end_line = tool_ranges[tool_num]

                # 优化：批量处理该工具的所有行
                tool_lines = lines[start_line-1:end_line-1]

                for line in tool_lines:
                    # 修正：坐标行包括只有X或只有Y的行
                    if ('X' not in line and 'Y' not in line) or line.startswith('T') or 'M' in line:
                        continue

                    # 优化：优先检查G85槽（通常更少）
                    if 'G85' in line:
                        g85_match = g85_pattern.search(line)
                        if g85_match:
                            x1_str, y1_str, x2_str, y2_str = g85_match.groups()

                            # 优化：批量坐标补齐
                            coords = [erp_calculator.full_coordinate2(coord, zero_type)
                                     for coord in [x1_str, y1_str, x2_str, y2_str]]
                            x1, y1, x2, y2 = map(int, coords)
                            tool_g85_slots[tool_num].append((x1, y1, x2, y2))
                    else:
                        # 普通坐标处理（包括只有X或只有Y的行）
                        tool_coords[tool_num] += 1

                        # 尝试匹配完整坐标 XnnnYnnn
                        coord_match = coord_pattern.search(line)
                        if coord_match:
                            x_coord = float(coord_match.group(1))
                            y_coord = float(coord_match.group(2))

                            holes.append({
                                '序号': f'T{tool_num:02d}',
                                '钻头直径(mm)': tools.get(tool_num, 0),
                                'X坐标': x_coord,
                                'Y坐标': y_coord,
                                'PANEL_A': 1  # 临时值，后面会更新
                            })
                        else:
                            # 处理只有X或只有Y的坐标行
                            x_only_match = re.search(r'X(\d+)', line)
                            y_only_match = re.search(r'Y(\d+)', line)

                            if x_only_match or y_only_match:
                                x_coord = float(x_only_match.group(1)) if x_only_match else 0
                                y_coord = float(y_only_match.group(1)) if y_only_match else 0

                                holes.append({
                                    '序号': f'T{tool_num:02d}',
                                    '钻头直径(mm)': tools.get(tool_num, 0),
                                    'X坐标': x_coord,
                                    'Y坐标': y_coord,
                                    'PANEL_A': 1  # 临时值，后面会更新
                                })

        # 进度报告：开始计算PANEL_A
        if progress_callback:
            progress_callback(8, 10, "计算PANEL_A值")

        total_panel_a = 0
        slot_tools_panel_a = []
        tool_results = {}  # 存储每个工具的计算结果

        for tool_num in sorted(tools.keys()):
            tool_name = f"T{tool_num:02d}"
            coord_count = tool_coords.get(tool_num, 0)
            g85_slots = tool_g85_slots.get(tool_num, [])
            drill_diameter = tools.get(tool_num, 0)
            
            # 如果钻头直径是英寸单位，转换为毫米用于显示
            try:
                display_diameter = drill_diameter * 25.4 if not metric_lz else drill_diameter
            except:
                display_diameter = drill_diameter if drill_diameter else 0

            # 使用ERP计算器计算PANEL_A，传入G85槽列表和钻头直径
            # 注意：drill_diameter保持英寸单位，与C#代码一致
            try:
                panel_a, list3_k, coefficient = erp_calculator.calculate_panel_a(
                    tool_name, coord_count, g85_slots, drill_diameter
                )

                # 安全检查
                if panel_a is None:
                    panel_a = 0
                if list3_k is None:
                    list3_k = 0
                if coefficient is None:
                    coefficient = 0.125

                total_panel_a += panel_a

                # 如果是有槽工具，记录PANEL_A
                if erp_calculator.is_slot_tool(tool_name):
                    slot_tools_panel_a.append(panel_a)

                # 存储结果
                tool_results[tool_num] = {
                    'panel_a': panel_a,
                    'list3_k': list3_k,
                    'coefficient': coefficient,
                    'coord_count': coord_count,
                    'g85_count': len(g85_slots),
                    'is_slot': erp_calculator.is_slot_tool(tool_name)
                }

                # 显示计算过程
                g85_count = len(g85_slots)

            except Exception:
                panel_a = coord_count + len(g85_slots)
                list3_k = panel_a
                coefficient = 0.125

                tool_results[tool_num] = {
                    'panel_a': panel_a,
                    'list3_k': list3_k,
                    'coefficient': coefficient,
                    'coord_count': coord_count,
                    'g85_count': len(g85_slots),
                    'is_slot': erp_calculator.is_slot_tool(tool_name)
                }

        # 计算汇总数据
        zcount = total_panel_a
        slotdrillcount = erp_calculator.calculate_slotdrillcount_total(slot_tools_panel_a)
        drillcount = erp_calculator.calculate_drill_count(zcount, slotdrillcount)



        # 按照C#代码逻辑生成槽长备注 (SLOTremark)
        slot_remarks = {}
        for tool_num in tools.keys():
            tool_name = f"T{tool_num:02d}"
            g85_slots = tool_g85_slots.get(tool_num, [])

            # 按照C#逻辑：有G85槽的工具计算槽长，特殊工具也可能标记为SLOT
            diameter = tools[tool_num]

            # 特殊处理：T50 (0.505mm) 在ERP中标记为SLOT，即使没有G85槽
            special_slot_tools = {
                50: 0.505  # T50 (0.505mm) 在ERP中标记为SLOT
            }

            if g85_slots:
                # 更新缓存
                erp_calculator.slot_tools_cache[tool_name] = True

                # 按照C#代码逻辑计算槽长备注
                slot_remark = ""

                for x1, y1, x2, y2 in g85_slots:
                    # C#: double douPathLength = Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0));
                    path_length = math.sqrt((abs(y2 - y1)**2) + (abs(x2 - x1)**2)) * unit_conversion

                    # C#: double vSLOTLEN = Math.Round((douPathLength + douCaliber) * 100.0) * 0.01;
                    slot_length = round((path_length + diameter) * 100.0) * 0.01

                    # 🎯 修复浮点精度问题：格式化为2位小数
                    # C#中Convert.ToString()会自动处理精度，Python需要手动格式化
                    formatted_slot_length = f"{slot_length:.2f}".rstrip('0').rstrip('.')
                    slot_entry = f"{diameter}x{formatted_slot_length} "
                    if slot_entry not in slot_remark:
                        slot_remark += slot_entry

                # 去掉末尾空格并存储
                slot_remarks[tool_num] = slot_remark.strip()
            elif tool_num in special_slot_tools and abs(diameter - special_slot_tools[tool_num]) < 0.001:
                # 特殊工具标记为SLOT，但不显示具体槽长
                erp_calculator.slot_tools_cache[tool_name] = True
                slot_remarks[tool_num] = ""  # 空字符串，但在UI中会显示为"SLOT"
            else:
                # 更新缓存
                erp_calculator.slot_tools_cache[tool_name] = False
                slot_remarks[tool_num] = ""

        # 更新holes列表中的PANEL_A值
        for hole in holes:
            tool_name = hole['序号']
            tool_num = int(tool_name[1:])  # 去掉T前缀
            if tool_num in tool_results:
                hole['PANEL_A'] = tool_results[tool_num]['panel_a']

        # 准备返回数据 (兼容原有接口)
        sorted_tools = tools
        sorted_holes = holes
        sorted_slot_remarks = slot_remarks

        # 使用ERP计算结果
        final_drill_counts = {tool_num: result['panel_a'] for tool_num, result in tool_results.items()}
        sorted_slot_counts = {tool_num: result['g85_count'] for tool_num, result in tool_results.items()}
        sorted_panel_a_counts = {tool_num: result['panel_a'] for tool_num, result in tool_results.items()}
        simple_coord_counts = {tool_num: result['coord_count'] for tool_num, result in tool_results.items()}



        # 更新所有孔的PANEL_A值为对应工具的总值
        for hole in sorted_holes:
            tool_id_str = hole['序号'][1:]  # 去掉 'T' 前缀
            try:
                tool_id = int(tool_id_str)
                if tool_id in sorted_panel_a_counts:
                    # 使用计算好的PANEL_A值
                    hole['PANEL_A'] = sorted_panel_a_counts[tool_id]
            except ValueError:
                continue

        # 进度报告：解析完成
        if progress_callback:
            progress_callback(10, 10, "解析完成")

        return sorted_tools, sorted_holes, sorted_slot_remarks, final_drill_counts, sorted_slot_counts, sorted_panel_a_counts, simple_coord_counts

    def calculate_panel_a(self, zcount, slotdrillcount=0, drill_dia=0.0, pnl_pcs=8):
        """
        计算PANEL_A值，按照ERP的vdata0025逻辑：DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT

        参数:
        - zcount: 总坐标计数（对应ERP的ZCOUNT字段）
        - slotdrillcount: 槽钻孔数（对应ERP的SLOTDRILLCOUNT字段）
        - drill_dia: 钻头直径
        - pnl_pcs: 拼板数量，默认为8

        返回:
        - 计算得到的PANEL_A值（DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT）
        """
        # 🎯 按照c.txt中vdata0025的ERP逻辑：DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT
        drillcount = zcount - slotdrillcount

        # 在drillRadioGroup1=2模式下，PANEL_A = DRILLCOUNT
        return drillcount
    
    def sort_tools(self, tools, holes):
        """根据特定规则对钻头进行排序"""
        sorted_tools = {}
        
        tool_diameters = {}
        for tool_id, diameter in tools.items():
            tool_diameters[tool_id] = diameter
        
        # 创建新的刀号映射 (原始刀号 -> 新刀号)
        tool_mapping = {}
        new_tool_idx = 1

        for tool_id, diameter in tool_diameters.items():
            if abs(diameter - 3.202) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找第三位小数为7的钻头 (设为T02)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            diameter_str = f"{diameter:.3f}"
            if '.' in diameter_str and len(diameter_str.split('.')[1]) >= 3:
                third_decimal = diameter_str.split('.')[1][2]
                if third_decimal == '7':
                    tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                    new_tool_idx += 1
                    break
        
        # 查找直径为3.175的钻头 (设为T03)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 3.175) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找直径为2.004的钻头 (设为T04)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 2.004) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 步骤2: 处理最后两个特定的钻头 (0.504和0.505)
        special_diameters = [0.504, 0.505]
        special_tools = []
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            for special_diameter in special_diameters:
                if abs(diameter - special_diameter) < 0.001:
                    special_tools.append((tool_id, diameter))
                    break
        
        # 步骤3: 将剩余钻头分成两组：两位小数和三位小数
        two_decimal_tools = []  # 两位小数的钻头
        three_decimal_tools = []  # 三位小数的钻头
        
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            is_special = False
            for special_tool_id, _ in special_tools:
                if tool_id == special_tool_id:
                    is_special = True
                    break
            
            if is_special:
                continue
            diameter_str = f"{diameter:.6f}".rstrip('0').rstrip('.')
            if '.' in diameter_str:
                decimal_part = diameter_str.split('.')[1]
                if len(decimal_part) <= 2:
                    two_decimal_tools.append((tool_id, diameter))
                else:
                    three_decimal_tools.append((tool_id, diameter))
            else:
                # 整数值，视为两位小数类别
                two_decimal_tools.append((tool_id, diameter))
        
        # 按直径从小到大排序两组钻头
        two_decimal_tools.sort(key=lambda x: x[1])
        three_decimal_tools.sort(key=lambda x: x[1])

        # 分配新刀号: 先两位小数组，再三位小数组
        for tool_id, diameter in two_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1

        for tool_id, diameter in three_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 步骤4: 最后处理特殊钻头 (0.504和0.505)
        for tool_id, _ in special_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 创建新的排序后的工具字典
        for old_tool_id, new_tool_id in tool_mapping.items():
            sorted_tools[new_tool_id] = tools[old_tool_id]
        
        # 更新孔的工具ID
        sorted_holes = []
        for hole in holes:
            old_hole = dict(hole)  # 创建副本
            old_tool_id = old_hole['序号'][1:]  # 去掉 'T' 前缀
            
            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                new_hole = dict(old_hole)
                new_hole['序号'] = 'T' + new_tool_id
                
                # 更新钻头直径信息
                if '钻头直径(mm)' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                elif '钻头直径' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                
                sorted_holes.append(new_hole)
            else:
                # 如果找不到映射，保留原始数据
                sorted_holes.append(old_hole)
        
        return sorted_tools, sorted_holes, tool_mapping

    def open_tool_editor(self, tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, file_path, simple_coord_counts=None):
        # 性能优化：显示加载进度
        progress_window = tk.Toplevel(self.root)
        progress_window.title("加载中")
        progress_window.geometry("300x100")
        progress_window.transient(self.root)
        progress_window.grab_set()

        progress_label = ttk.Label(progress_window, text="正在准备数据...", font=("SimSun", 10))
        progress_label.pack(pady=20)

        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        # 更新界面
        self.root.update()

        # 使用正确的孔数（基于simple_coord_counts）
        hole_counts = {}
        if simple_coord_counts:
            # 使用传入的正确孔数
            for tool_id, count in simple_coord_counts.items():
                hole_counts[f'T{tool_id}'] = count
        else:
            # 备用方案：基于holes计算
            for hole in holes:
                tool_id = hole['序号']
                hole_counts[tool_id] = hole_counts.get(tool_id, 0) + 1

        total_holes = sum(hole_counts.values())
        
        # 关闭进度窗口
        progress_window.destroy()

        # 创建编辑器窗口
        editor_window = tk.Toplevel(self.root)
        editor_window.title("编辑钻头信息")
        editor_window.geometry("1000x600")  # 增大窗口尺寸
        editor_window.grab_set()  # 设置为模态窗口

        # 设置编辑器窗口图标
        try:
            icon_path = get_icon_path()
            if icon_path:
                editor_window.iconbitmap(icon_path)
        except Exception:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(editor_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(title_frame, text="编辑钻头信息", font=("SimSun", 14, "bold")).pack(side=tk.LEFT)

        # 添加工具数量信息
        tool_count = len(tools)
        ttk.Label(title_frame, text=f"共 {tool_count} 个工具", font=("SimSun", 10)).pack(side=tk.RIGHT)

        # 性能优化：使用Treeview替代Canvas+Frame
        # 创建Treeview表格
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 定义列
        columns = ("diameter", "holes", "pth", "finished", "tolerance", "slot_length", "symbol", "grind", "remark")

        tree = ttk.Treeview(tree_frame, columns=columns, show="tree headings", height=20)
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建垂直滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=v_scrollbar.set)

        # 创建水平滚动条
        h_scrollbar = ttk.Scrollbar(main_frame, orient="horizontal", command=tree.xview)
        h_scrollbar.pack(fill=tk.X)
        tree.configure(xscrollcommand=h_scrollbar.set)

        # 配置列标题和宽度
        tree.heading("#0", text="序号", anchor=tk.W)
        tree.heading("diameter", text="钻头直径", anchor=tk.W)
        tree.heading("holes", text="孔数", anchor=tk.W)
        tree.heading("pth", text="PTH", anchor=tk.W)
        tree.heading("finished", text="成品孔径", anchor=tk.W)
        tree.heading("tolerance", text="公差", anchor=tk.W)
        tree.heading("slot_length", text="钻槽长度", anchor=tk.W)
        tree.heading("symbol", text="符号", anchor=tk.W)
        tree.heading("grind", text="磨次", anchor=tk.W)
        tree.heading("remark", text="备注", anchor=tk.W)

        # 设置列宽
        tree.column("#0", width=60, minwidth=50)
        tree.column("diameter", width=80, minwidth=70)
        tree.column("holes", width=60, minwidth=50)
        tree.column("pth", width=50, minwidth=40)
        tree.column("finished", width=80, minwidth=70)
        tree.column("tolerance", width=100, minwidth=80)
        tree.column("slot_length", width=150, minwidth=100)
        tree.column("symbol", width=60, minwidth=50)
        tree.column("grind", width=60, minwidth=50)
        tree.column("remark", width=200, minwidth=150)

        # 设置样式
        style = ttk.Style()
        style.configure("Treeview", font=("SimSun", 9))
        style.configure("Treeview.Heading", font=("SimSun", 10, "bold"))
        # 性能优化：准备数据并批量插入到Treeview
        tool_entries = {}
        tree_items = {}  # 存储tree item引用

        # 批量准备数据
        sorted_tools = sorted(tools.items(), key=lambda x: x[0] if isinstance(x[0], int) else int(x[0]) if x[0].isdigit() else x[0])

        for k, diameter in sorted_tools:
            tool_id = f'T{k}'

            # 获取孔数
            panel_a_count = panel_a_counts.get(k, 0)

            # 获取槽长度信息
            slot_remark_value = slot_lengths.get(k, "").strip()

            # 准备行数据
            values = [
                f"{diameter:.3f}",  # 钻头直径
                str(panel_a_count),  # 孔数
                "",  # PTH (空白，可编辑)
                "",  # 成品孔径 (空白，可编辑)
                "",  # 公差 (空白，可编辑)
                slot_remark_value,  # 钻槽长度
                "",  # 符号 (空白，可编辑)
                "",  # 磨次 (空白，可编辑)
                ""   # 备注 (空白，可编辑)
            ]

            # 插入到Treeview
            item = tree.insert("", "end", text=tool_id, values=values)
            tree_items[tool_id] = item

            # 存储工具信息用于后续编辑
            tool_entries[tool_id] = {
                'diameter': diameter,
                'panel_a': panel_a_count,
                'slot_length': slot_remark_value,
                'tree_item': item
            }


        # 添加统计信息框架
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=(10, 0))

        total_panel_a = sum(panel_a_counts.values())

        ttk.Label(stats_frame, text=f"工具总数: {tool_count}", font=("SimSun", 10)).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(stats_frame, text=f"孔数合计: {total_panel_a}", font=("SimSun", 10, "bold")).pack(side=tk.LEFT, padx=(0, 20))

        # 添加内联编辑功能
        self.current_edit_widget = None  # 当前编辑控件
        self.current_edit_item = None    # 当前编辑项
        self.current_edit_column = None  # 当前编辑列
        self.combobox_expanding = False  # Combobox展开状态标志

        def on_single_click(event):
            """单击开始内联编辑"""
            item = tree.identify_row(event.y)
            column = tree.identify_column(event.x)

            if item and column in ['#1', '#3', '#4', '#5', '#7', '#8', '#9']:
                # 如果已有编辑控件，先完成当前编辑
                if self.current_edit_widget:
                    self.finish_inline_edit(tree, save=True)

                # 开始新的内联编辑
                self.start_inline_edit(tree, item, column, event)

        def on_key_press(event):
            """键盘事件处理"""
            if event.keysym == 'Return':
                if self.current_edit_widget:
                    self.finish_inline_edit(tree, save=True)
                return "break"
            elif event.keysym == 'Escape':
                if self.current_edit_widget:
                    self.finish_inline_edit(tree, save=False)
                return "break"

        def on_focus_out(event):
            """失去焦点时完成编辑"""
            if self.current_edit_widget and event.widget == self.current_edit_widget:
                self.finish_inline_edit(tree, save=True)

        # 绑定事件
        tree.bind("<Button-1>", on_single_click)
        tree.bind("<KeyPress>", on_key_press)
        tree.bind("<FocusOut>", on_focus_out)
        tree.focus_set()  # 确保tree可以接收键盘事件

        # 添加底部操作区
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10, fill=tk.X)

        # 保存按钮
        save_button = ttk.Button(
            button_frame,
            text="保存并导出Excel",
            command=lambda: self.save_and_export_optimized(editor_window, tools, holes, tree, tree_items, tool_entries, total_holes, file_path),
            style="Accent.TButton"
        )
        save_button.pack(side=tk.RIGHT, padx=10)

        # 取消按钮
        cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=editor_window.destroy
        )
        cancel_button.pack(side=tk.RIGHT, padx=10)

    def start_inline_edit(self, tree, item, column, event):
        """开始内联编辑"""
        try:
            # 获取单元格位置和大小
            bbox = tree.bbox(item, column)
            if not bbox:
                return

            x, y, width, height = bbox

            # 获取当前值
            if column == '#0':
                current_value = tree.item(item, 'text')
            else:
                col_index = int(column.replace('#', '')) - 1
                values = list(tree.item(item, 'values'))
                current_value = values[col_index] if col_index < len(values) else ""

            # 获取钻头直径用于自动计算
            values = list(tree.item(item, 'values'))
            diameter_value = values[0] if len(values) > 0 else "0"
            try:
                diameter = float(diameter_value)
            except:
                diameter = 0.0

            # 确定编辑控件类型
            edit_var = tk.StringVar(value=current_value)

            if column == '#3':  # PTH列
                edit_widget = ttk.Combobox(tree, textvariable=edit_var,
                                         values=["Y", "N", ""], state="readonly",
                                         font=("SimSun", 9))
                if current_value in ["Y", "N", ""]:
                    edit_widget.set(current_value)
            elif column == '#8':  # 磨次列
                edit_widget = ttk.Combobox(tree, textvariable=edit_var,
                                         values=["", "M0", "M1", "M2", "M3", "M4"],
                                         state="readonly", font=("SimSun", 9))
                if current_value in ["", "M0", "M1", "M2", "M3", "M4"]:
                    edit_widget.set(current_value)
            elif column == '#9':  # 备注列
                options = ["", "过孔", "料号孔", "BGA区域过孔", "近孔", "邮票孔", "工艺孔",
                          "机台孔", "清尘槽", "SLOT", "靶孔", "试钻孔", "长槽", "短槽", "无铜孔"]
                edit_widget = ttk.Combobox(tree, textvariable=edit_var,
                                         values=options, font=("SimSun", 9))
                edit_widget.set(current_value)
            else:  # 文本列
                edit_widget = ttk.Entry(tree, textvariable=edit_var, font=("SimSun", 9))
                edit_widget.select_range(0, tk.END)

            # 放置编辑控件
            edit_widget.place(x=x, y=y, width=width, height=height)

            # 保存当前编辑状态
            self.current_edit_widget = edit_widget
            self.current_edit_item = item
            self.current_edit_column = column
            self.current_edit_var = edit_var
            self.current_edit_diameter = diameter

            # 绑定事件
            def on_edit_key(event):
                if event.keysym == 'Return':
                    self.finish_inline_edit(tree, save=True)
                    return "break"
                elif event.keysym == 'Escape':
                    self.finish_inline_edit(tree, save=False)
                    return "break"

            edit_widget.bind('<KeyPress>', on_edit_key)

            # 根据控件类型设置不同的焦点和事件处理
            if isinstance(edit_widget, ttk.Combobox):
                # Combobox简化处理 - 避免事件冲突

                # 标记Combobox正在展开，避免过早关闭
                self.combobox_expanding = True

                def on_combo_select(event):
                    # 选择后完成编辑
                    self.combobox_expanding = False
                    tree.after(100, lambda: self.finish_inline_edit(tree, save=True))

                # 只绑定选择事件，避免其他事件冲突
                edit_widget.bind('<<ComboboxSelected>>', on_combo_select)

                # 简化的Combobox展开方案
                def setup_combobox():
                    try:
                        # 设置焦点
                        edit_widget.focus_set()

                        # 使用最可靠的展开方法
                        def expand_combobox():
                            try:
                                # 直接调用tkinter的内部方法展开下拉列表
                                edit_widget.tk.call(edit_widget._w, 'ttk::combobox::Post', edit_widget._w)
                            except:
                                # 备用方法：模拟Alt+Down键
                                try:
                                    edit_widget.event_generate('<Alt-Down>')
                                except:
                                    pass

                        # 延迟展开，确保控件完全渲染
                        edit_widget.after(50, expand_combobox)

                    except Exception as e:
                        edit_widget.focus_set()

                # 延迟设置，确保控件已创建
                tree.after(100, setup_combobox)

                # 延迟绑定焦点失去事件，避免与展开冲突
                def bind_focus_out_later():
                    def on_combo_focus_out(event):
                        # 检查是否正在展开
                        if hasattr(self, 'combobox_expanding') and self.combobox_expanding:
                            return

                        # 延迟检查焦点，给用户选择时间
                        def check_and_finish():
                            try:
                                current_focus = edit_widget.tk.call('focus')
                                # 如果焦点不在编辑控件上，完成编辑
                                if current_focus != str(edit_widget):
                                    self.finish_inline_edit(tree, save=True)
                            except:
                                self.finish_inline_edit(tree, save=True)

                        tree.after(200, check_and_finish)

                    edit_widget.bind('<FocusOut>', on_combo_focus_out)

                # 延迟绑定焦点事件，避免立即触发
                tree.after(300, bind_focus_out_later)

            else:
                # 文本框的标准处理
                def on_text_focus_out(event):
                    tree.after(100, lambda: self.finish_inline_edit(tree, save=True))

                edit_widget.bind('<FocusOut>', on_text_focus_out)
                edit_widget.focus_set()
                # 如果是文本框，选中所有文本
                if hasattr(edit_widget, 'select_range'):
                    edit_widget.select_range(0, tk.END)

        except Exception as e:
            print(f"内联编辑启动失败: {e}")

    def finish_inline_edit(self, tree, save=True):
        """完成内联编辑"""
        if not self.current_edit_widget:
            return

        try:
            if save:
                new_value = self.current_edit_var.get()

                # 更新单元格值
                if self.current_edit_column == '#0':
                    tree.item(self.current_edit_item, text=new_value)
                else:
                    col_index = int(self.current_edit_column.replace('#', '')) - 1
                    values = list(tree.item(self.current_edit_item, 'values'))
                    if col_index < len(values):
                        values[col_index] = new_value

                        # 自动计算逻辑
                        if self.current_edit_column == '#3':  # PTH列变化
                            self.auto_calculate_pth_values(values, self.current_edit_diameter, new_value)
                        elif self.current_edit_column == '#1':  # 钻头直径变化
                            try:
                                new_diameter = float(new_value)
                                pth_value = values[2] if len(values) > 2 else ""
                                if pth_value:
                                    self.auto_calculate_pth_values(values, new_diameter, pth_value)
                            except:
                                pass

                        tree.item(self.current_edit_item, values=values)

            # 清理编辑控件
            self.current_edit_widget.destroy()

        except Exception as e:
            print(f"完成内联编辑失败: {e}")
        finally:
            # 重置编辑状态
            self.current_edit_widget = None
            self.current_edit_item = None
            self.current_edit_column = None
            self.current_edit_var = None
            self.current_edit_diameter = None

            # 重新聚焦到tree
            tree.focus_set()


    def auto_calculate_pth_values(self, values, diameter, pth_value):
        """自动计算PTH相关的成品孔径和公差"""
        try:
            if pth_value == "Y":  # PTH孔
                finished = round(diameter - 0.1, 3)
                tolerance = "+0.075/-0.075"
            elif pth_value == "N":  # NPTH孔
                finished = round(diameter - 0.05, 3)
                tolerance = "+0.05/-0.05"
            else:  # 空值
                finished = ""
                tolerance = ""

            # 更新values数组
            if len(values) > 3:
                values[3] = str(finished) if finished else ""  # 成品孔径
            if len(values) > 4:
                values[4] = tolerance  # 公差

            # 自动设置备注逻辑
            if len(values) > 8 and pth_value:  # 备注列
                current_remark = values[8]
                if not current_remark:  # 只在备注为空时自动设置
                    remark = self.get_auto_remark(diameter, pth_value)
                    if remark:
                        values[8] = remark

        except Exception as e:
            pass  # 忽略计算错误

    def get_auto_remark(self, diameter, pth_value):
        """根据直径和PTH类型自动获取备注"""
        # 预定义备注映射
        drill_diameter_remarks = {
            0.505: "机台孔",
            2.002: "定位工具孔",
            2.003: "激光定位孔",
            3.175: "工具孔",
            3.176: "防呆孔",
            3.202: "靶孔",
            0.504: "料号孔",
            2.004: "工具孔",
            0.076: "复合靶",
            0.1: "激光钻孔"
        }

        # 检查预定义备注
        predefined_remark = drill_diameter_remarks.get(diameter, "")
        if predefined_remark:
            return predefined_remark

        # 检查是否为无铜孔（小数点第三位是3）
        tmpfloat = diameter
        tmpfloat2 = int(tmpfloat * 100.0) * 10.0
        if abs(tmpfloat * 1000.0 - tmpfloat2 - 3.0) < 0.001:
            return "无铜孔"

        # 根据小数点后第三位判断槽类型
        if pth_value:
            try:
                third_decimal = int((diameter * 1000) % 10)
                if third_decimal == 1:
                    return "长槽"
                elif third_decimal == 2:
                    return "短槽"
            except:
                pass

        return ""

    def save_and_export_optimized(self, editor_window, tools, holes, tree, tree_items, tool_entries, total_holes, file_path):
        """优化版的保存和导出功能"""
        updated_tools = {}
        updated_holes = []

        for tool_id, item in tree_items.items():
            values = tree.item(item, 'values')
            tool_num = int(tool_id[1:])  # 去掉T前缀

            updated_tools[tool_num] = {
                'diameter': float(values[0]) if values[0] else tools.get(tool_num, 0),
                'pth': values[2],
                'finished': values[3],
                'tolerance': values[4],
                'slot_length': values[5],
                'symbol': values[6],
                'grind': values[7],
                'remark': values[8]
            }

        self.save_and_export_simple(editor_window, updated_tools, holes, total_holes, file_path)

    def save_and_export_simple(self, editor_window, tools_data, holes, total_holes, file_path):
        """简化版保存和导出"""
        tool_data = []

        panel_a_totals = {}
        for hole in holes:
            if 'PANEL_A' in hole and '序号' in hole:
                tool_id = hole['序号']
                if tool_id not in panel_a_totals:
                    panel_a_totals[tool_id] = 0
                panel_a_totals[tool_id] += hole['PANEL_A']

        for tool_num, tool_info in tools_data.items():
            tool_id = f'T{tool_num:02d}'
            panel_a_val = panel_a_totals.get(tool_id, 0)

            finished_val = tool_info.get('finished', '')
            if finished_val and str(finished_val).strip():
                try:
                    finished_val = float(finished_val)
                except:
                    finished_val = None
            else:
                finished_val = None

            tool_data.append({
                '序号': tool_id,
                '钻头直径': tool_info.get('diameter', 0),
                '孔数': panel_a_val,
                'PTH': tool_info.get('pth', ''),
                '成品孔径': finished_val,
                '公差': tool_info.get('tolerance', ''),
                '钻槽长度': tool_info.get('slot_length', ''),
                '符号': tool_info.get('symbol', ''),
                '磨次': tool_info.get('grind', ''),
                '备注': tool_info.get('remark', '')
            })
        
        # 添加合计行
        tool_data.append({
            '序号': '合计',
            '钻头直径': None,
            '孔数': sum(panel_a_totals.values()),  # 现在孔数显示PANEL_A总计
            'PTH': None,
            '成品孔径': None,
            '公差': None,
            '钻槽长度': None,
            '符号': None,
            '磨次': None,
            '备注': None
        })
        tools_df = pd.DataFrame(tool_data)
        holes_df = pd.DataFrame(holes)
        progress_window = tk.Toplevel(editor_window)
        progress_window.title("保存中")
        progress_window.geometry("300x100")
        progress_window.transient(editor_window)
        progress_window.grab_set()
        try:
            icon_path = get_icon_path()
            if icon_path:
                progress_window.iconbitmap(icon_path)
        except Exception:
            pass
        
        progress_label = ttk.Label(progress_window, text="正在保存Excel文件...", font=("SimSun", 10))
        progress_label.pack(pady=10)
        
        progress_bar = ttk.Progressbar(progress_window, orient="horizontal", length=250, mode="indeterminate")
        progress_bar.pack(pady=10)
        progress_bar.start(10)
        
        progress_window.update()
        output_path = os.path.splitext(file_path)[0] + '.xlsx'
        def save_excel_file():
            try:
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    tools_df.to_excel(writer, sheet_name='钻头信息', index=False)
                    holes_df.to_excel(writer, sheet_name='钻孔数据', index=False)

                progress_window.destroy()
                editor_window.destroy()
                result = messagebox.askyesno(
                    "处理成功", 
                    f"文件已转换为Excel格式:\n{output_path}\n\n是否立即打开该文件？"
                )
                
                # 如果用户选择打开文件
                if result:
                    try:
                        os.startfile(output_path)
                    except:
                        messagebox.showinfo("提示", f"请手动打开文件:\n{output_path}")
                
                # 更新状态
                self.status_label.config(text="转换完成")
                
            except Exception as e:
                # 关闭进度窗口并显示错误
                if progress_window.winfo_exists():
                    progress_window.destroy()
                messagebox.showerror("保存失败", f"导出Excel时出错: {str(e)}")
        
        # 启动保存线程
        save_thread = threading.Thread(target=save_excel_file)
        save_thread.daemon = True
        save_thread.start()

def main():
    import tkinter as tk
    from tkinter import messagebox
    # 命令行参数处理
    parser = argparse.ArgumentParser(description='DRL文件转Excel工具')
    parser.add_argument('--file', '-f', help='要处理的DRL文件路径')
    parser.add_argument('--d25rkey', type=int, help='工程主键 (对应C#中的vD25RKEY)')
    parser.add_argument('--flow_whse_ptr', type=int, help='流程仓库指针 (对应C#中的vFLOW_WHSE_PTR)')
    parser.add_argument('--frontend_pnl_pcs', type=int, help='前端传递的pnl_pcs参数 (对应C#中URL的pnl_pcs)')
    parser.add_argument('--drill_radio_group1', type=int, help='钻孔计算模式 (对应C#中的drillRadioGroup1: 0=乘法, 1=除法, 2=直接)')
    args = parser.parse_args()
    
    # 如果提供了文件参数，直接处理文件
    if args.file:
        try:
            # 创建根窗口但不显示
            root = tk.Tk()
            root.withdraw()
            
            # 验证许可证
            validator = LicenseValidator()
            if not validator.is_valid():
                sys.exit(1)

            # 创建应用实例
            app = DrlConverterApp(root, d25rkey=args.d25rkey, flow_whse_ptr=args.flow_whse_ptr,
                                frontend_pnl_pcs=args.frontend_pnl_pcs, drill_radio_group1=args.drill_radio_group1)

            # 处理文件
            tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, simple_coord_counts = app.parse_drl_file(args.file)

            # 退出
            root.destroy()
            return
        except Exception as e:
            return
    
    # 捕获未处理的异常
    def handle_exception(exc_type, exc_value, exc_traceback):
        import traceback
        # 获取异常信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        # 显示错误对话框
        messagebox.showerror("程序错误", f"程序遇到未处理的异常:\n{error_msg}")
        sys.exit(1)
    
    # 设置异常处理器
    sys.excepthook = handle_exception
    
    try:
        # 创建主窗口前优化Tkinter设置
        root = tk.Tk()
        # 设置DPI感知
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
        # 设置窗口属性
        root.title("JSYPCB钻带转Excel工具")
        root.resizable(True, True)  # 允许调整大小

        # 设置程序图标
        try:
            icon_path = get_icon_path()
            if icon_path:
                root.iconbitmap(icon_path)
        except Exception:
            pass  # 如果图标设置失败，继续运行程序
        # 设置窗口居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = 400
        window_height = 250
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 验证许可证
        validator = LicenseValidator()
        if not validator.is_valid():
            root.withdraw()
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            exit(1)
    
        # 创建应用实例
        app = DrlConverterApp(root)
        # 配置应用样式
        style = ttk.Style()
        if "vista" in style.theme_names():
            style.theme_use("vista")
        elif "clam" in style.theme_names():
            style.theme_use("clam")
        
        # 在Windows上配置按钮样式
        if os.name == 'nt':
            style.configure("Accent.TButton", 
                           background="#4CAF50", 
                           foreground="black",  
                           font=("SimSun", 10, "bold"))
        
        # 设置关闭窗口处理
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出程序吗?"):
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动主循环
        root.mainloop()

    except Exception as e:
        # 显示错误对话框
        if 'root' in locals():
            root.withdraw()
        messagebox.showerror("启动错误", f"程序启动时出错:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()