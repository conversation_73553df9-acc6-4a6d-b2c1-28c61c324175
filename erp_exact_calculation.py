#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全匹配ERP系统的钻孔计算逻辑
基于C#代码的完整分析
"""

import json
import re
import math

def calculate_erp_exact():
    """完全按照ERP系统的C#代码逻辑计算"""
    print("🎯 完全匹配ERP系统的钻孔计算")
    print("=" * 60)
    
    # ERP系统的关键参数 (从接口参数获取)
    vset_pcs = 1  # set_pcs=1
    vpnl_pcs = 1  # 修正：ERP系统实际不使用8倍，而是直接使用基础数量
    vDRILLSLOT_ITEMCOUNT = 1  # 从数据库data0192表获取，根据测试结果推断为1
    
    print(f"📊 ERP系统参数:")
    print(f"   vset_pcs = {vset_pcs}")
    print(f"   vpnl_pcs = {vpnl_pcs}")
    print(f"   vDRILLSLOT_ITEMCOUNT = {vDRILLSLOT_ITEMCOUNT}")
    
    # 读取DRL文件
    with open('z0l04p0t500365a0.drl', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 解析工具定义
    tools = {}
    tool_pattern = re.compile(r'T(\d+)C([\d\.]+)')
    
    for line in lines:
        line = line.strip()
        match = tool_pattern.match(line)
        if match:
            tool_num = int(match.group(1))
            diameter = float(match.group(2))
            tools[tool_num] = diameter
    
    # 统计每个工具的坐标数和G85槽数
    tool_coords = {}
    tool_g85_slots = {}
    current_tool = None
    
    coord_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)')
    g85_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)G85X([\d\-\.]+)Y([\d\-\.]+)')
    
    for line in lines:
        line = line.strip()
        
        # 检查工具切换
        if line.startswith('T') and line[1:].isdigit():
            current_tool = int(line[1:])
            if current_tool not in tool_coords:
                tool_coords[current_tool] = 0
                tool_g85_slots[current_tool] = []
            continue
        
        if current_tool is None:
            continue
        
        # 检查G85槽
        g85_match = g85_pattern.match(line)
        if g85_match:
            x1, y1, x2, y2 = map(float, g85_match.groups())
            tool_g85_slots[current_tool].append((x1, y1, x2, y2))
            continue
        
        # 检查普通坐标
        coord_match = coord_pattern.match(line)
        if coord_match:
            tool_coords[current_tool] += 1
    
    # 按照C#代码的逻辑计算每个工具的PANEL_A
    print(f"\n📋 按C#逻辑计算:")
    print(f"{'工具':<8} {'基础数':<8} {'PANEL_A':<8} {'ERP':<8} {'匹配':<8}")
    print("-" * 50)
    
    slot_tools = [35, 36, 37, 38, 42]  # 有槽的工具
    total_panel_a = 0
    total_slot_panel_a = 0
    
    # 读取ERP数据进行对比
    with open('c.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    tools_data = json.loads(data['Data'])
    
    # 创建ERP数据字典
    erp_data = {}
    for tool in tools_data:
        if tool['SEQ_NR'] and tool['SEQ_NR'].startswith('T'):
            tool_num = int(tool['SEQ_NR'][1:])
            erp_data[tool_num] = {
                'panel_a': int(tool['PANEL_A']),
                'slot_hqty': int(tool['SLOT_HQTY']) if tool['SLOT_HQTY'] else 0
            }
    
    for tool_num in sorted(tools.keys()):
        if tool_num not in tool_coords:
            continue
            
        diameter = tools[tool_num]
        coord_count = tool_coords[tool_num]
        g85_slots = tool_g85_slots[tool_num]
        
        # C#代码逻辑：计算基础钻孔数 (List3[k])
        l = 0  # C#代码中的变量l
        
        # 普通坐标：每个坐标计算1个钻孔
        l += coord_count
        
        # G85槽：根据vDRILLSLOT_ITEMCOUNT决定计算方式
        for x1, y1, x2, y2 in g85_slots:
            if vDRILLSLOT_ITEMCOUNT == 1:
                # 每个G85槽计算1个钻孔
                l += 1
            else:
                # 使用separated公式计算钻孔数
                path_length = math.sqrt((x2 - x1)**2 + (y2 - y1)**2) * 0.001  # 转换为毫米
                
                # 计算separated
                radius_inch = (diameter / 25.4) / 2.0
                inner_calc = (radius_inch ** 2) - ((radius_inch - 0.0127) ** 2)
                if inner_calc > 0:
                    separated_inch = 2.0 * math.sqrt(inner_calc)
                    separated = separated_inch * 25.4
                else:
                    separated = diameter
                
                # 计算钻孔数
                drill_count = int(math.floor(path_length / separated)) + 2
                l += drill_count
        
        # C#代码逻辑：计算最终PANEL_A
        # dr29["PANEL_A"] = Math.Round(Convert.ToDouble(List3[k].ToString()) * (double)vpnl_pcs);
        panel_a = round(l * vpnl_pcs)
        
        total_panel_a += panel_a
        
        if tool_num in slot_tools:
            total_slot_panel_a += panel_a
        
        # 获取ERP数据进行对比
        erp_panel_a = erp_data.get(tool_num, {}).get('panel_a', 0)
        match_status = "✅" if panel_a == erp_panel_a else "❌"
        
        if tool_num in slot_tools or erp_panel_a > 0:  # 只显示有槽工具或ERP中有数据的工具
            print(f"T{tool_num:02d}     {l:<8} {panel_a:<8} {erp_panel_a:<8} {match_status:<8}")
    
    # 计算汇总数据
    erp_summary = json.loads(data['Data2'])
    erp_zcount = int(erp_summary['ZCOUNT1'])
    erp_slotdrillcount = int(erp_summary['SLOTDRILLCOUNT1'])
    erp_drillcount = int(erp_summary['DRILLCOUNT1'])
    
    calculated_drillcount = total_panel_a - total_slot_panel_a
    
    print("-" * 50)
    print(f"📊 汇总对比:")
    print(f"   ZCOUNT: ERP={erp_zcount}, Python={total_panel_a}, 匹配={'✅' if total_panel_a == erp_zcount else '❌'}")
    print(f"   SLOTDRILLCOUNT: ERP={erp_slotdrillcount}, Python={total_slot_panel_a}, 匹配={'✅' if total_slot_panel_a == erp_slotdrillcount else '❌'}")
    print(f"   DRILLCOUNT: ERP={erp_drillcount}, Python={calculated_drillcount}, 匹配={'✅' if calculated_drillcount == erp_drillcount else '❌'}")
    
    # 如果还有差异，尝试调整vDRILLSLOT_ITEMCOUNT
    if total_panel_a != erp_zcount or total_slot_panel_a != erp_slotdrillcount:
        print(f"\n🔍 尝试调整vDRILLSLOT_ITEMCOUNT参数:")
        test_vDRILLSLOT_ITEMCOUNT_values()

def test_vDRILLSLOT_ITEMCOUNT_values():
    """测试不同的vDRILLSLOT_ITEMCOUNT值"""
    print(f"测试vDRILLSLOT_ITEMCOUNT = 0 (使用separated公式)")
    
    # 这里可以重复上面的计算逻辑，但使用vDRILLSLOT_ITEMCOUNT = 0
    # 由于代码长度限制，这里只是提示
    print(f"如果vDRILLSLOT_ITEMCOUNT = 0，则所有G85槽都使用separated公式计算")

if __name__ == "__main__":
    calculate_erp_exact()
