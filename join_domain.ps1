# 加入域的 PowerShell 脚本
# 作者：AI Assistant
# 功能：将 Windows 计算机加入到 PXJSY.COM 域

# 需要以管理员权限运行此脚本

# 检查是否以管理员权限运行
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $isAdmin) {
    Write-Host "错误：此脚本需要管理员权限运行！" -ForegroundColor Red
    Write-Host "请右键点击 PowerShell，选择'以管理员身份运行'，然后重新运行此脚本。" -ForegroundColor Yellow
    Exit 1
}

# 设置域相关变量
$domainName = "PXJSY.COM"

try {
    # 提示用户输入域管理员凭据
    Write-Host "请输入域管理员账号信息..." -ForegroundColor Green
    $credential = Get-Credential -Message "请输入域管理员账号和密码"

    # 加入域
    Write-Host "正在尝试加入域 $domainName ..." -ForegroundColor Yellow
    Add-Computer -DomainName $domainName -Credential $credential -Force -Restart

    Write-Host "成功加入域 $domainName！" -ForegroundColor Green
    Write-Host "系统将在 30 秒后重启..." -ForegroundColor Yellow
    
} catch {
    Write-Host "错误：加入域失败！" -ForegroundColor Red
    Write-Host "错误信息: $_" -ForegroundColor Red
    Write-Host "请检查以下可能的问题：" -ForegroundColor Yellow
    Write-Host "1. 确保计算机能够连接到域控制器" -ForegroundColor Yellow
    Write-Host "2. 确保输入的域管理员账号和密码正确" -ForegroundColor Yellow
    Write-Host "3. 确保 DNS 设置正确，能够解析域名" -ForegroundColor Yellow
    Write-Host "4. 检查域控制器是否正常运行" -ForegroundColor Yellow
    Exit 1
} 