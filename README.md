# Jsy Upload Windows Service

这是一个Windows服务，用于自动上传Jsy PCB数据。

## 安装要求

- Python 3.7 或更高版本
- Windows 操作系统
- 管理员权限

## 安装步骤

1. 安装所需的Python包：
```bash
pip install -r requirements.txt
```

2. 以管理员身份打开命令提示符，进入项目目录

3. 安装服务：
```bash
python JsyUploadService.py install
```

4. 启动服务：
```bash
python JsyUploadService.py start
```

## 服务管理命令

- 启动服务：`python JsyUploadService.py start`
- 停止服务：`python JsyUploadService.py stop`
- 重启服务：`python JsyUploadService.py restart`
- 删除服务：`python JsyUploadService.py remove`

## 查看服务状态

1. 打开服务管理器（services.msc）
2. 查找名为"Jsy Upload Service"的服务
3. 可以查看服务状态、启动类型等信息

## 日志

服务日志会记录在Windows事件日志中，可以通过事件查看器查看。

# API 测试脚本

这个项目包含了几个用于测试海康威视 API 的 Python 脚本。

## 准备工作

1. 确保已安装 Python（建议 Python 3.7+）
2. 安装必要的依赖：
   ```
   pip install requests
   ```

## 脚本说明

### 1. test_api.py

基本的 API 调用流程测试，模拟在 Apifox 中的操作：
- 登录并获取 token
- 使用 token 访问第二个 API

### 2. test_token_expiry.py

测试 token 有效期：
- 获取 token 后立即进行 API 调用
- 间隔 30 秒、60 秒和 120 秒后再次调用，检查 token 是否过期

### 3. test_auth_formats.py

测试不同的 Authorization 头格式：
- 标准 Bearer 格式（Bearer token）
- 无空格 Bearer 格式（Bearertoken）
- 小写 bearer 格式（bearer token）
- 只有 token
- Token 前缀（Token token）
- Basic 认证（Basic token）

### 4. test_hikiot_web_api.py

测试 hikiot web API：
- 使用提供的长字符串作为请求体
- 发送 POST 请求并检查响应

## 使用方法

1. 打开需要测试的脚本文件
2. 修改登录凭证（替换您的实际密码）：
   ```python
   login_data = {
       "phone": "19136753172",  # 替换为您的实际手机号
       "password": "请在此处填写您的实际密码"  # 替换为您的实际密码
   }
   ```
3. 运行脚本：
   ```
   python test_api.py
   ```
   或
   ```
   python test_token_expiry.py
   ```
   或
   ```
   python test_auth_formats.py
   ```
   或
   ```
   python test_hikiot_web_api.py
   ```

## 注意事项

- 所有脚本都已禁用 SSL 验证，以解决可能的 SSL 连接问题
- 如果仍然遇到网络问题，可能是由于服务器限制或网络环境问题
- 请确保在使用脚本前替换实际的登录凭证 

# 海康考勤系统打包说明

## 文件说明
- `api.py`: 海康威视数据同步工具
- `月度考勤/out.py`: 考勤计算工具
- `build.bat`: 打包脚本
- `start.bat`: 启动脚本
- `install_dependencies.bat`: 安装依赖项脚本
- `requirements.txt`: 依赖项列表

## 打包步骤

### 1. 准备环境
确保你的电脑上已安装Python环境（推荐3.8或更高版本）。

### 2. 安装依赖项
双击运行 `install_dependencies.bat` 文件，安装所需的Python库。

### 3. 打包应用
双击运行 `build.bat` 文件，系统将自动打包 api.py 和 out.py 生成可执行文件。
打包完成后，生成的可执行文件将保存在 `release` 文件夹中：
- `海康数据同步.exe`: 用于同步海康威视数据
- `考勤计算.exe`: 用于计算考勤数据

### 4. 使用说明
在无Python环境的电脑上，将 `release` 文件夹中的两个可执行文件和 `start.bat` 复制到同一个文件夹，然后：

1. 双击运行 `start.bat`，系统将先启动"海康数据同步.exe"
2. 等待5分钟后，系统会自动启动"考勤计算.exe"

也可以单独运行每个程序：
- 双击运行 `海康数据同步.exe` 下载当天的考勤数据
- 双击运行 `考勤计算.exe` 处理考勤数据并生成报表

## 注意事项
- 确保电脑具有网络连接，以便数据同步工具能够访问海康威视服务器
- 第一次运行时可能需要配置防火墙权限
- 程序运行时可能需要管理员权限 