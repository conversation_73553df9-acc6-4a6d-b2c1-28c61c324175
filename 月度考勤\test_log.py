#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试日志功能
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入sync.py中的日志模块
from sync import LoggerSetup, logger, log_setup

def test_logging():
    """测试日志功能"""
    print("🧪 测试日志功能")
    print("=" * 50)
    
    # 测试不同级别的日志
    logger.info("这是一条INFO级别的日志")
    logger.warning("这是一条WARNING级别的日志")
    logger.error("这是一条ERROR级别的日志")
    logger.debug("这是一条DEBUG级别的日志")
    
    # 测试中文字符
    logger.info("测试中文字符：你好世界！🌍")
    
    # 测试异常记录
    try:
        1 / 0
    except Exception as e:
        log_setup.log_exception(e, "测试异常")
    
    # 测试API请求记录
    test_headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
    }
    test_data = {
        'username': 'test',
        'password': 'test'
    }
    
    log_setup.log_api_request("POST", "https://api.test.com/login", test_headers, test_data)
    
    # 模拟API响应
    class MockResponse:
        def __init__(self, status_code, text):
            self.status_code = status_code
            self.text = text
    
    success_response = MockResponse(200, '{"code": 0, "msg": "success"}')
    log_setup.log_api_response(success_response, True)
    
    error_response = MockResponse(500, '{"code": 1, "msg": "server error"}')
    log_setup.log_api_response(error_response, False)
    
    # 等待一秒确保日志写入
    time.sleep(1)
    
    print("\n✅ 日志测试完成")
    print(f"📝 日志文件位置: {log_setup.log_file_path}")
    
    # 检查日志文件是否存在
    if log_setup.log_file_path and os.path.exists(log_setup.log_file_path):
        file_size = os.path.getsize(log_setup.log_file_path)
        print(f"📊 日志文件大小: {file_size} 字节")
        
        # 读取并显示日志文件内容
        print("\n📄 日志文件内容预览:")
        print("-" * 50)
        try:
            with open(log_setup.log_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                for i, line in enumerate(lines[:10]):  # 只显示前10行
                    if line.strip():
                        print(f"{i+1:2d}: {line}")
                if len(lines) > 10:
                    print(f"... 还有 {len(lines) - 10} 行")
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("❌ 日志文件不存在")

if __name__ == "__main__":
    try:
        test_logging()
    finally:
        # 关闭日志系统
        log_setup.close_logger()
        input("\n按回车键退出...")
