@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
echo 🎯 sync.py 打包工具
echo ================================================

echo 📦 安装依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成

echo 🔨 开始打包...

REM 检查是否有Excel文件
set EXCEL_FILES=
for %%f in (*.xlsx) do (
    echo 📄 找到Excel文件: %%f
    set EXCEL_FILES=!EXCEL_FILES! --add-data="%%f;."
)

if defined EXCEL_FILES (
    echo 📄 添加Excel文件到打包...
    pyinstaller --onefile --windowed --name="月度考勤同步工具" !EXCEL_FILES! --hidden-import=pandas --hidden-import=openpyxl --hidden-import=requests --hidden-import=tkinter --clean sync.py
) else (
    echo ℹ️ 没有找到Excel文件，跳过添加...
    pyinstaller --onefile --windowed --name="月度考勤同步工具" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=requests --hidden-import=tkinter --clean sync.py
)

if %errorlevel% neq 0 (
    echo ❌ 打包失败，尝试创建调试版本...

    if defined EXCEL_FILES (
        pyinstaller --onefile --name="月度考勤同步工具_调试版" !EXCEL_FILES! --hidden-import=pandas --hidden-import=openpyxl --hidden-import=requests --hidden-import=tkinter --clean sync.py
    ) else (
        pyinstaller --onefile --name="月度考勤同步工具_调试版" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=requests --hidden-import=tkinter --clean sync.py
    )
)

echo 📁 复制输出文件...
if exist "dist\*.exe" (
    copy "dist\*.exe" .
    echo ✅ 文件复制完成
) else (
    echo ❌ 没有找到生成的exe文件
)

echo 🧹 清理临时文件...
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.spec" del "*.spec"

echo 🎉 打包完成！
echo 📁 生成的文件:
dir *.exe

echo.
echo 💡 使用说明:
echo    - 生成的exe文件可以在没有Python环境的Windows机器上运行
echo    - 如果遇到问题，请使用调试版本查看错误信息
echo.

pause
