import os
import sys
import subprocess
import ctypes
from getpass import getpass

def is_admin():
    """检查脚本是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def join_domain(domain_name, username, password):
    """加入域的主要函数"""
    # 构建 PowerShell 命令
    ps_command = f'''
    $securePass = ConvertTo-SecureString '{password}' -AsPlainText -Force;
    $credential = New-Object System.Management.Automation.PSCredential('{username}', $securePass);
    Add-Computer -DomainName {domain_name} -Credential $credential -Force -Restart;
    '''
    
    try:
        # 执行 PowerShell 命令
        result = subprocess.run(
            ['powershell', '-Command', ps_command],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"\n[成功] 已成功加入域 {domain_name}")
            print("系统将在稍后重启...")
            return True
        else:
            print(f"\n[错误] 加入域失败！")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"\n[错误] 执行过程中出现异常：{str(e)}")
        return False

def main():
    # 检查管理员权限
    if not is_admin():
        print("[错误] 此脚本需要管理员权限运行！")
        print("请右键点击 Python 脚本，选择'以管理员身份运行'。")
        input("按回车键退出...")
        sys.exit(1)

    # 设置域相关信息
    DOMAIN_NAME = "PXJSY"
    DOMAIN_USERNAME = "gabe"
    DOMAIN_PASSWORD = "wwxkpo35781"
    
    print("=== Windows 加入域工具 ===")
    print(f"目标域名: {DOMAIN_NAME}")
    print(f"域管理员: {DOMAIN_USERNAME}")
    print("------------------------")
    
    # 确认操作
    confirm = input("\n是否开始加入域操作？(y/n): ")
    
    if confirm.lower() == 'y':
        print("\n正在尝试加入域...")
        if join_domain(DOMAIN_NAME, DOMAIN_USERNAME, DOMAIN_PASSWORD):
            input("\n按回车键重启计算机...")
            os.system("shutdown /r /t 30")
    else:
        print("\n操作已取消")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()