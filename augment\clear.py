import os
import sqlite3
import shutil
import platform

# 配置部分
SEARCH_KEYWORD = "augment"# 搜索关键词

def get_vscode_db_path():
    """智能获取各操作系统下的VSCode数据库路径"""
    system = platform.system()
    try:
        if system == "Darwin":  # macOS
            return os.path.expanduser("~/Library/Application Support/Code/User/globalStorage/state.vscdb")
        elif system == "Windows":
            return os.path.join(os.getenv('APPDATA'), "Code", "User", "globalStorage", "state.vscdb")
        elif system == "Linux":
            return os.path.expanduser("~/.config/Code/User/globalStorage/state.vscdb")
    except Exception as e:
        print(f"路径获取错误: {e}")
        returnNone

def clean_vscode_db():
    """主清理函数"""
    db_path = get_vscode_db_path()
    if not db_path or not os.path.exists(db_path):
        print("数据库路径错误，请检查VSCode是否安装")
        return

    # 创建备份（安全第一）
    backup_path = f"{db_path}.backup"
    try:
        shutil.copy2(db_path, backup_path)
    except IOError as e:
        print(f"备份失败: {e}")
        return

    # 数据库操作
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            # 查询匹配记录
            cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE ?", (f'%{SEARCH_KEYWORD}%',))
            count = cursor.fetchone()[0]
            
            if count > 0:
                cursor.execute("DELETE FROM ItemTable WHERE key LIKE ?", (f'%{SEARCH_KEYWORD}%',))
                conn.commit()
                print(f"成功删除{count}条Augment相关记录")
            else:
                print("未找到需要清理的数据")
    except sqlite3.Error as e:
        print(f"数据库操作出错: {e}")

if __name__ == "__main__":
    clean_vscode_db()