#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from datetime import datetime
from pathlib import Path

def test_direct_logging():
    """直接测试日志文件写入"""
    print("开始直接日志测试...")
    
    try:
        # 创建D盘logs目录
        log_dir = Path("D:/logs")
        print(f"尝试创建目录: {log_dir}")
        log_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ 目录创建成功: {log_dir}")
        
        # 生成日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"direct_test_{timestamp}.log"
        print(f"尝试创建文件: {log_file}")
        
        # 直接写入文件
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - INFO - 直接日志测试开始\n")
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - INFO - 这是一条测试日志\n")
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - INFO - 测试中文字符：你好世界！🌍\n")
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - INFO - 直接日志测试结束\n")
        
        print(f"✅ 文件写入成功: {log_file}")
        
        # 检查文件是否存在
        if log_file.exists():
            file_size = log_file.stat().st_size
            print(f"✅ 文件大小: {file_size} 字节")
            
            # 读取并显示内容
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print("\n📄 文件内容:")
                print("-" * 50)
                print(content)
                print("-" * 50)
        else:
            print("❌ 文件不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_direct_logging()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    input("按回车键退出...")
