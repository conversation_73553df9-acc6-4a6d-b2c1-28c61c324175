import win32serviceutil
import win32service
import win32event
import servicemanager
import os
import sys

# 添加当前目录到 PYTHONPATH
sys.path.append(os.path.dirname(sys.argv[0]))

import JsyUpload  # 导入您的主程序模块


class JsyUploadService(win32serviceutil.ServiceFramework):
    _svc_name_ = "JsyJinZhouUploadService"
    _svc_display_name_ = "金洲数据上传服务"
    _svc_description_ = "定时从数据库读取数据并上传至金洲系统"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_alive = True

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ""))
        try:
            JsyUpload.main()  # 启动你的主函数
        except Exception as e:
            servicemanager.LogMsg(servicemanager.EVENTLOG_ERROR_TYPE,
                                  servicemanager.PYS_SERVICE_STOPPED,
                                  (self._svc_name_, f"Error: {e}"))

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False


if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(JsyUploadService)