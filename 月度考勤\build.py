#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
sync.py 打包脚本
使用PyInstaller将Python程序打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def build_executable():
    """使用PyInstaller打包可执行文件"""
    print("🔨 开始打包...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # Windows下隐藏控制台窗口（因为使用了tkinter GUI）
        "--name=月度考勤同步工具",        # 可执行文件名称
        "--hidden-import=pandas",       # 确保pandas被包含
        "--hidden-import=openpyxl",     # 确保openpyxl被包含
        "--hidden-import=requests",     # 确保requests被包含
        "--hidden-import=tkinter",      # 确保tkinter被包含
        "--clean",                      # 清理临时文件
        "sync.py"                       # 主程序文件
    ]

    # 检查并添加图标文件
    if os.path.exists("icon.ico"):
        cmd.insert(-1, "--icon=icon.ico")

    # 检查并添加Excel文件
    import glob
    xlsx_files = glob.glob("*.xlsx")
    if xlsx_files:
        for xlsx_file in xlsx_files:
            cmd.insert(-1, f"--add-data={xlsx_file};.")
            print(f"📄 找到Excel文件: {xlsx_file}")
    else:
        print("ℹ️  没有找到Excel文件，跳过添加")
    
    try:
        print(f"🔨 执行命令: {' '.join(cmd)}")
        # 执行打包命令
        subprocess.check_call(cmd)
        print("✅ 打包完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False

def create_simple_build():
    """创建简化版本的打包（不使用--windowed，便于调试）"""
    print("🔨 创建调试版本...")

    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--name=月度考勤同步工具_调试版",  # 可执行文件名称
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=requests",
        "--hidden-import=tkinter",
        "--clean",
        "sync.py"
    ]

    # 检查并添加Excel文件
    import glob
    xlsx_files = glob.glob("*.xlsx")
    if xlsx_files:
        for xlsx_file in xlsx_files:
            cmd.insert(-1, f"--add-data={xlsx_file};.")
            print(f"📄 添加Excel文件: {xlsx_file}")
    else:
        print("ℹ️  没有找到Excel文件，跳过添加")
    
    try:
        subprocess.check_call(cmd)
        print("✅ 调试版本打包完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 调试版本打包失败: {e}")
        return False

def copy_output():
    """复制输出文件到当前目录"""
    dist_dir = Path("dist")
    if dist_dir.exists():
        print("📁 复制输出文件...")
        for file in dist_dir.glob("*.exe"):
            shutil.copy2(file, ".")
            print(f"✅ 已复制: {file.name}")

def cleanup():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["*.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已删除目录: {dir_name}")
    
    import glob
    for pattern in files_to_remove:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"✅ 已删除文件: {file}")

def main():
    """主函数"""
    print("🎯 sync.py 打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("sync.py"):
        print("❌ 找不到sync.py文件，请确保在正确的目录下运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装完成")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建两个版本
    print("\n🔨 创建正式版本（带GUI）...")
    build_success = build_executable()
    
    print("\n🔨 创建调试版本（带控制台）...")
    debug_success = create_simple_build()
    
    if build_success or debug_success:
        copy_output()
        cleanup()
        
        print("\n🎉 打包完成！")
        print("📁 输出文件:")
        for file in Path(".").glob("*.exe"):
            size = file.stat().st_size / (1024 * 1024)  # MB
            print(f"   {file.name} ({size:.1f} MB)")
        
        print("\n💡 使用说明:")
        print("   - 正式版本: 适合最终用户使用，无控制台窗口")
        print("   - 调试版本: 适合调试，有控制台窗口显示错误信息")
        print("   - 可执行文件可以在没有Python环境的Windows机器上运行")
        
        return True
    else:
        print("❌ 打包失败")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
