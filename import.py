import pandas as pd
from datetime import datetime
import os
import openpyxl

def update_all_excel():
    try:
        # 读取两个Excel文件
        out_file = 'D:\\origin\\out_20250330.xlsx'
        all_file = 'D:\\origin\\all.xlsx'
        new_file = 'D:\\origin\\all_new.xlsx'

        if not os.path.exists(out_file) or not os.path.exists(all_file):
            print("文件不存在")
            return

        # 读取out文件的数据
        df_out = pd.read_excel(out_file)

        # 使用openpyxl读取原始文件以保持格式
        wb = openpyxl.load_workbook(all_file)
        ws = wb.active

        print("开始更新数据...")

        # 遍历out文件中的每一行
        for _, row in df_out.iterrows():
            name = row['姓名']
            normal_hours = row['正班']
            overtime_hours = row['加班']

            # 在Excel中查找对应的行
            for row_idx in range(3, ws.max_row + 1):  # 从第3行开始（跳过表头）
                if ws.cell(row=row_idx, column=3).value == name:  # 姓名在第3列
                    if ws.cell(row=row_idx, column=5).value == '正班':  # 计时方式在第5列
                        ws.cell(row=row_idx, column=43).value = normal_hours  # 29日在第43列
                        print(f"已更新 {name} 的正班时间")
                    elif ws.cell(row=row_idx, column=5).value == '加班' and overtime_hours:
                        ws.cell(row=row_idx, column=43).value = overtime_hours
                        print(f"已更新 {name} 的加班时间")

        # 保存为新文件
        wb.save(new_file)
        print(f"\n数据已保存到新文件: {new_file}")
        print("\n请注意：")
        print(f"1. 新数据已保存到: {new_file}")
        print(f"2. 请手动检查新文件内容")
        print(f"3. 如果确认无误，请将新文件替换原文件")

    except Exception as e:
        print(f"更新失败: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    update_all_excel()