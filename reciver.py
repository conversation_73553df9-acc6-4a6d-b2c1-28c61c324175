from flask import Flask, request, jsonify
import json
import hashlib
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad, unpad
import base64
import os

app = Flask(__name__)

# 配置Verification Token和Encrypt Key
VERIFICATION_TOKEN = '95d07619ebc34375967c60a1171c703b'
ENCRYPT_KEY = '7d6b6490e6ab4cc8976ecb3224be77a1'  

def get_aes_key(encrypt_key):
    """
    使用SHA256生成AES密钥
    """
    return hashlib.sha256(encrypt_key.encode()).digest()

def decrypt_event(encrypted_event_base64):
    """
    解密事件内容
    1. Base64解码
    2. 提取IV和加密数据
    3. 使用AES-CBC模式解密
    """
    encrypted_event = base64.b64decode(encrypted_event_base64)
    iv = encrypted_event[:16]
    encrypted_data = encrypted_event[16:]
    
    key = get_aes_key(ENCRYPT_KEY)
    cipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted_padded_data = cipher.decrypt(encrypted_data)
    decrypted_data = unpad(decrypted_padded_data, AES.block_size)
    
    return decrypted_data.decode('utf-8')

def encrypt_event(plaintext):
    """
    加密事件内容
    1. 生成随机IV
    2. 填充数据
    3. 使用AES-CBC模式加密
    4. Base64编码
    """
    iv = os.urandom(16)  # 生成16字节的随机IV
    key = get_aes_key(ENCRYPT_KEY)
    
    # 填充数据
    padded_data = pad(plaintext.encode('utf-8'), AES.block_size)
    
    # 创建AES加密器
    cipher = AES.new(key, AES.MODE_CBC, iv)
    encrypted_data = cipher.encrypt(padded_data)
    
    # 合并IV和加密数据
    encrypted_event = iv + encrypted_data
    
    # Base64编码
    encrypted_event_base64 = base64.b64encode(encrypted_event).decode('utf-8')
    
    return encrypted_event_base64

from flask import Flask, request, jsonify
import json

app = Flask(__name__)

@app.route('/web', methods=['POST'])
def handle_webhook():
    """
    处理webhook回调
    """
    try:
        # 打印完整的请求信息
        print("\n=== 请求详情 ===")
        print(f"请求方法: {request.method}")
        print(f"请求URL: {request.url}")
        print("\n--- 请求头 ---")
        for header, value in request.headers.items():
            print(f"{header}: {value}")
        
        print("\n--- 请求体 ---")
        try:
            body = request.get_data().decode('utf-8')
            print(f"原始请求体: {body}")
            print(f"JSON格式化后: {json.dumps(request.json, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"请求体解析失败: {e}")
        
        print("\n--- 请求参数 ---")
        print(f"URL参数: {dict(request.args)}")
        print(f"表单数据: {dict(request.form)}")
        print("================\n")

        # 直接返回成功
        return jsonify({"status": "success"}), 200
            
    except Exception as e:
        print(f"发生异常: {e}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("服务器启动在 http://0.0.0.0:80")
    app.run(host='0.0.0.0', port=80, debug=True)