import pandas as pd

# 输入/输出路径
input_path = r'D:\attendance\6\inserted_and_colored_calculated_modified_origin.xlsx'
output_path = r'D:\attendance\6\final_output.xlsx'

# 读取 Excel 文件（不设置 header，默认第一行为数据行）
df = pd.read_excel(input_path, header=None)

# 获取所有奇数行（行号 % 2 == 1 → 第1、3、5...行）
odd_rows_df = df.iloc[[i for i in range(len(df)) if (i + 1) % 2 == 1]]

# 保存结果到新文件（如果你希望保留标题行作为第1行，不需要额外处理）
odd_rows_df.to_excel(output_path, index=False, header=False)

print(f"✅ 已删除所有偶数行（按实际行号判断）")
print(f"📁 结果已保存至: {output_path}")