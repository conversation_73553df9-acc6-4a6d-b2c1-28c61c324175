#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量调用工作流接口脚本
从a.json文件中提取procInstId，遍历调用moveByTaskName接口
"""

import json
import requests
import time
from datetime import datetime

def load_json_data():
    """加载a.json文件"""
    try:
        with open('a.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 成功加载a.json文件")
        return data
    except FileNotFoundError:
        print(f"❌ 文件不存在: a.json")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        return None

def extract_proc_inst_ids(data):
    """从JSON数据中提取所有procInstId"""
    proc_inst_ids = []
    
    def find_proc_inst_ids(obj, path=""):
        """递归查找procInstId"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                # 调试信息：打印所有键名
                if "ProcInstId" in key or "procInstId" in key:
                    print(f"🔍 检查键: {key} = {value} (路径: {current_path})")
                if key == "ProcInstId" and isinstance(value, str) and value.strip():
                    proc_inst_ids.append(value.strip())
                    print(f"✅ 找到ProcInstId: {value} (路径: {current_path})")
                else:
                    find_proc_inst_ids(value, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]"
                find_proc_inst_ids(item, current_path)
    
    find_proc_inst_ids(data)
    
    # 去重
    unique_ids = list(set(proc_inst_ids))
    if len(unique_ids) != len(proc_inst_ids):
        print(f"📝 去重后: {len(proc_inst_ids)} -> {len(unique_ids)} 个procInstId")
    
    return unique_ids

def call_workflow_api(proc_inst_id):
    """调用工作流接口"""
    url = "http://*********:8001/yudaoWorkFlow/moveByTaskName"
    
    headers = {
        'sign': 'aab6fe515250a7f53fd1b6c87cc01088',
        'time': '638856303391158912',
        'appkey': 'erpclient',
        'loginuser': '504202624',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': '*********:8001',
        'Connection': 'keep-alive'
    }
    
    payload = {
        "procInstId": proc_inst_id,
        "taskName": "财务主管审核",
        "toTaskName": "财务主管审核"
    }
    
    try:
        print(f"📤 调用接口: procInstId={proc_inst_id}")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 调用成功: {result}")
                return True, result
            except json.JSONDecodeError:
                print(f"✅ 调用成功: {response.text}")
                return True, response.text
        else:
            print(f"❌ 调用失败: {response.status_code} - {response.text}")
            return False, f"HTTP {response.status_code}: {response.text}"
            
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
        return False, "请求超时"
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接错误，请检查网络或服务器地址")
        return False, "连接错误"
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False, str(e)

def main():
    """主函数"""
    print("🚀 批量调用工作流接口")
    print("=" * 50)
    
    # 1. 加载JSON文件
    data = load_json_data()
    if data is None:
        return
    
    # 2. 提取procInstId
    proc_inst_ids = extract_proc_inst_ids(data)
    
    if not proc_inst_ids:
        print("❌ 未找到任何procInstId")
        return
    
    print(f"\n📊 找到 {len(proc_inst_ids)} 个procInstId")
    print(f"接口地址: http://*********:8001/yudaoWorkFlow/moveByTaskName")
    print(f"任务名称: 财务主管审核")
    
    # 显示所有procInstId
    print(f"\n📋 procInstId列表:")
    for i, proc_id in enumerate(proc_inst_ids, 1):
        print(f"  {i}. {proc_id}")
    
    # 确认是否继续
    confirm = input(f"\n❓ 确认要调用 {len(proc_inst_ids)} 个接口吗？(y/N): ")
    if confirm.lower() != 'y':
        print("❌ 用户取消操作")
        return
    
    # 3. 遍历调用接口
    success_count = 0
    failed_count = 0
    results = []
    
    print(f"\n🔄 开始批量调用...")
    print("-" * 50)
    
    for i, proc_inst_id in enumerate(proc_inst_ids, 1):
        print(f"\n[{i}/{len(proc_inst_ids)}] 处理: {proc_inst_id}")
        
        success, result = call_workflow_api(proc_inst_id)
        
        results.append({
            'procInstId': proc_inst_id,
            'success': success,
            'result': result,
            'timestamp': datetime.now().isoformat()
        })
        
        if success:
            success_count += 1
        else:
            failed_count += 1
        
        # 延迟1秒避免请求过快
        if i < len(proc_inst_ids):
            print(f"⏳ 等待1秒...")
            time.sleep(1)
    
    # 4. 汇总结果
    print(f"\n{'='*50}")
    print(f"📊 批量调用完成")
    print(f"{'='*50}")
    print(f"总数量: {len(proc_inst_ids)}")
    print(f"成功: {success_count}")
    print(f"失败: {failed_count}")
    print(f"成功率: {success_count/len(proc_inst_ids)*100:.1f}%")
    
    # 5. 显示失败的记录
    if failed_count > 0:
        print(f"\n❌ 失败的记录:")
        for result in results:
            if not result['success']:
                print(f"  {result['procInstId']}: {result['result']}")
    
    # 6. 保存结果到文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"workflow_result_{timestamp}.json"

    try:
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 详细结果已保存到: {result_file}")
    except Exception as e:
        print(f"\n❌ 保存结果失败: {e}")

if __name__ == "__main__":
    main()
