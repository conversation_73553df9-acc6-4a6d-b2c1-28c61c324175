('D:\\tools\\python\\tcl\\tcl8.6',
 'tcl',
 ['demos', '*.lib', 'tclConfig.sh'],
 'DATA',
 [('tcl\\auto.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\clock.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('tcl\\history.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('tcl\\init.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\package.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('tcl\\parray.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('tcl\\safe.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tclIndex', 'D:\\tools\\python\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('tcl\\tm.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\word.tcl', 'D:\\tools\\python\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\tzdata\\CET', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Cuba', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('tcl\\tzdata\\EET', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('tcl\\tzdata\\Egypt',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Eire', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('tcl\\tzdata\\EST', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\GB', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\GMT', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\GMT0', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\HST', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('tcl\\tzdata\\Iceland',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Iran', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('tcl\\tzdata\\Israel',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\MET', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('tcl\\tzdata\\MST', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\NZ', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\PRC', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\ROC', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('tcl\\tzdata\\ROK', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('tcl\\tzdata\\Singapore',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\UCT', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('tcl\\tzdata\\Universal',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\UTC', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('tcl\\tzdata\\W-SU', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('tcl\\tzdata\\WET', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('tcl\\tzdata\\Zulu', 'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kanton',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kyiv',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qostanay',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Nuuk',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'D:\\tools\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'D:\\tools\\python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'D:\\tools\\python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\af.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\msgs\\ar.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\msgs\\be.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('tcl\\msgs\\bg.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('tcl\\msgs\\bn.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\ca.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('tcl\\msgs\\cs.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('tcl\\msgs\\da.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('tcl\\msgs\\de.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\msgs\\el.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\msgs\\eo.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('tcl\\msgs\\es.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\msgs\\et.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('tcl\\msgs\\eu.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\msgs\\fa.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\msgs\\fi.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('tcl\\msgs\\fo.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\msgs\\fr.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\msgs\\ga.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\msgs\\gl.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\msgs\\gv.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\msgs\\he.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('tcl\\msgs\\hi.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\msgs\\hr.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('tcl\\msgs\\hu.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('tcl\\msgs\\id.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\msgs\\is.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('tcl\\msgs\\it.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\msgs\\ja.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('tcl\\msgs\\kl.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\msgs\\ko.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('tcl\\msgs\\kok.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\msgs\\kw.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\msgs\\lt.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('tcl\\msgs\\lv.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('tcl\\msgs\\mk.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('tcl\\msgs\\mr.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\msgs\\ms.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\msgs\\mt.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('tcl\\msgs\\nb.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('tcl\\msgs\\nl.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\msgs\\nn.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('tcl\\msgs\\pl.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('tcl\\msgs\\pt.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\msgs\\ro.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('tcl\\msgs\\ru.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\msgs\\sh.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('tcl\\msgs\\sk.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('tcl\\msgs\\sl.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('tcl\\msgs\\sq.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('tcl\\msgs\\sr.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('tcl\\msgs\\sv.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('tcl\\msgs\\sw.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('tcl\\msgs\\ta.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\msgs\\te.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\msgs\\th.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('tcl\\msgs\\tr.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('tcl\\msgs\\uk.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('tcl\\msgs\\vi.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('tcl\\msgs\\zh.msg', 'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'D:\\tools\\python\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'D:\\tools\\python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'D:\\tools\\python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\encoding\\cns11643.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-11.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'D:\\tools\\python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA')])
