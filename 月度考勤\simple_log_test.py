#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import os
from datetime import datetime
from pathlib import Path

def test_simple_logging():
    """简单的日志测试"""
    print("开始日志测试...")
    
    try:
        # 创建D盘logs目录
        log_dir = Path("D:/logs")
        log_dir.mkdir(parents=True, exist_ok=True)
        print(f"日志目录: {log_dir}")
        
        # 生成日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"test_sync_{timestamp}.log"
        print(f"日志文件: {log_file}")
        
        # 创建logger
        logger = logging.getLogger('test_logger')
        logger.setLevel(logging.DEBUG)
        
        # 清除已有的处理器
        logger.handlers.clear()
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器到logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        # 测试日志
        logger.info("=" * 60)
        logger.info("日志测试开始")
        logger.info("=" * 60)
        logger.info("这是一条INFO日志")
        logger.warning("这是一条WARNING日志")
        logger.error("这是一条ERROR日志")
        logger.debug("这是一条DEBUG日志")
        logger.info("测试中文字符：你好世界！🌍")
        logger.info("=" * 60)
        logger.info("日志测试结束")
        logger.info("=" * 60)
        
        # 关闭处理器
        for handler in logger.handlers:
            handler.close()
            logger.removeHandler(handler)
        
        # 检查文件是否存在
        if log_file.exists():
            file_size = log_file.stat().st_size
            print(f"✅ 日志文件创建成功，大小: {file_size} 字节")
            
            # 读取并显示内容
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print("\n📄 日志文件内容:")
                print("-" * 50)
                print(content)
        else:
            print("❌ 日志文件创建失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_logging()
    input("按回车键退出...")
