#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Hikiot API Authentication Module
Provides login functionality for the Hikiot API
"""

import requests
import json
import logging
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HikiotAuthError(Exception):
    """Custom exception for Hikiot authentication errors"""
    pass


class HikiotAPI:
    """Hikiot API client for authentication and token management"""
    
    def __init__(self):
        self.base_url = "https://api.hikiot.com/api-saas/open/v1"
        self.login_endpoint = f"{self.base_url}/pwdLogin"
        self.token = None
        self.other_token = None
        self.phone = None
        
        # Default headers
        self.headers = {
            "Authorization": "Basic bGluay13ZWI6bGluaw==",
            "Content-Type": "application/json",
            "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
            "Accept": "*/*",
            "Host": "api.hikiot.com",
            "Connection": "keep-alive"
        }
    
    def login(self, username: str = "19136753172", password: str = "34eNtEaiBjs/c/cDnSJccA==", 
              is_auto: bool = True) -> str:
        """
        Login to Hikiot API and retrieve authentication token
        
        Args:
            username (str): Username for login (default: "19136753172")
            password (str): Password for login (default: "34eNtEaiBjs/c/cDnSJccA==")
            is_auto (bool): Auto login flag (default: True)
            
        Returns:
            str: Authentication token
            
        Raises:
            HikiotAuthError: If login fails or response is invalid
            requests.RequestException: If HTTP request fails
        """
        
        # Prepare request payload
        payload = {
            "username": username,
            "password": password,
            "isAuto": is_auto
        }
        
        try:
            logger.info(f"Attempting login for user: {username}")
            
            # Make the HTTP POST request
            response = requests.post(
                self.login_endpoint,
                headers=self.headers,
                json=payload,
                timeout=30  # 30 second timeout
            )
            
            # Check HTTP status code
            response.raise_for_status()
            
            # Parse JSON response
            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                raise HikiotAuthError(f"Invalid JSON response from API: {e}")
            
            # Validate response structure
            if not isinstance(response_data, dict):
                raise HikiotAuthError("Response is not a valid JSON object")
            
            # Check API response code
            code = response_data.get("code")
            msg = response_data.get("msg", "Unknown error")
            
            if code != 0:
                logger.error(f"API returned error code {code}: {msg}")
                raise HikiotAuthError(f"Login failed: {msg} (code: {code})")
            
            # Extract data section
            data = response_data.get("data")
            if not data or not isinstance(data, dict):
                raise HikiotAuthError("Response missing 'data' section or invalid format")
            
            # Extract token
            token = data.get("token")
            if not token or not isinstance(token, str):
                raise HikiotAuthError("Response missing valid 'token' in data section")
            
            # Store additional information
            self.token = token
            self.other_token = data.get("otherToken")
            self.phone = data.get("phone")
            
            logger.info(f"Login successful for user: {username}")
            logger.info(f"Token received: {token[:8]}...{token[-8:] if len(token) > 16 else token}")
            
            return token
            
        except requests.exceptions.Timeout:
            logger.error("Request timeout while attempting to login")
            raise HikiotAuthError("Login request timed out")
            
        except requests.exceptions.ConnectionError:
            logger.error("Connection error while attempting to login")
            raise HikiotAuthError("Failed to connect to Hikiot API")
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error during login: {e}")
            raise HikiotAuthError(f"HTTP error: {e}")
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error during login: {e}")
            raise HikiotAuthError(f"Request failed: {e}")
    
    def get_token(self) -> Optional[str]:
        """
        Get the current stored token
        
        Returns:
            str or None: Current token if available
        """
        return self.token
    
    def get_other_token(self) -> Optional[str]:
        """
        Get the current stored other token
        
        Returns:
            str or None: Current other token if available
        """
        return self.other_token
    
    def get_phone(self) -> Optional[str]:
        """
        Get the phone number from login response
        
        Returns:
            str or None: Phone number if available
        """
        return self.phone
    
    def is_authenticated(self) -> bool:
        """
        Check if user is currently authenticated (has a token)
        
        Returns:
            bool: True if authenticated, False otherwise
        """
        return self.token is not None


# Convenience function for simple token retrieval
def get_hikiot_token(username: str = "19136753172", password: str = "34eNtEaiBjs/c/cDnSJccA==") -> str:
    """
    Simple function to get Hikiot authentication token
    
    Args:
        username (str): Username for login
        password (str): Password for login
        
    Returns:
        str: Authentication token
        
    Raises:
        HikiotAuthError: If login fails
    """
    api = HikiotAPI()
    return api.login(username, password)


# Example usage
if __name__ == "__main__":
    try:
        # Method 1: Using the class
        api = HikiotAPI()
        token = api.login()
        print(f"Login successful! Token: {token}")
        print(f"Phone: {api.get_phone()}")
        print(f"Other Token: {api.get_other_token()}")
        
        # Method 2: Using the convenience function
        # token = get_hikiot_token()
        # print(f"Token: {token}")
        
    except HikiotAuthError as e:
        print(f"Authentication error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
