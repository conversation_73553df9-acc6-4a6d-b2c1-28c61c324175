import sys
import io

# 设置标准输出和标准错误的编码为 UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
import requests
import os
import time
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import messagebox

## 获取appAccessToken
url = 'https://open-api.hikiot.com/auth/exchangeAppToken'
app_key = '1905605994948616256'
app_secret = 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKS7NdkPOkVtk+iHPKlpa+hXtFHem0DiMsV1zX9gMHHG5TWDCW8F8FTG7NRuJFwTvxmny14nt+HHW7SLcdBQz+UHlRSN/rOlpsiRQQ8bW11JrYkKmWX+VLQ0Ane2D7ZTWy8l8GmRoiNdBnt9bz/t6gMQnePkN+JvhItJREYOUr/NAgMBAAECgYEAntbpmFAHlxSO70Mfqhc99n5DIEIun8S8wgvSR8UfUUZAk3Wzrfsi/wwFJtzBcIuV1A4ombRgqXNKqO9gokaZ/iGtBQXnI0AkuDelUFrYmyV5Iy1P/mYWniaJNM7I+PkbOSHOt0F923h3NrmxOyBBD3ujdtPtKw2mYo9JbGrFkH0CQQD22uBb9sBRCdXu+LATQKQZWGotbDgIgL/MlXJ3IU0rLuznN1lbezP6EI/fYvzZ+GaCttoWZzNLGl8Lzk1KkeMvAkEAqtV9OHWf9D304XtBwPvXjzc9v2JWyF3cU+sgDlCtyCxxtAwBQhYL2io8OvA4nr7In4l9JAa4u11HAcSpQNe9wwJBAJaE5ZMNxTxkq+7X8rz6iFTwBWYG+6+rmcfMRIyBYMV5Cnj91d2jJRFQcfm7KQRbo14G0rogWTVtMhFHzPcwN38CQGdzhzQUjvuyNUQChywdlkkFE+B85b7KkC1FepEQrFxHBcgZaP2jKiRAZV7vr+n58LYj5WNWqrHT57cbZ797pk8CQHY3p2yb3fGQV5sqv5wCEyl5SUCbLnd2qwOIAogSZ8ygVSTl3k227jJyogPEk3EJ8EAtCAuy1kFgx8KEO8ZbmZo='

headers = {
    'Content-Type': 'application/json'
}

data = {
    'appKey': app_key,
    'appSecret': app_secret
}

def show_confirmation_dialog(title, message):
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    response = messagebox.askyesno(title, message)
    root.destroy()
    return response

def main():
    response = show_confirmation_dialog("确认同步", "修姐姐 您同步了数据吗?")
    if not response:
        print("用户选择终止程序")
        return

    response = show_confirmation_dialog("确认运行", "修姐姐 同步之后您需要5分钟运行 您确定吗？")
    if not response:
        print("用户选择终止程序")
        return

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        response_data = response.json()
        print('Response Data:', response_data)
        app_access_token = response_data.get('data', {}).get('appAccessToken')
        print('appAccessToken:', app_access_token)

        if app_access_token:
            url_apply_auth_code = 'https://open-api.hikiot.com/auth/third/applyAuthCode'
            headers_apply_auth_code = {
                'Content-Type': 'application/json'
            }
            data_apply_auth_code = {
                'appKey': app_key,
                'userName': '19136753172',
                'password': 'wwxkpo35781',  
                'redirectUrl': 'https://open.hikiot.com/util'
            }
            response_apply_auth_code = requests.post(url_apply_auth_code, headers=headers_apply_auth_code, json=data_apply_auth_code)

            if response_apply_auth_code.status_code == 200:
                response_data_apply_auth_code = response_apply_auth_code.json()
                auth_code = response_data_apply_auth_code.get('data', {}).get('authCode')
                print('auth_code:', auth_code)
                if auth_code:
                    url_code2_token = 'https://open-api.hikiot.com/auth/third/code2Token'
                    headers_code2_token = {
                        'Content-Type': 'application/json',
                        'App-Access-Token': app_access_token
                    }
                    params_code2_token = {
                        'authCode': auth_code
                    }

                    response_code2_token = requests.get(url_code2_token, headers=headers_code2_token, params=params_code2_token)

                    if response_code2_token.status_code == 200:
                        response_data_code2_token = response_code2_token.json()
                        user_access_token = response_data_code2_token.get('data', {}).get('userAccessToken')
                        print('userAccessToken:', user_access_token)
                        if user_access_token:
                            url_attendance_export = 'https://open-api.hikiot.com/attendance/export/v1/origin/record'
                            headers_attendance_export = {
                                'Content-Type': 'application/json',
                                'App-Access-Token': app_access_token,
                                'User-Access-Token': user_access_token
                            }
                            today = datetime.today().strftime('%Y-%m-%d')
                            yesterday = (datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')
                            date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
                            data_attendance_export = {
                                'beginDate': yesterday,
                                'endDate': yesterday
                            }

                            response_attendance_export = requests.post(url_attendance_export, headers=headers_attendance_export, json=data_attendance_export)
                            if response_attendance_export.status_code == 200:
                                response_data_attendance_export = response_attendance_export.json()
                                print('Attendance Export Response Data:', response_data_attendance_export)
                                id = response_data_attendance_export.get('data')
                                print('id:', id)
                                if id:
                                    url_file_center_detail = 'https://open-api.hikiot.com/fileCenter/v1/detail'
                                    headers_file_center_detail = {
                                        'Content-Type': 'application/json',
                                        'App-Access-Token': app_access_token
                                    }

                                    params_file_center_detail = {
                                        'id': id
                                    }

                                    response_file_center_detail = requests.get(url_file_center_detail, headers=headers_file_center_detail, params=params_file_center_detail)
                                    if response_file_center_detail.status_code == 200:
                                        response_data_file_center_detail = response_file_center_detail.json()
                                        while True:
                                            statusDesc = response_data_file_center_detail.get('data', {}).get('statusDesc')
                                            print('Current status:', statusDesc)
                                            
                                            if statusDesc == "导出中":
                                                print("文件正在导出中，等待5秒后重试...")
                                                time.sleep(5) 
                                                response_file_center_detail = requests.get(
                                                    url_file_center_detail, 
                                                    headers=headers_file_center_detail, 
                                                    params=params_file_center_detail
                                                )
                                                if response_file_center_detail.status_code == 200:
                                                    response_data_file_center_detail = response_file_center_detail.json()
                                                else:
                                                    print('File Center Detail Error:', response_file_center_detail.status_code)
                                                    break
                                            else:
                                                fileUrl = response_data_file_center_detail.get('data', {}).get('fileUrl')
                                                print('File Center Detail Response Data:', fileUrl)
                                                if fileUrl:
                                                    try:
                                                        origin_folder = r'D:\attendance\origin'
                                                        if not os.path.exists(origin_folder):
                                                            os.makedirs(origin_folder)
                                                        file_name = os.path.join(origin_folder, f"{date}.xlsx")
                                                        file_response = requests.get(fileUrl)
                                                        if file_response.status_code == 200:
                                                            with open(file_name, 'wb') as f:
                                                                f.write(file_response.content)
                                                            print(f'File successfully downloaded as {file_name}')
                                                        else:
                                                            print('Failed to download file:', file_response.status_code)
                                                    except Exception as e:
                                                        print('Error downloading file:', str(e))
                                                else:
                                                    print('No file URL found')
                                                break  
                            else:
                                print('Attendance Export Error:', response_attendance_export.status_code, response_attendance_export.text)

                    else:
                        print('code2Token Error:', response_code2_token.status_code, response_code2_token.text)
                else:
                    print('Failed to get auth_code')
                
            else:
                print('applyAuthCode Error:', response_apply_auth_code.status_code, response_apply_auth_code.text)
        else:
            print('Failed to get appAccessToken')
    else:
        print('Error:', response.status_code, response.text)

if __name__ == '__main__':
    main()