#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取value.xlsx文件中的数据库参数配置
分析Data0047和Data0278表的数据
"""

import pandas as pd
import json

def read_value_excel():
    """读取value.xlsx文件"""
    print("🔍 读取value.xlsx文件中的数据库参数")
    print("=" * 60)
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile('value.xlsx')
        
        print(f"📊 Excel文件包含的工作表:")
        for sheet_name in excel_file.sheet_names:
            print(f"   - {sheet_name}")
        
        # 尝试读取每个工作表
        for sheet_name in excel_file.sheet_names:
            print(f"\n📋 工作表: {sheet_name}")
            print("-" * 40)
            
            try:
                df = pd.read_excel('value.xlsx', sheet_name=sheet_name)
                print(f"   行数: {len(df)}")
                print(f"   列数: {len(df.columns)}")
                print(f"   列名: {list(df.columns)}")
                
                # 显示前几行数据
                if len(df) > 0:
                    print(f"\n   前5行数据:")
                    print(df.head().to_string(index=False))
                
                # 如果包含SPEC_RKEY列，特别关注
                if 'SPEC_RKEY' in df.columns:
                    print(f"\n   🎯 发现SPEC_RKEY列!")
                    spec_values = df['SPEC_RKEY'].unique()
                    print(f"   SPEC_RKEY的值: {spec_values}")
                    
                    # 查找B和C的配置
                    if 'PARAMETER_VALUE' in df.columns:
                        b_rows = df[df['SPEC_RKEY'] == 'B']
                        c_rows = df[df['SPEC_RKEY'] == 'C']
                        
                        if len(b_rows) > 0:
                            print(f"\n   📊 SPEC_RKEY='B'的配置:")
                            print(b_rows.to_string(index=False))
                        
                        if len(c_rows) > 0:
                            print(f"\n   📊 SPEC_RKEY='C'的配置:")
                            print(c_rows.to_string(index=False))
                
                # 如果包含工具相关的列，特别关注
                tool_related_columns = [col for col in df.columns if any(keyword in col.upper() for keyword in ['TOOL', 'DRILL', 'T35', 'T36', 'MULTIPLIER', 'FACTOR'])]
                if tool_related_columns:
                    print(f"\n   🔧 发现工具相关列: {tool_related_columns}")
                
            except Exception as e:
                print(f"   ❌ 读取工作表失败: {e}")
        
        # 尝试查找特定的数据模式
        print(f"\n🔍 查找参数配置模式:")
        
        # 查找所有工作表中可能包含倍数配置的数据
        for sheet_name in excel_file.sheet_names:
            try:
                df = pd.read_excel('value.xlsx', sheet_name=sheet_name)
                
                # 查找可能的倍数配置
                for col in df.columns:
                    if df[col].dtype in ['float64', 'int64']:
                        # 检查是否有类似我们发现的倍数值
                        unique_values = df[col].unique()
                        target_multipliers = [8.297, 10.474, 8.200, 5.747, 6.684, 4.789, 4.000, 5.000]
                        
                        for target in target_multipliers:
                            if any(abs(val - target) < 0.001 for val in unique_values if pd.notna(val)):
                                print(f"   🎯 在工作表'{sheet_name}'的列'{col}'中发现倍数{target}")
                                matching_rows = df[abs(df[col] - target) < 0.001]
                                print(f"      匹配行:")
                                print(matching_rows.to_string(index=False))
                                break
                
            except Exception as e:
                continue
                
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        print(f"请确保文件存在且格式正确")

if __name__ == "__main__":
    read_value_excel()
