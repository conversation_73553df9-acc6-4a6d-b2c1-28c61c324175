#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Hikiot API Token获取方法
简洁版本，只返回token
"""

import requests


def get_hikiot_token(username="19136753172", password="34eNtEaiBjs/c/cDnSJccA=="):
    """
    获取Hikiot API的token
    
    Args:
        username (str): 用户名，默认为 "19136753172"
        password (str): 密码，默认为 "34eNtEaiBjs/c/cDnSJccA=="
    
    Returns:
        str: 返回token字符串，失败时返回None
    """
    
    url = "https://api.hikiot.com/api-saas/open/v1/pwdLogin"
    
    headers = {
        'Authorization': 'Basic bGluay13ZWI6bGluaw==',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.hikiot.com',
        'Connection': 'keep-alive'
    }
    
    data = {
        "username": username,
        "password": password,
        "isAuto": True
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get("code") == 0:
            return result.get("data", {}).get("token")
        else:
            print(f"登录失败: {result.get('msg', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"请求失败: {e}")
        return None


# 测试
if __name__ == "__main__":
    token = get_hikiot_token()
    if token:
        print(f"Token: {token}")
    else:
        print("获取token失败")
