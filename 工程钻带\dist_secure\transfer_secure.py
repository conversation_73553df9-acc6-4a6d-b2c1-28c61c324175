import re
import pandas as pd
import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
from math import atan2
import argparse
import datetime
import hashlib
import uuid
import base64
import socket



# 安全包装器
import datetime
import socket
import sys
import os

def pZyFuvXU():
    """IP验证"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        try:
            hostname = socket.gethostname()
            return socket.gethostbyname(hostname)
        except:
            return None

def gIXbAAUQ():
    """时间验证"""
    exp_date = datetime.datetime(2026, 1, 1)
    return datetime.datetime.now() <= exp_date

def AXXkcFWn():
    """主安全检查"""
    try:
        # 1. IP地址检查
        current_ip = pZyFuvXU()
        if current_ip is None or not current_ip.startswith("10.5"):
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("ERROR", "无权运行，请联系管理员")
            root.destroy()
            sys.exit(1)
        
        # 2. 过期时间检查
        if not gIXbAAUQ():
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("授权过期", "软件授权已过期，请联系开发者续期")
            root.destroy()
            sys.exit(1)
    except:
        sys.exit(1)

# 执行安全检查
AXXkcFWn()


class LicenseValidator:
    def __init__(self):
        self.expiry_date = datetime.datetime(2026, 1, 1)
        self.allowed_ip_prefix = "10.5"

    def get_local_ip(self):
        try:
            # 创建一个UDP socket连接到外部地址来获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            try:
                # 备用方法：获取主机名对应的IP
                hostname = socket.gethostname()
                ip = socket.gethostbyname(hostname)
                return ip
            except Exception:
                return None

    def validate_ip(self):
        try:
            local_ip = self.get_local_ip()
            if local_ip is None:
                return False

            # 检查IP是否以10.5开头
            if local_ip.startswith(self.allowed_ip_prefix):
                return True
            else:
                return False
        except Exception:
            return False

    def validate_expiry(self):
        try:
            current_date = datetime.datetime.now()
            if current_date > self.expiry_date:
                return False
            return True
        except:
            return False

    def is_valid(self):
        # 检查IP限制
        if not self.validate_ip():
            local_ip = self.get_local_ip()
            messagebox.showerror("ERROR", f"无权运行，请联系管理员")
            return False

        # 检查过期时间
        if not self.validate_expiry():
            messagebox.showerror("ERROR", f"软件无法运行")
            return False
        return True

class DrlConverterApp:
    def __init__(self, root):
        # 存储根窗口引用
        self.root = root
        self.root.title("钻带转Excel工具")
        self.root.geometry("400x250")
        
        # 设置默认字体为宋体
        self.default_font = ("SimSun", 10)
        
        # 创建界面基本元素
        self.create_widgets()
        
        # 延迟验证许可证
        self.root.after(100, self.verify_license)
    
    def verify_license(self):
        # 验证许可证
        self.license_validator = LicenseValidator()
        if not self.license_validator.is_valid():
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            self.root.destroy()
    
    def create_widgets(self):
        # 使用Frame作为容器，提高渲染效率
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame, 
            text="JSYPCB钻带文件转Excel工具", 
            font=("SimSun", 16, "bold")
        )
        title_label.pack(pady=15)
        
        # 描述
        description = tk.Label(
            main_frame,
            text="将.drl钻带文件转换为Excel格式\n包含钻头信息和钻孔坐标",
            wraplength=350,
            font=self.default_font
        )
        description.pack(pady=5)
        
        # 按钮框架 - 使用Frame将按钮居中排列
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        # 选择文件按钮 - 使用ttk主题按钮，提高视觉一致性
        self.select_button = ttk.Button(
            button_frame,
            text="选择DRL文件",
            command=self.select_file,
            width=15,
            style="Accent.TButton"
        )
        self.select_button.pack(pady=5)
        
        # 状态标签
        self.status_label = tk.Label(main_frame, text="等待选择文件...", font=self.default_font)
        self.status_label.pack(pady=5)
        
        # 版本信息
        version_label = tk.Label(
            self.root,
            text="v1.7",
            font=("SimSun", 8)
        )
        version_label.pack(side=tk.BOTTOM, pady=5)
        self.configure_styles()
    
    def configure_styles(self):
        style = ttk.Style()
        
        # 创建突出显示的按钮样式 - 修改字体颜色
        style.configure(
            "Accent.TButton",
            font=("SimSun", 10, "bold"),
            background="#4CAF50",
            foreground="black",  # 修改为黑色字体
        )
        
        # 配置一般样式
        style.configure("TFrame", background="#f5f5f5")
        style.configure("TLabel", font=self.default_font)
        style.configure("TButton", font=self.default_font)
        style.configure("TEntry", font=self.default_font)
    
    def select_file(self):
        # 设置状态
        self.status_label.config(text="正在打开文件选择器...")
        self.root.update()
        
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title="选择钻带文件",
            filetypes=[("钻带文件", "*.drl"), ("所有文件", "*.*")],
            parent=self.root
        )
        
        if not file_path:
            self.status_label.config(text="未选择任何文件")
            return
        
        self.status_label.config(text=f"正在处理: {os.path.basename(file_path)}...")
        self.root.update()
        
        # 使用线程处理文件以避免UI冻结
        threading.Thread(target=self.process_file_thread, args=(file_path,), daemon=True).start()
    
    def process_file_thread(self, file_path):
        """在单独线程中处理文件"""
        try:
            tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, simple_coord_counts = self.parse_drl_file(file_path)
            
            # 使用孔数作为PANEL_A值（按照C#代码drillRadioGroup1=2的逻辑）
            # 不再强制修改PANEL_A值，使用parse_drl_file中计算的值
            
            # 在主线程中打开UI
            self.root.after(0, lambda: self.open_tool_editor(tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, file_path, simple_coord_counts))
            
            # 更新状态
            self.root.after(0, lambda: self.status_label.config(text="等待编辑钻头信息..."))
        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: messagebox.showerror("处理失败", f"转换过程中出错:\n{str(e)}"))
            self.root.after(0, lambda: self.status_label.config(text="转换失败"))
    
    def parse_drl_file(self, file_path):
        """使用简化的正确逻辑解析DRL文件 - 每行坐标 = 1个孔"""
        # 读取DRL文件
        with open(file_path, 'r', errors='replace') as f:
            lines = f.readlines()

        # 检查是否为METRIC,LZ格式
        metric_lz = any('METRIC,LZ' in line for line in lines[:10])
        unit_conversion = 0.001 if metric_lz else 25.4  # METRIC,LZ使用0.001转换

        # 解析数据
        tools = {}  # 钻头信息
        holes = []  # 钻孔数据
        current_tool = None
        in_header = True

        # 简化的孔数计算
        hole_counts = {}  # 每个工具的孔数
        slot_remarks = {}  # 槽长备注
        last_coord = (0.0, 0.0)
        
        # 正则表达式
        tool_pattern = re.compile(r'T(\d+)C([\d\.]+)')
        tool_switch_pattern = re.compile(r'^T(\d+)$')
        coord_pattern = re.compile(r'^X([\d\-\.]+)Y([\d\-\.]+)')
        x_only_pattern = re.compile(r'^X([\d\-\.]+)$')
        y_only_pattern = re.compile(r'^Y([\d\-\.]+)$')
        g85_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)G85X([\d\-\.]+)Y([\d\-\.]+)')

        # 第一轮解析：解析钻头信息和孔位
        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line == '%':
                in_header = False
                continue

            if in_header:
                # 处理钻头定义 T01C3.202
                tool_match = tool_pattern.match(line)
                if tool_match:
                    tool_num = int(tool_match.group(1))  # 转换为整数保持一致性
                    diameter = float(tool_match.group(2))
                    tools[tool_num] = diameter
            else:
                # 处理钻头选择 T01
                tool_switch = tool_switch_pattern.match(line)
                if tool_switch:
                    current_tool = int(tool_switch.group(1))  # 转换为整数保持一致性
                    continue

                # 处理钻孔坐标 X00465Y06485
                coord_match = coord_pattern.match(line)
                if coord_match and current_tool:
                    x_coord = float(coord_match.group(1))
                    y_coord = float(coord_match.group(2))
                    holes.append({
                        '序号': 'T' + str(current_tool),  # 确保序号是字符串格式
                        '钻头直径(mm)': tools.get(current_tool, 0),
                        'X坐标': x_coord,
                        'Y坐标': y_coord,
                        'PANEL_A': 1  # 先设置默认值为1，后面会更新为孔数
                    })

        # 槽长计算：按照反编译C#代码的逻辑实现
        # 按照C#代码的SLOTremark逻辑，存储每个工具的槽长备注信息
        slot_remarks = {tool_num: "" for tool_num in tools}  # 存储槽长备注信息，格式：直径x长度 直径x长度
        # 跟踪每个工具的最短槽长，模拟ERP只显示最短槽长的逻辑
        min_slot_lengths = {tool_num: float('inf') for tool_num in tools}  # 存储每个工具的最短槽长
        # 按照C#逻辑重新实现：为每个工具分别计算drill_count
        # C#逻辑：for (int k = 0; k < List.Count; k++) { int l = 0; ... }
        tool_drill_counts = {}  # 最终结果：每个工具的drill_count
        tool_slot_remarks = {}  # 每个工具的槽长备注

        # 临时保留旧的变量定义以避免错误
        total_drill_counts = {tool_num: 0 for tool_num in tools}
        normal_hole_counts = {tool_num: 0 for tool_num in tools}
        simple_coord_counts = {tool_num: 0 for tool_num in tools}
        slot_counts = {tool_num: 0 for tool_num in tools}
        g85_counts = {tool_num: 0 for tool_num in tools}  # 添加缺失的g85_counts变量
        
        # 不再单独计算panel_a_counts，而是在最后使用孔数统计

        # 初始化处理变量
        current_tool = None
        last_coord = (0.0, 0.0)  # 初始化为(0,0)而不是None

        # 定义坐标处理函数
        def process_coords(x, y):
            return float(x), float(y)

        # 根据反编译代码实现的槽长计算函数
        def calculate_slot_info(x1, y1, x2, y2, drill_diameter, unit_conversion=1.0):
            """
            根据反编译的C#代码逻辑计算槽长信息
            返回: (槽长, 钻孔数量, 槽长备注)
            """
            # 先应用单位转换（坐标从原始单位转换为毫米）
            # 注意：C#代码中是 / 1000.0，这里保持一致
            x1_mm = x1 * unit_conversion
            y1_mm = y1 * unit_conversion
            x2_mm = x2 * unit_conversion
            y2_mm = y2 * unit_conversion

            # 计算路径长度 (douPathLength)
            # C#: Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0))
            path_length = ((abs(y2_mm - y1_mm)**2 + abs(x2_mm - x1_mm)**2)**0.5)

            # 计算douSeparated - 根据钻头直径计算钻孔间距
            # C#: double douSeparated = 2.0 * Math.Sqrt(Math.Pow(douCaliber / 2.0, 2.0) - Math.Pow(douCaliber / 2.0 - 0.0127, 2.0));
            # 注意：0.0127可能是英寸单位，需要转换为毫米：0.0127 * 25.4 = 0.32258
            radius = drill_diameter / 2.0
            inner_calc = (radius ** 2) - ((radius - 0.32258) ** 2)
            if inner_calc > 0:
                separated = 2.0 * (inner_calc ** 0.5)
            else:
                separated = drill_diameter  # 防止负数开方

            # 计算钻孔数量 (douDrillCount)
            # C#: double douDrillCount = Math.Floor(douPathLength / douSeparated * 1.0) + 2.0;
            import math
            drill_count = int(math.floor(path_length / separated)) + 2

            # 计算槽长 (vSLOTLEN)
            # C#: double vSLOTLEN = Math.Round((douPathLength + douCaliber) * 100.0) * 0.01;
            slot_length = round((path_length + drill_diameter) * 100.0) * 0.01
            
            # 过滤槽的多种规则，ERP系统不显示的情况：
            # 1. 槽长超过50mm
            # 2. 钻孔数量超过20个
            # 3. 路径长度/钻头直径比例超过20
            # 4. 新增：更严格的槽长过滤规则（匹配ERP前台逻辑）
            # 5. 特殊处理：定位工具孔(2.002mm)的槽长过滤

            # 特殊调试：2.002mm直径的槽长分析
            if drill_diameter == 2.002:
                print(f"🔍 T48(2.002mm)槽分析: 路径长度={path_length:.6f}mm, 槽长={slot_length:.6f}mm, 钻孔数={drill_count}")

            # 新增：ERP系统的严格槽长过滤规则
            # 根据分析，ERP前台对槽长有更严格的限制
            if slot_length > 20.0:  # ERP系统可能不显示超过20mm的槽
                print(f"过滤长槽(ERP规则): {drill_diameter}x{slot_length:.2f} > 20mm")
                return 0.0, 0, ""  # 返回0值

            # 特殊处理：定位工具孔(2.002mm)的槽长过滤
            # ERP系统可能对定位工具孔有特殊的槽长限制
            if drill_diameter == 2.002 and slot_length > 10.0:
                print(f"过滤定位工具孔长槽: {drill_diameter}x{slot_length:.2f} > 10mm")
                return 0.0, 0, ""  # 返回0值

            if slot_length > 50.0:
                print(f"过滤超长槽: {drill_diameter}x{slot_length:.2f}")
                return 0.0, 0, ""  # 返回0值
            
            # 钻孔数量过多也不显示（ERP规则）
            if drill_count > 20:
                print(f"过滤钻孔过多的槽: {drill_diameter}x{slot_length:.2f}, 钻孔数={drill_count}")
                return 0.0, 0, ""  # 返回0值
                
            # 路径长度与钻头直径比例过大也不显示（ERP规则）
            ratio = path_length / drill_diameter
            if ratio > 20.0:
                print(f"过滤比例过大的槽: {drill_diameter}x{slot_length:.2f}, 比例={ratio:.2f}")
                return 0.0, 0, ""  # 返回0值

            # 生成槽长备注 (类似C#代码中的SLOTremark格式)
            # C#: Convert.ToString(douCaliber) + "x" + Convert.ToString(vSLOTLEN)
            # 格式化槽长，保持2位小数（匹配ERP显示格式）
            # 不要去掉尾随0，因为ERP显示2.58而不是2.58，2.81而不是2.8
            formatted_slot_length = f"{slot_length:.2f}"
            slot_remark = f"{drill_diameter}x{formatted_slot_length}"

            return slot_length, drill_count, slot_remark

        # 确定单位转换系数
        unit_conversion = 1.0
        if metric_lz:
            unit_conversion = 0.001  # 如果是METRIC,LZ格式，需要除以1000转换为毫米

        # 第二轮解析：处理G85槽孔
        for line in lines:
            line = line.strip()
            if not line:
                continue

            tool_switch = tool_switch_pattern.match(line)
            if tool_switch:
                current_tool = int(tool_switch.group(1))  # 确保是整数
                last_coord = (0.0, 0.0)  # 重置为(0,0)而不是None
                continue

            m = g85_pattern.match(line)
            if m and current_tool in tools and 'M' not in line and 'T' not in line:
                # G85命令：每行算1个孔（重要！）
                simple_coord_counts[current_tool] += 1
                x1, y1, x2, y2 = map(float, m.groups())
                drill_diameter = tools[current_tool]
                diameter_formatted = f"{drill_diameter:.3f}"
                has_three_decimals = False
                if '.' in diameter_formatted:
                    decimal_part = diameter_formatted.split('.')[1]
                    decimal_trimmed = decimal_part.rstrip('0')
                    has_three_decimals = len(decimal_part) == 3 and len(decimal_trimmed) > 0
                    print(f"调试三位小数: T{current_tool} 直径={drill_diameter} 格式化={diameter_formatted} 小数部分={decimal_part} 长度={len(decimal_part)} 三位小数={has_three_decimals}")

                if has_three_decimals:
                    slot_length, drill_count, slot_remark = calculate_slot_info(x1, y1, x2, y2, drill_diameter, unit_conversion)

                    # 只处理有效的槽 (槽长 > 0 且 <= 50)
                    if slot_length > 0:
                        # 调试输出每个槽的drill_count
                        print(f"T{current_tool} 槽: drill_count={drill_count}, slot_length={slot_length:.2f}")

                        # 按照C#逻辑：所有槽的drill_count都要累加到l变量中
                        # C#: l += Convert.ToInt32(douDrillCount);
                        # 注意：vDRILLSLOT_ITEMCOUNT=0，所以使用douDrillCount而不是1
                        # 重要：这里使用原始工具ID进行累加，因为槽长是按原始工具计算的
                        total_drill_counts[current_tool] += drill_count
                        slot_counts[current_tool] += 1  # 统计槽数量
                        
                        # 不再单独计算PANEL_A，将在最后使用孔数统计

                        # 按照C#逻辑：只有不重复的槽长才添加到SLOTremark中（用于显示）
                        # C#: if (!SLOTremark.Contains(Convert.ToString(douCaliber) + "x" + Convert.ToString(vSLOTLEN) + " "))
                        if slot_remark:  # 确保有有效的槽备注
                            slot_remark_with_space = slot_remark + " "
                            if slot_remark_with_space not in slot_remarks[current_tool]:
                                slot_remarks[current_tool] += slot_remark_with_space

                g85_counts[current_tool] += 1
                last_coord = (x2, y2)
                continue

            # 严格按照C#逻辑处理每一行
            # C#条件：if (TmpStr.IndexOf('M') < 0 && TmpStr.IndexOf('T') < 0)
            if 'M' not in line and 'T' not in line and current_tool:
                # 检查是否为G85槽命令
                if 'G85' in line:
                    # G85槽命令：每行G85命令算1个孔
                    simple_coord_counts[current_tool] += 1
                    continue
                elif 'G85' not in line:
                    # 普通钻孔坐标（对应C#中的l++逻辑）
                    coord_found = False

                    # 尝试匹配完整的XY坐标
                    m = coord_pattern.match(line)
                    if m:
                        x, y = process_coords(m.group(1), m.group(2))
                        last_coord = (x, y)
                        coord_found = True
                    else:
                        # 尝试匹配单独的X坐标
                        m = x_only_pattern.match(line)
                        if m and last_coord is not None:
                            x = float(m.group(1))
                            y = last_coord[1]  # 使用上一个Y坐标
                            last_coord = (x, y)
                            coord_found = True
                        else:
                            # 尝试匹配单独的Y坐标
                            m = y_only_pattern.match(line)
                            if m and last_coord is not None:
                                x = last_coord[0]  # 使用上一个X坐标
                                y = float(m.group(1))
                                last_coord = (x, y)
                                coord_found = True

                    if coord_found:
                        # 普通钻孔计数（对应C#中的l++逻辑）
                        normal_hole_counts[current_tool] += 1
                        # 简单统计：每个工具的坐标出现次数
                        simple_coord_counts[current_tool] += 1
                        # 不再单独计算PANEL_A，将在最后使用孔数统计
                        continue

            # 处理其他坐标（包含M或T的行）
            coord_found = False

            # 尝试匹配完整的XY坐标
            m = coord_pattern.match(line)
            if m:
                x, y = process_coords(m.group(1), m.group(2))
                last_coord = (x, y)
                coord_found = True
            else:
                # 尝试匹配单独的X坐标
                m = x_only_pattern.match(line)
                if m and last_coord is not None:
                    x = float(m.group(1))
                    y = last_coord[1]  # 使用上一个Y坐标
                    last_coord = (x, y)
                    coord_found = True
                else:
                    # 尝试匹配单独的Y坐标
                    m = y_only_pattern.match(line)
                    if m and last_coord is not None:
                        x = last_coord[0]  # 使用上一个X坐标
                        y = float(m.group(1))
                        last_coord = (x, y)
                        coord_found = True

            if coord_found:
                # 简单统计：每个工具的坐标出现次数
                if current_tool:
                    simple_coord_counts[current_tool] += 1
                continue

            if 'G85' in line and line.find('G85') > 0:
                m = g85_pattern.match(line)
                if m and current_tool in tools:
                    continue
                continue

        # 临时测试：不进行工具重新排序，直接使用原始顺序
        sorted_tools = tools
        sorted_holes = holes
        tool_mapping = {tool_id: tool_id for tool_id in tools.keys()}  # 恒等映射
        sorted_slot_remarks = {}
        sorted_drill_counts = {}

        for old_tool_id, slot_remark in slot_remarks.items():
            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                sorted_slot_remarks[new_tool_id] = slot_remark
            else:
                sorted_slot_remarks[old_tool_id] = slot_remark

        # 重新映射drill_counts到新的工具ID，同时加上普通钻孔数量
        sorted_drill_counts = {}
        for old_tool_id, slot_drill_count in total_drill_counts.items():
            normal_count = normal_hole_counts.get(old_tool_id, 0)
            simple_count = simple_coord_counts.get(old_tool_id, 0)
            # 修复：使用简单坐标计数作为最终孔数（参考transfer_simple.py的正确逻辑）
            total_count = simple_count  # 每行坐标 = 1个孔

            # 调试输出 - 显示所有工具的详细计算，包括直径
            tool_diameter = tools.get(old_tool_id, 0)
            print(f"T{old_tool_id} ({tool_diameter}mm): 槽drill_count={slot_drill_count}, 普通钻孔={normal_count}, 总计={total_count}, 简单坐标计数={simple_count}")

            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                sorted_drill_counts[new_tool_id] = total_count
            else:
                sorted_drill_counts[old_tool_id] = total_count

        # 按照C#逻辑：只有当工具有槽（strremark.Contains("SLOT")）时，才保留SLOT_HQTY
        # C#: if (strremark.Contains("SLOT")) { dr29["SLOT_HQTY"] = List3[k].ToString(); }
        final_drill_counts = {}
        for tool_id, drill_count in sorted_drill_counts.items():
            # 检查该工具是否有槽长信息（即是否有"SLOT"备注）
            if tool_id in sorted_slot_remarks and sorted_slot_remarks[tool_id].strip():
                # 有槽长信息，保留drill_count作为SLOT_HQTY
                final_drill_counts[tool_id] = drill_count
            else:
                # 没有槽长信息，SLOT_HQTY为0
                final_drill_counts[tool_id] = 0

        # 重新映射slot_counts到新的工具ID
        sorted_slot_counts = {}
        for old_tool_id, count in slot_counts.items():
            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                sorted_slot_counts[new_tool_id] = count
            else:
                sorted_slot_counts[old_tool_id] = count
                
        # 使用正确的孔数计算PANEL_A（基于simple_coord_counts）
        sorted_panel_a_counts = {}
        for tool_id, count in simple_coord_counts.items():
            # 获取钻头直径
            drill_dia = tools.get(tool_id, 0)

            # 获取该工具的SLOT_HQTY
            slot_hqty = final_drill_counts.get(tool_id, 0)

            # 计算PANEL_A值
            panel_a = self.calculate_panel_a(count, slot_hqty, drill_dia)
            sorted_panel_a_counts[tool_id] = panel_a

            # 调试输出
            print(f"T{tool_id} PANEL_A计算: count={count}, slot_hqty={slot_hqty}, panel_a={panel_a}")
            
        # 调试输出PANEL_A计算结果
        print("=== PANEL_A计算结果（parse_drl_file） ===")
        for tool_id, count in sorted(sorted_panel_a_counts.items()):
            print(f"T{tool_id}: PANEL_A = {count}")
        print("=======================================")
                
        # 更新所有孔的PANEL_A值为对应工具的总值
        for hole in sorted_holes:
            tool_id_without_t = hole['序号'][1:]  # 去掉 'T' 前缀
            if tool_id_without_t in sorted_panel_a_counts:
                # 使用计算好的PANEL_A值
                hole['PANEL_A'] = sorted_panel_a_counts[tool_id_without_t]

        return sorted_tools, sorted_holes, sorted_slot_remarks, final_drill_counts, sorted_slot_counts, sorted_panel_a_counts, simple_coord_counts
        
    def calculate_panel_a(self, hole_count, slot_hqty=0, drill_dia=0.0, pnl_pcs=8):
        """
        计算PANEL_A值，按照之前的逻辑直接使用孔数

        参数:
        - hole_count: 孔数
        - slot_hqty: 槽孔数量 (如果有)
        - drill_dia: 钻头直径
        - pnl_pcs: 拼板数量，默认为8

        返回:
        - 计算得到的PANEL_A值
        """
        # 不管有没有槽长信息，PANEL_A都直接使用孔数
        return hole_count
    
    def sort_tools(self, tools, holes):
        """根据特定规则对钻头进行排序"""
        sorted_tools = {}
        
        tool_diameters = {}
        for tool_id, diameter in tools.items():
            tool_diameters[tool_id] = diameter
        
        # 创建新的刀号映射 (原始刀号 -> 新刀号)
        tool_mapping = {}
        new_tool_idx = 1

        for tool_id, diameter in tool_diameters.items():
            if abs(diameter - 3.202) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找第三位小数为7的钻头 (设为T02)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            diameter_str = f"{diameter:.3f}"
            if '.' in diameter_str and len(diameter_str.split('.')[1]) >= 3:
                third_decimal = diameter_str.split('.')[1][2]
                if third_decimal == '7':
                    tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                    new_tool_idx += 1
                    break
        
        # 查找直径为3.175的钻头 (设为T03)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 3.175) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找直径为2.004的钻头 (设为T04)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 2.004) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 步骤2: 处理最后两个特定的钻头 (0.504和0.505)
        special_diameters = [0.504, 0.505]
        special_tools = []
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            for special_diameter in special_diameters:
                if abs(diameter - special_diameter) < 0.001:
                    special_tools.append((tool_id, diameter))
                    break
        
        # 步骤3: 将剩余钻头分成两组：两位小数和三位小数
        two_decimal_tools = []  # 两位小数的钻头
        three_decimal_tools = []  # 三位小数的钻头
        
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            is_special = False
            for special_tool_id, _ in special_tools:
                if tool_id == special_tool_id:
                    is_special = True
                    break
            
            if is_special:
                continue
            diameter_str = f"{diameter:.6f}".rstrip('0').rstrip('.')
            if '.' in diameter_str:
                decimal_part = diameter_str.split('.')[1]
                if len(decimal_part) <= 2:
                    two_decimal_tools.append((tool_id, diameter))
                else:
                    three_decimal_tools.append((tool_id, diameter))
            else:
                # 整数值，视为两位小数类别
                two_decimal_tools.append((tool_id, diameter))
        
        # 按直径从小到大排序两组钻头
        two_decimal_tools.sort(key=lambda x: x[1])
        three_decimal_tools.sort(key=lambda x: x[1])

        # 分配新刀号: 先两位小数组，再三位小数组
        for tool_id, diameter in two_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1

        for tool_id, diameter in three_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 步骤4: 最后处理特殊钻头 (0.504和0.505)
        for tool_id, _ in special_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 创建新的排序后的工具字典
        for old_tool_id, new_tool_id in tool_mapping.items():
            sorted_tools[new_tool_id] = tools[old_tool_id]
        
        # 更新孔的工具ID
        sorted_holes = []
        for hole in holes:
            old_hole = dict(hole)  # 创建副本
            old_tool_id = old_hole['序号'][1:]  # 去掉 'T' 前缀
            
            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                new_hole = dict(old_hole)
                new_hole['序号'] = 'T' + new_tool_id
                
                # 更新钻头直径信息
                if '钻头直径(mm)' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                elif '钻头直径' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                
                sorted_holes.append(new_hole)
            else:
                # 如果找不到映射，保留原始数据
                sorted_holes.append(old_hole)
        
        return sorted_tools, sorted_holes, tool_mapping

    def open_tool_editor_treeview(self, tools, holes, slot_lengths, file_path):
        """使用Treeview创建可拖动列宽的表格编辑器"""
        # 计算每个钻头的孔数
        hole_counts = {}
        for hole in holes:
            tool_id = hole['序号']
            hole_counts[tool_id] = hole_counts.get(tool_id, 0) + 1
            
        # PANEL_A值直接使用孔数（按照C#代码drillRadioGroup1=2的逻辑）
        # 根据用户提供的请求参数：drillRadioGroup1=2
        panel_a_counts = hole_counts.copy()  # 使用孔数作为PANEL_A值

        total_holes = sum(hole_counts.values())

        # 创建编辑器窗口
        editor_window = tk.Toplevel(self.root)
        editor_window.title("钻头信息编辑器 - 专业版")
        editor_window.geometry("1400x700")
        editor_window.grab_set()  # 设置为模态窗口
        editor_window.configure(bg='#f0f0f0')

        # 设置窗口图标和样式
        editor_window.resizable(True, True)
        editor_window.minsize(1200, 600)

        # 创建主框架 - 使用现代化样式
        main_frame = ttk.Frame(editor_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 创建标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        # 主标题
        title_label = ttk.Label(title_frame, text="🔧 钻头信息编辑器",
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)

        # 统计信息
        stats_label = ttk.Label(title_frame,
                               text=f"📊 {len(tools)}种钻头 | {total_holes}个孔位",
                               font=("Microsoft YaHei", 10))
        stats_label.pack(side=tk.RIGHT)

        # 创建表格容器 - 添加边框和阴影效果
        table_container = ttk.LabelFrame(main_frame, text="📋 钻头详细信息", padding=10)
        table_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建表格框架
        tree_frame = ttk.Frame(table_container)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 配置现代化样式
        style = ttk.Style()

        # 设置主题
        style.theme_use('clam')

        # 配置Treeview样式 - 修复表头和行样式
        style.configure("Modern.Treeview",
                       background="#ffffff",
                       foreground="#333333",
                       rowheight=30,
                       fieldbackground="#ffffff",
                       font=("Microsoft YaHei", 9),
                       borderwidth=1,
                       relief="solid")

        # 配置表头样式 - 确保颜色正确显示
        style.configure("Modern.Treeview.Heading",
                       background="#2c3e50",
                       foreground="white",
                       font=("Microsoft YaHei", 10, "bold"),
                       relief="raised",
                       borderwidth=2)

        # 配置选中和悬停效果
        style.map("Modern.Treeview",
                 background=[('selected', '#e8f4fd'), ('active', '#f0f8ff')],
                 foreground=[('selected', '#2c3e50')])

        style.map("Modern.Treeview.Heading",
                 background=[('active', '#34495e'), ('pressed', '#1a252f')],
                 foreground=[('active', 'white'), ('pressed', 'white')])

        # 定义列
        columns = ("序号", "钻头直径", "孔数", "PANEL_A", "PTH", "成品孔径", "公差", "钻槽长度", "符号", "磨次", "备注")

        # 创建Treeview - 使用自定义样式
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings',
                           height=22, style="Modern.Treeview")

        # 设置列标题和宽度 - 优化列宽分配
        column_config = {
            "序号": {"width": 70, "anchor": "center", "text": "🔢 序号"},
            "钻头直径": {"width": 90, "anchor": "center", "text": "📏 钻头直径(mm)"},
            "孔数": {"width": 70, "anchor": "center", "text": "🔵 孔数"},
            "PANEL_A": {"width": 80, "anchor": "center", "text": "📊 PANEL_A"},
            "PTH": {"width": 60, "anchor": "center", "text": "🔗 PTH"},
            "成品孔径": {"width": 100, "anchor": "center", "text": "⚙️ 成品孔径"},
            "公差": {"width": 80, "anchor": "center", "text": "📐 公差"},
            "钻槽长度": {"width": 180, "anchor": "w", "text": "📊 钻槽长度"},
            "符号": {"width": 80, "anchor": "center", "text": "🔤 符号"},
            "磨次": {"width": 70, "anchor": "center", "text": "🔄 磨次"},
            "备注": {"width": 250, "anchor": "w", "text": "📝 备注"}
        }

        for col in columns:
            config = column_config[col]
            tree.heading(col, text=config["text"])
            tree.column(col, width=config["width"], minwidth=60, anchor=config["anchor"])

        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 放置Treeview和滚动条
        tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # 配置grid权重
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # 配置交替行颜色
        tree.tag_configure('oddrow', background='#f8f9fa')
        tree.tag_configure('evenrow', background='#ffffff')
        tree.tag_configure('slotrow', background='#e8f5e8', foreground='#2d5a2d')

        # 填充数据 - 添加颜色区分和数据格式化
        row_count = 0
        for k, diameter in sorted(tools.items(), key=lambda x: x[0] if isinstance(x[0], int) else int(x[0]) if x[0].isdigit() else x[0]):
            tool_id = 'T' + str(k)
            hole_count = hole_counts.get(tool_id, 0)

            # 获取槽长信息并格式化备注
            slot_remark_value = slot_lengths.get(k, "").strip()
            remark_value = f"SLOT {slot_remark_value}" if slot_remark_value else ""

            # 格式化直径显示
            diameter_display = f"{diameter:.3f}"

            # 格式化孔数显示
            hole_display = f"{hole_count:,}" if hole_count > 0 else "0"
            
            # 格式化PANEL_A显示
            panel_a_count = panel_a_counts.get(tool_id, 0)
            panel_a_display = f"{panel_a_count:,}" if panel_a_count > 0 else "0"

            # 确定行标签
            if slot_remark_value:
                row_tag = 'slotrow'
                remark_display = f"🎯 {remark_value}"
            else:
                row_tag = 'evenrow' if row_count % 2 == 0 else 'oddrow'
                remark_display = remark_value

            # 插入数据行
            item = tree.insert('', 'end', values=(
                tool_id,                    # 序号
                diameter_display,           # 钻头直径
                hole_display,               # 孔数
                panel_a_display,            # PANEL_A
                "",                         # PTH
                "",                         # 成品孔径
                "",                         # 公差
                slot_remark_value,          # 钻槽长度
                "",                         # 符号
                "",                         # 磨次
                remark_display              # 备注
            ), tags=(row_tag,))

            row_count += 1

        # 创建底部操作栏
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(15, 0))

        # 左侧状态信息
        status_frame = ttk.Frame(bottom_frame)
        status_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 状态信息标签
        status_text = f"📈 数据统计: {len(tools)}种钻头 | {total_holes}个孔位 | {row_count}行数据"
        status_label = ttk.Label(status_frame, text=status_text,
                                font=("Microsoft YaHei", 9), foreground="#666666")
        status_label.pack(side=tk.LEFT, pady=5)

        # 右侧按钮组
        button_frame = ttk.Frame(bottom_frame)
        button_frame.pack(side=tk.RIGHT)

        # 配置按钮样式
        style.configure("Action.TButton",
                       font=("Microsoft YaHei", 10),
                       padding=(20, 8))

        style.configure("Primary.TButton",
                       font=("Microsoft YaHei", 10, "bold"),
                       padding=(25, 10))

        def save_and_export():
            """保存并导出Excel"""
            try:
                # 获取所有数据
                data = []
                for item in tree.get_children():
                    values = tree.item(item, 'values')
                    # 清理备注中的emoji
                    remark = values[9].replace("🎯 ", "") if values[9] else ""
                    data.append({
                        '序号': values[0],
                        '钻头直径': values[1],
                        '孔数': values[2],
                        'PANEL_A': values[3],  # 添加PANEL_A
                        'PTH': values[4],
                        '成品孔径': values[5],
                        '公差': values[6],
                        '钻槽长度': values[7],
                        '符号': values[8],
                        '磨次': values[9],
                        '备注': remark
                    })

                # 导出Excel
                output_path = file_path.replace('.drl', '_钻头信息.xlsx')
                self.export_data_to_excel(data, holes, output_path)

                messagebox.showinfo("✅ 导出成功",
                                  f"Excel文件已成功保存到:\n{output_path}\n\n包含 {len(data)} 行数据")
                editor_window.destroy()

            except Exception as e:
                messagebox.showerror("❌ 导出失败", f"保存过程中发生错误:\n{str(e)}")

        # 添加现代化按钮
        cancel_btn = ttk.Button(button_frame, text="❌ 取消",
                               command=editor_window.destroy, style="Action.TButton")
        cancel_btn.pack(side=tk.RIGHT, padx=(0, 10))

        export_btn = ttk.Button(button_frame, text="📊 导出Excel",
                               command=save_and_export, style="Primary.TButton")
        export_btn.pack(side=tk.RIGHT)

    def export_data_to_excel(self, data, holes, output_path):
        """导出数据到Excel文件 - 简化版本避免卡顿"""
        try:
            import pandas as pd

            # 创建DataFrame
            tools_df = pd.DataFrame(data)
            holes_df = pd.DataFrame(holes)

            # 快速保存到Excel，不添加复杂样式
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                tools_df.to_excel(writer, sheet_name='钻头信息', index=False)
                holes_df.to_excel(writer, sheet_name='钻孔数据', index=False)

        except Exception as e:
            raise Exception(f"Excel导出失败: {str(e)}")

    def open_tool_editor(self, tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, file_path, simple_coord_counts=None):
        # 确保所有孔的PANEL_A值正确设置
        # 不再强制设置PANEL_A值为8，使用parse_drl_file中计算的值
        
        # 使用正确的孔数（基于simple_coord_counts）
        hole_counts = {}
        if simple_coord_counts:
            # 使用传入的正确孔数
            print("=== simple_coord_counts调试 ===")
            for tool_id, count in simple_coord_counts.items():
                print(f"工具{tool_id}: 坐标行数={count}")
                hole_counts[f'T{tool_id}'] = count
            print("=== hole_counts结果 ===")
            for tool_id, count in hole_counts.items():
                print(f"{tool_id}: 孔数={count}")
            print("========================")
        else:
            # 备用方案：基于holes计算
            for hole in holes:
                tool_id = hole['序号']
                hole_counts[tool_id] = hole_counts.get(tool_id, 0) + 1

        # 调试输出：显示计算出的drill_counts
        print("=== 钻孔数量计算结果 ===")
        for tool_id in sorted(drill_counts.keys()):
            print(f"T{tool_id}: 累计drill_count = {drill_counts[tool_id]}")
        print("========================")

        # 显示槽数量统计
        print("=== 槽数量统计 ===")
        for tool_id in sorted(drill_counts.keys()):
            if drill_counts[tool_id] > 0:
                # 计算平均每槽drill_count
                avg_drill_count = drill_counts[tool_id] / max(1, slot_counts.get(tool_id, 1))
                print(f"T{tool_id}: 总槽数={slot_counts.get(tool_id, 0)}, 累计drill_count={drill_counts[tool_id]}, 平均每槽={avg_drill_count:.1f}")
        print("===================")

        # 显示PANEL_A计数统计
        print("=== PANEL_A统计（open_tool_editor） ===")
        for tool_id in sorted(panel_a_counts.keys()):
            print(f"T{tool_id}: PANEL_A计数={panel_a_counts[tool_id]}")
        print("=========================================")

        total_holes = sum(hole_counts.values())
        
        # 创建编辑器窗口
        editor_window = tk.Toplevel(self.root)
        editor_window.title("编辑钻头信息")
        editor_window.geometry("900x500")
        editor_window.grab_set()  # 设置为模态窗口
        
        # 创建主框架
        main_frame = ttk.Frame(editor_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        ttk.Label(main_frame, text="钻头信息编辑", font=("SimSun", 14, "bold")).pack(pady=10)
        
        # 创建带滚动条的画布 - 使用高效渲染
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        canvas = tk.Canvas(canvas_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        
        # 配置画布和滚动条
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置画布和滚动条
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        def _on_mousewheel(event):
                # 检查事件源是否是Combobox
            widget = event.widget
            if isinstance(widget, ttk.Combobox):
                return "break"  # 如果是Combobox，完全阻止事件处理
            
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        

        
        # 移除全局绑定，改为只绑定到canvas和scrollable_frame
        canvas.bind("<MouseWheel>", _on_mousewheel)
        scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
        
        # 为所有非Combobox的控件绑定鼠标滚轮事件，而不是全局绑定
        def bind_mousewheel_to_children(widget):
            for child in widget.winfo_children():
                if not isinstance(child, ttk.Combobox):  # 不绑定到Combobox
                    child.bind("<MouseWheel>", _on_mousewheel)
                    if hasattr(child, 'winfo_children'):
                        bind_mousewheel_to_children(child)
        
        # 递归绑定到所有非Combobox子控件
        bind_mousewheel_to_children(scrollable_frame)
        
        frame = ttk.Frame(scrollable_frame)
        frame.pack(fill=tk.BOTH, expand=True)
        frame.bind("<MouseWheel>", _on_mousewheel)  # 确保frame也能响应滚轮
        columns = ["序号", "钻头直径", "孔数", "PANEL_A", "PTH", "成品孔径", "公差", "钻槽长度", "符号", "磨次", "备注"]
        for i, col in enumerate(columns):
            ttk.Label(frame, text=col, font=("SimSun", 10, "bold")).grid(
                row=0, column=i, padx=5, pady=5, sticky="nsew"
            )
        # 设置表格样式
        style = ttk.Style()
        style.configure("TFrame", font=("SimSun", 10))
        style.configure("TLabel", font=("SimSun", 10))
        style.configure("TEntry", font=("SimSun", 10))
        style.configure("TButton", font=("SimSun", 10))
        style.configure("TCombobox", font=("SimSun", 10))
        for i in range(10):  # 10列
            frame.grid_columnconfigure(i, weight=1)
        tool_entries = {}
        row_idx = 1
        for k, diameter in sorted(tools.items(), key=lambda x: x[0] if isinstance(x[0], int) else int(x[0]) if x[0].isdigit() else x[0]):
            tool_id = 'T' + str(k)
            tool_entries[tool_id] = {}
            
            # 序号
            ttk.Label(frame, text=tool_id, font=("SimSun", 10)).grid(
                row=row_idx, column=0, padx=5, pady=2, sticky="nsew"
            )

            diameter_var = tk.StringVar(value=str(diameter))
            diameter_entry = ttk.Entry(frame, width=10, textvariable=diameter_var, font=("SimSun", 10))
            diameter_entry.grid(row=row_idx, column=1, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['diameter'] = diameter_var

            hole_count = hole_counts.get(tool_id, 0)
            ttk.Label(frame, text=str(hole_count), font=("SimSun", 10)).grid(
                row=row_idx, column=2, padx=5, pady=2, sticky="nsew")
                
            # PANEL_A - 使用传入的panel_a_counts参数
            panel_a_count = panel_a_counts.get(k, hole_count)  # 使用传入的panel_a_counts，如果没有则使用孔数
            # 调试输出
            print(f"open_tool_editor - T{k}: PANEL_A = {panel_a_count}, panel_a_counts = {panel_a_counts.get(k, 'None')}")
            panel_a_var = tk.StringVar(value=str(panel_a_count))
            ttk.Label(frame, text=str(panel_a_count), font=("SimSun", 10)).grid(
                row=row_idx, column=3, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['panel_a'] = panel_a_var

            pth_var = tk.StringVar(value="")
            pth_combo = ttk.Combobox(frame, width=5, textvariable=pth_var, values=["Y", "N", ""],
                                    state="readonly", font=("SimSun", 10))
            pth_combo.grid(row=row_idx, column=4, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['pth'] = pth_var

            finished_var = tk.StringVar()
            finished_entry = ttk.Entry(frame, width=10, textvariable=finished_var,
                                     font=("SimSun", 10))
            finished_entry.grid(row=row_idx, column=5, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['finished'] = finished_var

            tolerance_var = tk.StringVar()
            tolerance_entry = ttk.Entry(frame, width=15, textvariable=tolerance_var,
                                      font=("SimSun", 10))
            tolerance_entry.grid(row=row_idx, column=6, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['tolerance'] = tolerance_var
            # 检查钻头直径是否本身就是三位小数（只有三位小数的才显示槽长度）
            # 使用与槽长计算相同的逻辑
            diameter_formatted = f"{diameter:.3f}"
            has_three_decimals = False
            if '.' in diameter_formatted:
                decimal_part = diameter_formatted.split('.')[1]
                # 去掉末尾的0后检查是否还有3位数字
                decimal_trimmed = decimal_part.rstrip('0')
                # 如果原始有3位小数且去掉0后仍有数字，说明是真正的三位小数
                has_three_decimals = len(decimal_part) == 3 and len(decimal_trimmed) > 0

            # 只有三位小数的钻头才显示槽长度，其他显示为空
            if has_three_decimals:
                slot_remark_value = slot_lengths.get(k, "").strip()

                # 修复槽长显示的直径问题：将槽长中的直径替换为当前工具的实际直径
                if slot_remark_value:
                    # 将槽长备注中的直径替换为当前工具的实际直径
                    # 例如：将 "1.101x2.52 1.101x255.41" 替换为 "1.1x2.52 1.1x255.41"
                    import re
                    # 查找所有 "数字x数字" 的模式，并替换直径部分
                    def replace_diameter(match):
                        old_diameter = match.group(1)
                        slot_length = match.group(2)
                        # 过滤掉超长的槽，ERP不显示这些值
                        if float(slot_length) > 50:
                            return ""
                        return f"{diameter}x{slot_length}"

                    # 先替换所有直径，再过滤超长槽
                    slot_remark_value = re.sub(r'(\d+\.\d+)x(\d+\.\d+)', replace_diameter, slot_remark_value)
                    # 清理可能留下的多余空格
                    slot_remark_value = re.sub(r'\s+', ' ', slot_remark_value).strip()
            else:
                slot_remark_value = ""

            length_var = tk.StringVar(value=slot_remark_value)
            length_entry = ttk.Entry(frame, width=15, textvariable=length_var, font=("SimSun", 10))
            length_entry.grid(row=row_idx, column=7, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['length'] = length_var
            symbol_var = tk.StringVar()
            symbol_entry = ttk.Entry(frame, width=10, textvariable=symbol_var, font=("SimSun", 10))
            symbol_entry.grid(row=row_idx, column=8, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['symbol'] = symbol_var
            grind_var = tk.StringVar()
            grind_values = ["", "M0", "M1", "M2", "M3", "M4"]
            grind_combo = ttk.Combobox(frame, width=5, textvariable=grind_var, values=grind_values,
                                      state="readonly", font=("SimSun", 10))
            grind_combo.grid(row=row_idx, column=9, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['grind'] = grind_var
            # 模拟C#代码中的数据库查询逻辑：drills_remark表
            # 根据钻头直径查询预设备注，如果有预设备注则优先使用，否则使用SLOT信息
            drill_diameter_remarks = {
                # 根据您提供的实际ERP数据库drills_remark表内容配置
                # RKEY  DRILL_DIA  DRILL_REMARK
                0.505: "机台孔",        # RKEY=1
                2.002: "定位工具孔",    # RKEY=2
                2.003: "激光定位孔",    # RKEY=3
                3.175: "工具孔",        # RKEY=4
                3.176: "防呆孔",        # RKEY=5
                3.202: "靶孔",          # RKEY=6
                0.504: "料号孔",        # RKEY=7
                2.004: "工具孔",        # RKEY=8
                0.076: "复合靶",        # RKEY=9
                0.1: "激光钻孔",        # RKEY=10
            }

            # 备注默认值逻辑：根据C#代码drillRadioGroup1=2的逻辑
            remark_default_value = ""

            # 根据数据库映射获取预定义备注
            predefined_remark = drill_diameter_remarks.get(diameter, "")

            # 检查是否为无铜孔（小数点第三位是3）
            # C#逻辑: if (tmpfloat * 1000.0 - tmpfloat2 == 3.0) { SPEC_MARK = "无铜孔"; }
            # 其中 tmpfloat2 = Math.Floor(tmpfloat * 100.0) * 10.0
            is_npth_hole = False
            tmpfloat = diameter
            tmpfloat2 = int(tmpfloat * 100.0) * 10.0  # Math.Floor然后乘以10
            if abs(tmpfloat * 1000.0 - tmpfloat2 - 3.0) < 0.001:  # 使用小的误差范围避免浮点精度问题
                is_npth_hole = True

            # 如果是无铜孔，优先设置为"无铜孔"
            if is_npth_hole:
                remark_default_value = "无铜孔"
            else:
                # 检查钻头直径是否本身就是三位小数（只有三位小数的才计算槽长）
                # 例如：0.751, 0.753, 1.101, 1.001 是三位小数，1.0, 1.00 不是三位小数
                # 使用与槽长计算相同的逻辑
                diameter_formatted = f"{diameter:.3f}"
                has_three_decimals = False
                if '.' in diameter_formatted:
                    decimal_part = diameter_formatted.split('.')[1]
                    decimal_trimmed = decimal_part.rstrip('0')
                    has_three_decimals = len(decimal_part) == 3 and len(decimal_trimmed) > 0
                strremark = "SLOT" 
                if slot_remark_value and has_three_decimals: 
                    # C#: if (strremark.Contains("SLOT")) 分支
                    if predefined_remark:
                        # C#: dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString()
                        remark_default_value = f"{predefined_remark} SLOT {slot_remark_value}"
                    else:
                        # C#: dr29["REMARK"] = strremark + " " + List4[k].ToString()
                        remark_default_value = f"SLOT {slot_remark_value}"
                else: 
                    remark_default_value = predefined_remark if predefined_remark else ""

            remark_var = tk.StringVar(value=remark_default_value)
            remark_values = ["", "过孔", "料号孔", "BGA区域过孔", "近孔", "邮票孔", "工艺孔", "机台孔", "清尘槽", "SLOT", "靶孔", "试钻孔", "长槽", "短槽"]
            remark_combo = ttk.Combobox(frame, width=35, textvariable=remark_var, values=remark_values,
                                       font=("SimSun", 10))
            remark_combo.grid(row=row_idx, column=10, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['remark'] = remark_var
            
            # 设置默认值和绑定更新事件
            def update_values(tool_id=tool_id, diameter_var=diameter_var, pth_var=pth_var, 
                             finished_var=finished_var, tolerance_var=tolerance_var, remark_var=remark_var):
                try:
                    diameter = float(diameter_var.get())
                    pth = pth_var.get()
                    
                    # 自动设置备注 - 仅当用户选择了PTH时
                    if pth:
                        remark = ""
                        if abs(diameter - 3.202) < 0.001:
                            remark = "靶孔"
                        elif abs(diameter - 0.307) < 0.001:
                            remark = "试钻孔"
                        else:
                            # 检查小数点后第三位
                            third_decimal = int((diameter * 1000) % 10)
                            if third_decimal == 1:
                                remark = "长槽"
                            elif third_decimal == 2:
                                remark = "短槽"
                        
                        # 只有在备注为空时才设置
                        if not remark_var.get():
                            remark_var.set(remark)
                    
                    # PTH切换时始终刷新成品孔径和公差
                    if pth == "Y":
                        finished = round(diameter - 0.1, 3)
                        tolerance = "+0.075/-0.075"
                    elif pth == "N":
                        finished = round(diameter - 0.05, 3)
                        tolerance = "+0.05/-0.05"
                    else:  # pth为空
                        finished = ""
                        tolerance = ""
                    finished_var.set(str(finished) if finished else "")
                    tolerance_var.set(tolerance)
                except ValueError:
                    pass
            
            # 绑定事件
            diameter_var.trace_add("write", lambda *args, t=tool_id, d=diameter_var, p=pth_var, 
                                 f=finished_var, tol=tolerance_var, r=remark_var: 
                                 update_values(t, d, p, f, tol, r))
            pth_var.trace_add("write", lambda *args, t=tool_id, d=diameter_var, p=pth_var, 
                            f=finished_var, tol=tolerance_var, r=remark_var: 
                            update_values(t, d, p, f, tol, r))
            
            def prevent_scroll_interference(combo):
                combo.bind("<MouseWheel>", lambda e: "break")
                combo.bind("<<ComboboxSelected>>", lambda e: scrollable_frame.focus_set())
                
            prevent_scroll_interference(pth_combo)
            prevent_scroll_interference(grind_combo)
            prevent_scroll_interference(remark_combo)
            
            row_idx += 1
        
        ttk.Separator(frame, orient='horizontal').grid(row=row_idx, column=0, columnspan=11, sticky='ew', pady=5)
        row_idx += 1
        
        ttk.Label(frame, text="合计", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=0, padx=5, pady=2, sticky="nsew"
        )
        
        ttk.Label(frame, text=f"{total_holes}", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=2, padx=5, pady=2, sticky="nsew"
        )
        total_panel_a = sum(panel_a_counts.values())
        ttk.Label(frame, text=f"{total_panel_a}", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=3, padx=5, pady=2, sticky="nsew"
        )
        for child in frame.winfo_children():
            child.grid_configure(padx=3, pady=3)
        bind_mousewheel_to_children(frame)
        for child in frame.winfo_children():
            if isinstance(child, ttk.Combobox):
                prevent_scroll_interference(child)
        
        # 添加底部操作区
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10, fill=tk.X)
        
        # 保存按钮
        save_button = ttk.Button(
            button_frame, 
            text="保存并导出Excel", 
            command=lambda: self.save_and_export(editor_window, tools, holes, tool_entries, hole_counts, total_holes, file_path),
            style="Accent.TButton"
        )
        save_button.pack(side=tk.RIGHT, padx=10)
        
        # 取消按钮
        cancel_button = ttk.Button(
            button_frame, 
            text="取消", 
            command=editor_window.destroy
        )
        cancel_button.pack(side=tk.RIGHT, padx=10)
    def save_and_export(self, editor_window, tools, holes, tool_entries, hole_counts, total_holes, file_path):
        print("=== 导出时的PANEL_A值 ===")
        tool_panel_a = {}
        for hole in holes:
            tool_id = hole['序号']
            panel_a = hole['PANEL_A']
            if tool_id not in tool_panel_a:
                tool_panel_a[tool_id] = []
            tool_panel_a[tool_id].append(panel_a)
        
        for tool_id, panel_a_values in sorted(tool_panel_a.items()):
            print(f"{tool_id}: PANEL_A值 = {panel_a_values[:5]}... (共{len(panel_a_values)}个)")
        print("========================")
        
        # 创建钻头信息表
        tool_data = []
        
        # 先聚合PANEL_A值（按照C#代码逻辑）
        panel_a_totals = {}
        for hole in holes:
            if 'PANEL_A' in hole and '序号' in hole:
                tool_id = hole['序号']
                if tool_id not in panel_a_totals:
                    panel_a_totals[tool_id] = 0
                panel_a_totals[tool_id] += hole['PANEL_A']
        
        for tool_id, entries in tool_entries.items():
            try:
                # 处理成品孔径可能为空的情况
                finished_val = entries['finished'].get()
                if finished_val and finished_val.strip():
                    finished_val = float(finished_val)
                else:
                    finished_val = None
                    
                # 获取PANEL_A聚合值
                panel_a_val = panel_a_totals.get(tool_id, 0)
                    
                tool_data.append({
                    '序号': tool_id,
                    '钻头直径': float(entries['diameter'].get()),
                    '孔数': hole_counts.get(tool_id, 0),
                    'PANEL_A': panel_a_val,  # 添加PANEL_A值
                    'PTH': entries['pth'].get(),
                    '成品孔径': finished_val,
                    '公差': entries['tolerance'].get(),
                    '钻槽长度': entries['length'].get(),
                    '符号': entries['symbol'].get(),
                    '磨次': entries['grind'].get(),
                    '备注': entries['remark'].get()
                })
            except ValueError as e:
                messagebox.showerror("错误", f"工具 {tool_id} 数据格式错误: {str(e)}")
                return
        
        # 添加合计行
        tool_data.append({
            '序号': '合计',
            '钻头直径': None,
            '孔数': total_holes,
            'PANEL_A': sum(panel_a_totals.values()),  # 添加PANEL_A总计
            'PTH': None,
            '成品孔径': None,
            '公差': None,
            '钻槽长度': None,
            '符号': None,
            '磨次': None,
            '备注': None
        })
        
        # 更新孔数据
        for hole in holes:
            tool_id = hole['序号']
            
            if '钻头直径(mm)' in hole:
                hole['钻头直径'] = hole.pop('钻头直径(mm)')
            
            if tool_id in tool_entries:
                if '钻头直径' in hole:
                    pass  # 已经是正确的键名
                
                hole['PTH'] = tool_entries[tool_id]['pth'].get()
                hole['成品孔径'] = tool_entries[tool_id]['finished'].get()
                hole['公差'] = tool_entries[tool_id]['tolerance'].get()
                hole['钻槽长度'] = tool_entries[tool_id]['length'].get()
                hole['符号'] = tool_entries[tool_id]['symbol'].get()
                hole['磨次'] = tool_entries[tool_id]['grind'].get()
                hole['备注'] = tool_entries[tool_id]['remark'].get()
        
        # 创建数据框
        tools_df = pd.DataFrame(tool_data)
        holes_df = pd.DataFrame(holes)
        
        # 创建保存进度窗口
        progress_window = tk.Toplevel(editor_window)
        progress_window.title("保存中")
        progress_window.geometry("300x100")
        progress_window.transient(editor_window)
        progress_window.grab_set()
        
        progress_label = ttk.Label(progress_window, text="正在保存Excel文件...", font=("SimSun", 10))
        progress_label.pack(pady=10)
        
        progress_bar = ttk.Progressbar(progress_window, orient="horizontal", length=250, mode="indeterminate")
        progress_bar.pack(pady=10)
        progress_bar.start(10)
        
        progress_window.update()
        
        # 设置输出路径
        output_path = os.path.splitext(file_path)[0] + '.xlsx'
        
        # 快速保存Excel文件 - 避免卡顿
        def save_excel_file():
            try:
                # 简化保存，不添加复杂样式
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    tools_df.to_excel(writer, sheet_name='钻头信息', index=False)
                    holes_df.to_excel(writer, sheet_name='钻孔数据', index=False)

                
                # 关闭进度窗口
                progress_window.destroy()
                
                # 关闭编辑器窗口
                editor_window.destroy()
                
                # 显示成功消息并询问是否打开文件
                result = messagebox.askyesno(
                    "处理成功", 
                    f"文件已转换为Excel格式:\n{output_path}\n\n是否立即打开该文件？"
                )
                
                # 如果用户选择打开文件
                if result:
                    try:
                        os.startfile(output_path)
                    except:
                        messagebox.showinfo("提示", f"请手动打开文件:\n{output_path}")
                
                # 更新状态
                self.status_label.config(text="转换完成")
                
            except Exception as e:
                # 关闭进度窗口并显示错误
                if progress_window.winfo_exists():
                    progress_window.destroy()
                messagebox.showerror("保存失败", f"导出Excel时出错: {str(e)}")
        
        # 启动保存线程
        save_thread = threading.Thread(target=save_excel_file)
        save_thread.daemon = True
        save_thread.start()

def main():
    import tkinter as tk
    from tkinter import messagebox
    # 命令行参数处理
    parser = argparse.ArgumentParser(description='DRL文件转Excel工具')
    parser.add_argument('--file', '-f', help='要处理的DRL文件路径')
    args = parser.parse_args()
    
    # 如果提供了文件参数，直接处理文件
    if args.file:
        try:
            # 创建根窗口但不显示
            root = tk.Tk()
            root.withdraw()
            
            # 验证许可证
            validator = LicenseValidator()
            if not validator.is_valid():
                sys.exit(1)

            # 创建应用实例
            app = DrlConverterApp(root)

            # 处理文件
            tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, simple_coord_counts = app.parse_drl_file(args.file)

            # 退出
            root.destroy()
            return
        except Exception as e:
            return
    
    # 捕获未处理的异常
    def handle_exception(exc_type, exc_value, exc_traceback):
        import traceback
        # 获取异常信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        # 显示错误对话框
        messagebox.showerror("程序错误", f"程序遇到未处理的异常:\n{error_msg}")
        sys.exit(1)
    
    # 设置异常处理器
    sys.excepthook = handle_exception
    
    try:
        # 创建主窗口前优化Tkinter设置
        root = tk.Tk()
        # 设置DPI感知
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
        # 设置窗口属性
        root.title("JSYPCB钻带转Excel工具")
        root.resizable(True, True)  # 允许调整大小
        # 设置窗口居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = 400
        window_height = 250
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 验证许可证
        validator = LicenseValidator()
        if not validator.is_valid():
            root.withdraw()
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            exit(1)
        
        # 创建应用实例
        app = DrlConverterApp(root)
        # 配置应用样式
        style = ttk.Style()
        if "vista" in style.theme_names():
            style.theme_use("vista")
        elif "clam" in style.theme_names():
            style.theme_use("clam")
        
        # 在Windows上配置按钮样式
        if os.name == 'nt':
            style.configure("Accent.TButton", 
                           background="#4CAF50", 
                           foreground="black",  
                           font=("SimSun", 10, "bold"))
        
        # 设置关闭窗口处理
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出程序吗?"):
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动主循环
        root.mainloop()

    except Exception as e:
        # 显示错误对话框
        if 'root' in locals():
            root.withdraw()
        messagebox.showerror("启动错误", f"程序启动时出错:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()