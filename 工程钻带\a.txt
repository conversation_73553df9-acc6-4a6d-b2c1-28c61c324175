using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using ENGI03.Common;
using ENGI03.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace ENGI03.Areas.ENGI03.Controllers
{
	// Token: 0x0200003A RID: 58
	public class DrillDataController : Controller
	{
		// Token: 0x0600024F RID: 591 RVA: 0x00052180 File Offset: 0x00050380
		public ActionResult GetControl_drill()
		{
			string sqlv = "select data1195.TabSht_Drill1_vflag ,data1195.TabSht_Drill2_vflag,data1195.TabSht_Drill3_vflag,data1195.TabSht_Drill4_vflag,data1195.TabSht_Drill5_vflag,data1195.TabSht_Drill6_vflag,data1195.TabSht_Drill7_vflag,data1195.TabSht_Drill8_vflag\r\n,data1195.TabSht_Drill9_vflag,data1195.TabSht_Drill10_vflag\t,data1195.TabSht_Drill11_vflag ,data1195.TabSht_Drill12_vflag,data1195.TabSht_Drill13_vflag,data1195.TabSht_Drill14_vflag,data1195.TabSht_Drill15_vflag,data1195.TabSht_Drill16_vflag\r\n,data1195.TabSht_Drill1,data1195.TabSht_Drill2,data1195.TabSht_Drill3,data1195.TabSht_Drill4,data1195.TabSht_Drill5,data1195.TabSht_Drill6,data1195.TabSht_Drill7,data1195.TabSht_Drill8\r\n,data1195.TabSht_Drill9,data1195.TabSht_Drill10,data1195.TabSht_Drill11\t,data1195.TabSht_Drill12,data1195.TabSht_Drill13,data1195.TabSht_Drill14,data1195.TabSht_Drill15,data1195.TabSht_Drill16\r\n,data0192.PTHNULL_NOTOPERATE\r\nfrom data1195(nolock),DATA0192(NOLOCK)";
			DataTable vdata1195v = DBHelper.ExecuteDataTable(sqlv, new SqlParameter[0]);
			return base.Json(new ReturnJsonInfo(100, vdata1195v.Rows.Count, "success", JsonConvert.SerializeObject(vdata1195v)), 0);
		}

		// Token: 0x06000250 RID: 592 RVA: 0x000521C4 File Offset: 0x000503C4
		public ActionResult GetData0025()
		{
			int vGroup_mode_flag = Convert.ToInt32(base.Request.QueryString["vGroup_mode_flag"]);
			int vFLOW_WHSE_PTR = Convert.ToInt32(base.Request.QueryString["vFLOW_WHSE_PTR"]);
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			int ihave = 0;
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			string sql25v;
			if (vGroup_mode_flag == 1)
			{
				sql25v = string.Format("\r\nSELECT DATA8025.ZTITLE1,DATA8025.ZTITLE2,DATA8025.ZTITLE3,DATA8025.ZTITLE4,DATA8025.ZTITLE5,DATA8025.ZTITLE6,DATA8025.ZTITLE7,DATA8025.ZTITLE8,\r\nDATA8025.ZTITLE9,DATA8025.ZTITLE10,DATA8025.ZTITLE11,DATA8025.ZTITLE12,DATA8025.ZTITLE13,DATA8025.ZTITLE14,DATA8025.ZTITLE15,DATA8025.ZTITLE16,\r\nDATA8025.ZFILE1,DATA8025.ZFILE2,DATA8025.ZFILE3,DATA8025.ZFILE4,DATA8025.ZFILE5,DATA8025.ZFILE6,DATA8025.ZFILE7,DATA8025.ZFILE8,\r\nDATA8025.ZFILE9,DATA8025.ZFILE10,DATA8025.ZFILE11,DATA8025.ZFILE12,DATA8025.ZFILE13,DATA8025.ZFILE14,DATA8025.ZFILE15,DATA8025.ZFILE16,\r\nDATA8025.ZMIN,DATA8025.ZMIN1,DATA8025.ZMIN2,DATA8025.ZMIN3,DATA8025.ZMIN4,DATA8025.ZMIN5,DATA8025.ZMIN6,DATA8025.ZMIN7,DATA8025.ZMIN8,\r\nDATA8025.ZMIN9,DATA8025.ZMIN10,DATA8025.ZMIN11,DATA8025.ZMIN12,DATA8025.ZMIN13,DATA8025.ZMIN14,DATA8025.ZMIN15,DATA8025.ZMIN16, \r\nDATA8025.ZCOUNT,DATA8025.ZCOUNT1,DATA8025.ZCOUNT2,DATA8025.ZCOUNT3,DATA8025.ZCOUNT4,DATA8025.ZCOUNT5,DATA8025.ZCOUNT6,DATA8025.ZCOUNT7,DATA8025.ZCOUNT8,\r\nDATA8025.ZCOUNT9,DATA8025.ZCOUNT10,DATA8025.ZCOUNT11,DATA8025.ZCOUNT12,DATA8025.ZCOUNT13,DATA8025.ZCOUNT14,DATA8025.ZCOUNT15,DATA8025.ZCOUNT16,\r\nDATA8025.SLOTDRILLCOUNT1,DATA8025.SLOTDRILLCOUNT2,DATA8025.SLOTDRILLCOUNT3,DATA8025.SLOTDRILLCOUNT4,DATA8025.SLOTDRILLCOUNT5,DATA8025.SLOTDRILLCOUNT6,\r\nDATA8025.SLOTDRILLCOUNT7,DATA8025.SLOTDRILLCOUNT8,DATA8025.SLOTDRILLCOUNT9,DATA8025.SLOTDRILLCOUNT10,DATA8025.SLOTDRILLCOUNT11,DATA8025.SLOTDRILLCOUNT12,\r\nDATA8025.SLOTDRILLCOUNT13,DATA8025.SLOTDRILLCOUNT14,DATA8025.SLOTDRILLCOUNT15,DATA8025.SLOTDRILLCOUNT16,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT1,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT1,'0') as int)  AS DRILLCOUNT1,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT2,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT2,'0') as int)  AS DRILLCOUNT2,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT3,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT3,'0') as int)  AS DRILLCOUNT3,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT4,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT4,'0') as int)  AS DRILLCOUNT4,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT5,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT5,'0') as int)  AS DRILLCOUNT5,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT6,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT6,'0') as int)  AS DRILLCOUNT6,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT7,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT7,'0') as int)  AS DRILLCOUNT7,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT8,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT8,'0') as int)  AS DRILLCOUNT8,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT9,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT9,'0') as int)  AS DRILLCOUNT0,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT10,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT10,'0') as int)  AS DRILLCOUNT10,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT11,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT11,'0') as int)  AS DRILLCOUNT11,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT12,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT12,'0') as int)  AS DRILLCOUNT12,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT13,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT13,'0') as int)  AS DRILLCOUNT13,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT14,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT14,'0') as int)  AS DRILLCOUNT14,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT15,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT15,'0') as int)  AS DRILLCOUNT15,\r\nTRY_CAST( '0'+ISNULL(DATA8025.ZCOUNT16,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA8025.SLOTDRILLCOUNT16,'0') as int)  AS DRILLCOUNT16 \r\nFROM DATA8025(NOLOCK)\r\nINNER JOIN DATA0025 ON DATA8025.D0025_PTR=DATA0025.RKEY \r\nWHERE DATA0025.RKEY={0} AND DATA8025.FLOW_WHSE_PTR={1} ", vD25RKEY, vFLOW_WHSE_PTR);
				DataTable vdata8025v = DBHelper.ExecuteDataTable(sql25v, new SqlParameter[0]);
				if (vdata8025v.Rows.Count <= 0)
				{
					ENGI03Compatible.CreateData8025(vGroup_mode_flag, vFLOW_WHSE_PTR, vD25RKEY, ref ihave, ref builder);
					if (ihave == 1)
					{
						string vlog = builder.ToString();
					}
				}
			}
			else
			{
				sql25v = string.Format("\r\nSELECT DATA0025.ZTITLE1,DATA0025.ZTITLE2,DATA0025.ZTITLE3,DATA0025.ZTITLE4,DATA0025.ZTITLE5,DATA0025.ZTITLE6,DATA0025.ZTITLE7,DATA0025.ZTITLE8,\r\nDATA0025.ZTITLE9,DATA0025.ZTITLE10,DATA0025.ZTITLE11,DATA0025.ZTITLE12,DATA0025.ZTITLE13,DATA0025.ZTITLE14,DATA0025.ZTITLE15,DATA0025.ZTITLE16,\r\nDATA0025.ZFILE1,DATA0025.ZFILE2,DATA0025.ZFILE3,DATA0025.ZFILE4,DATA0025.ZFILE5,DATA0025.ZFILE6,DATA0025.ZFILE7,DATA0025.ZFILE8,\r\nDATA0025.ZFILE9,DATA0025.ZFILE10,DATA0025.ZFILE11,DATA0025.ZFILE12,DATA0025.ZFILE13,DATA0025.ZFILE14,DATA0025.ZFILE15,DATA0025.ZFILE16,\r\nDATA0025.ZMIN,DATA0025.ZMIN1,DATA0025.ZMIN2,DATA0025.ZMIN3,DATA0025.ZMIN4,DATA0025.ZMIN5,DATA0025.ZMIN6,DATA0025.ZMIN7,DATA0025.ZMIN8,\r\nDATA0025.ZMIN9,DATA0025.ZMIN10,DATA0025.ZMIN11,DATA0025.ZMIN12,DATA0025.ZMIN13,DATA0025.ZMIN14,DATA0025.ZMIN15,DATA0025.ZMIN16, \r\nDATA0025.ZCOUNT,DATA0025.ZCOUNT1,DATA0025.ZCOUNT2,DATA0025.ZCOUNT3,DATA0025.ZCOUNT4,DATA0025.ZCOUNT5,DATA0025.ZCOUNT6,DATA0025.ZCOUNT7,DATA0025.ZCOUNT8,\r\nDATA0025.ZCOUNT9,DATA0025.ZCOUNT10,DATA0025.ZCOUNT11,DATA0025.ZCOUNT12,DATA0025.ZCOUNT13,DATA0025.ZCOUNT14,DATA0025.ZCOUNT15,DATA0025.ZCOUNT16,\r\nDATA0025.SLOTDRILLCOUNT1,DATA0025.SLOTDRILLCOUNT2,DATA0025.SLOTDRILLCOUNT3,DATA0025.SLOTDRILLCOUNT4,DATA0025.SLOTDRILLCOUNT5,DATA0025.SLOTDRILLCOUNT6,\r\nDATA0025.SLOTDRILLCOUNT7,DATA0025.SLOTDRILLCOUNT8,DATA0025.SLOTDRILLCOUNT9,DATA0025.SLOTDRILLCOUNT10,DATA0025.SLOTDRILLCOUNT11,DATA0025.SLOTDRILLCOUNT12,\r\nDATA0025.SLOTDRILLCOUNT13,DATA0025.SLOTDRILLCOUNT14,DATA0025.SLOTDRILLCOUNT15,DATA0025.SLOTDRILLCOUNT16,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT1,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT1,'0') as int)  AS DRILLCOUNT1,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT2,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT2,'0') as int)  AS DRILLCOUNT2,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT3,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT3,'0') as int)  AS DRILLCOUNT3,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT4,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT4,'0') as int)  AS DRILLCOUNT4,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT5,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT5,'0') as int)  AS DRILLCOUNT5,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT6,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT6,'0') as int)  AS DRILLCOUNT6,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT7,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT7,'0') as int)  AS DRILLCOUNT7,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT8,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT8,'0') as int)  AS DRILLCOUNT8,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT9,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT9,'0') as int)  AS DRILLCOUNT9,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT10,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT10,'0') as int)  AS DRILLCOUNT10,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT11,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT11,'0') as int)  AS DRILLCOUNT11,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT12,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT12,'0') as int)  AS DRILLCOUNT12,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT13,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT13,'0') as int)  AS DRILLCOUNT13,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT14,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT14,'0') as int)  AS DRILLCOUNT14,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT15,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT15,'0') as int)  AS DRILLCOUNT15,\r\nTRY_CAST( '0'+ISNULL(DATA0025.ZCOUNT16,'0') AS int )- TRY_CAST( '0'+ ISNULL(DATA0025.SLOTDRILLCOUNT16,'0') as int)  AS DRILLCOUNT16 \r\nFROM DATA0025(NOLOCK) WHERE DATA0025.RKEY={0}", vD25RKEY);
			}
			DataTable vdata0025v = DBHelper.ExecuteDataTable(sql25v, new SqlParameter[0]);
			vdata0025v.PrimaryKey = new DataColumn[]
			{
				vdata0025v.Columns["RKEY"]
			};
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, vdata0025v.Rows.Count, "success", JsonConvert.SerializeObject(vdata0025v)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, vdata0025v.Rows.Count, builder.ToString(), JsonConvert.SerializeObject(vdata0025v)), 0);
		}

		// Token: 0x06000251 RID: 593 RVA: 0x00052328 File Offset: 0x00050528
		public ActionResult GetData0229()
		{
			int vGroup_mode_flag = Convert.ToInt32(base.Request.QueryString["vGroup_mode_flag"]);
			int vFLOW_WHSE_PTR = Convert.ToInt32(base.Request.QueryString["vFLOW_WHSE_PTR"]);
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			SqlParameter[] p229 = new SqlParameter[2];
			p229[0] = new SqlParameter("@vptr", SqlDbType.Int);
			p229[0].Value = vD25RKEY;
			p229[1] = new SqlParameter("@vFLOW_WHSE_PTR", SqlDbType.Int);
			if (vGroup_mode_flag == 1)
			{
				p229[1].Value = vFLOW_WHSE_PTR;
			}
			else
			{
				p229[1].Value = 0;
			}
			DataTable vData229 = DBHelper.ExecuteStoredDataTable("ENGI03V;102", p229);
			vData229.PrimaryKey = new DataColumn[]
			{
				vData229.Columns["RKEY"]
			};
			return base.Json(new ReturnJsonInfo(100, vData229.Rows.Count, "success", JsonConvert.SerializeObject(vData229)), 0);
		}

		// Token: 0x06000252 RID: 594 RVA: 0x00052430 File Offset: 0x00050630
		public ActionResult GetData0230()
		{
			int vGroup_mode_flag = Convert.ToInt32(base.Request.QueryString["vGroup_mode_flag"]);
			int vFLOW_WHSE_PTR = Convert.ToInt32(base.Request.QueryString["vFLOW_WHSE_PTR"]);
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			SqlParameter[] p230 = new SqlParameter[2];
			p230[0] = new SqlParameter("@vptr", SqlDbType.Int);
			p230[0].Value = vD25RKEY;
			p230[1] = new SqlParameter("@vFLOW_WHSE_PTR", SqlDbType.Int);
			if (vGroup_mode_flag == 1)
			{
				p230[1].Value = vFLOW_WHSE_PTR;
			}
			else
			{
				p230[1].Value = 0;
			}
			DataTable vData230 = DBHelper.ExecuteStoredDataTable("ENGI03V;105", p230);
			vData230.PrimaryKey = new DataColumn[]
			{
				vData230.Columns["RKEY"]
			};
			return base.Json(new ReturnJsonInfo(100, vData230.Rows.Count, "success", JsonConvert.SerializeObject(vData230)), 0);
		}

		// Token: 0x06000253 RID: 595 RVA: 0x00052538 File Offset: 0x00050738
		public ActionResult GetData0029()
		{
			int vGroup_mode_flag = Convert.ToInt32(base.Request.QueryString["vGroup_mode_flag"]);
			int vFLOW_WHSE_PTR = Convert.ToInt32(base.Request.QueryString["vFLOW_WHSE_PTR"]);
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			SqlParameter[] p29 = new SqlParameter[2];
			p29[0] = new SqlParameter("@vptr", SqlDbType.Int);
			p29[0].Value = vD25RKEY;
			p29[1] = new SqlParameter("@vFLOW_WHSE_PTR", SqlDbType.Int);
			if (vGroup_mode_flag == 1)
			{
				p29[1].Value = vFLOW_WHSE_PTR;
			}
			else
			{
				p29[1].Value = 0;
			}
			DataTable vData29 = DBHelper.ExecuteStoredDataTable("ENGI03V;164", p29);
			vData29.PrimaryKey = new DataColumn[]
			{
				vData29.Columns["RKEY"]
			};
			return base.Json(new ReturnJsonInfo(100, vData29.Rows.Count, "success", JsonConvert.SerializeObject(vData29)), 0);
		}

		// Token: 0x06000254 RID: 596 RVA: 0x00052640 File Offset: 0x00050840
		public ActionResult GetData1192()
		{
			string sql1192 = "select top 1 data1192.drill_pcs_set_pnl\r\n,data1192.drill_order_by\r\n,data1192.drill_remark_list\r\n,data1192.GRIND_TIMES_LIST\r\n,data1192.drillfile_include_pnumber\r\n,data1192.drill_pth_list\r\n,data1192.DRILL_PTH\r\n,data1192.DRILL_NPTH\r\nfrom data1192(nolock)";
			DataTable vData1192 = DBHelper.ExecuteDataTable(sql1192, new SqlParameter[0]);
			vData1192.PrimaryKey = new DataColumn[]
			{
				vData1192.Columns["RKEY"]
			};
			return base.Json(new ReturnJsonInfo(100, vData1192.Rows.Count, "success", JsonConvert.SerializeObject(vData1192)), 0);
		}

		// Token: 0x06000255 RID: 597 RVA: 0x000526A4 File Offset: 0x000508A4
		public ActionResult GetData0008()
		{
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			string sql8 = string.Format(" SELECT RKEY, EST_SCRAP1_LMT, EST_SCRAP_LTM, PROD_CODE, PRODUCT_NAME,\r\n       isnull(DG_ADJ_PTH,0) as DG_ADJ_PTH, isnull(DG_ADJ_NPTH,0) as DG_ADJ_NPTH, IES_FLOW_PTR, ANALYSIS_CODE2,\r\n\t   isnull(DG_TOLERANCE_PTH,'') as DG_TOLERANCE_PTH, isnull(DG_TOLERANCE_NPTH,'') as DG_TOLERANCE_NPTH\r\n                                  FROM DATA0008(nolock) where RKEY IN (SELECT PROD_CODE_PTR FROM DATA0025(NOLOCK) WHERE RKEY={0}) ", vD25RKEY);
			DataTable vData8 = DBHelper.ExecuteDataTable(sql8, new SqlParameter[0]);
			vData8.PrimaryKey = new DataColumn[]
			{
				vData8.Columns["RKEY"]
			};
			return base.Json(new ReturnJsonInfo(100, vData8.Rows.Count, "success", JsonConvert.SerializeObject(vData8)), 0);
		}

		// Token: 0x06000256 RID: 598 RVA: 0x00052730 File Offset: 0x00050930
		public ActionResult DRILL_COUNT()
		{
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			JObject jo = (JObject)JsonConvert.DeserializeObject(myJson);
			Dictionary<string, object> vDrillHoleOption = JsonConvert.DeserializeObject<Dictionary<string, object>>(jo["DrillHoleOption"].ToString());
			DataTable TempTable = TableHelp.JsonToDataTable(jo["data0029"].ToString());
			Dictionary<string, object> vcontrol_drill = JsonConvert.DeserializeObject<Dictionary<string, object>>(jo["control_drill"].ToString());
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			int vPTHNULL_NOTOPERATE = 0;
			if (vcontrol_drill.ContainsKey("PTHNULL_NOTOPERATE") && vcontrol_drill["PTHNULL_NOTOPERATE"] != null)
			{
				vPTHNULL_NOTOPERATE = Convert.ToInt32(vcontrol_drill["PTHNULL_NOTOPERATE"].ToString());
			}
			string U_point = "#.00";
			if (vDrillHoleOption.ContainsKey("FORMAT") && vDrillHoleOption["FORMAT"].ToString().Trim() != "")
			{
				switch (Convert.ToInt32(vDrillHoleOption["FORMAT"].ToString()))
				{
				case 0:
					U_point = "#0";
					break;
				case 1:
					U_point = "#0.0";
					break;
				case 2:
					U_point = "#0.00";
					break;
				case 3:
					U_point = "#0.000";
					break;
				case 4:
					U_point = "#0.0000";
					break;
				case 5:
					U_point = "#0.00000";
					break;
				case 6:
					U_point = "#0.000000";
					break;
				}
			}
			string vPTH = "PTH";
			if (vDrillHoleOption.ContainsKey("PTH") && vDrillHoleOption["PTH"] != null && vDrillHoleOption["PTH"].ToString().Trim() != "")
			{
				vPTH = vDrillHoleOption["PTH"].ToString().Trim();
			}
			double vRANGE_FROM = 0.1;
			if (vDrillHoleOption.ContainsKey("RANGE_FROM") && vDrillHoleOption["RANGE_FROM"] != null && vDrillHoleOption["RANGE_FROM"].ToString().Trim() != "")
			{
				vRANGE_FROM = Convert.ToDouble(vDrillHoleOption["RANGE_FROM"].ToString().Trim());
			}
			double vRANGE_TO = 9.0;
			if (vDrillHoleOption.ContainsKey("RANGE_FROM") && vDrillHoleOption["RANGE_TO"] != null && vDrillHoleOption["RANGE_TO"].ToString().Trim() != "")
			{
				vRANGE_TO = Convert.ToDouble(vDrillHoleOption["RANGE_TO"].ToString().Trim());
			}
			double vDG_ADJ_PTH = 0.0;
			if (vDrillHoleOption.ContainsKey("DG_ADJ_PTH") && vDrillHoleOption["DG_ADJ_PTH"] != null && vDrillHoleOption["DG_ADJ_PTH"].ToString().Trim() != "")
			{
				vDG_ADJ_PTH = Convert.ToDouble(vDrillHoleOption["DG_ADJ_PTH"].ToString().Trim());
			}
			double vDG_ADJ_NPTH = 0.0;
			if (vDrillHoleOption.ContainsKey("DG_ADJ_NPTH") && vDrillHoleOption["DG_ADJ_NPTH"] != null && vDrillHoleOption["DG_ADJ_NPTH"].ToString().Trim() != "")
			{
				vDG_ADJ_NPTH = Convert.ToDouble(vDrillHoleOption["DG_ADJ_NPTH"].ToString().Trim());
			}
			string vDG_TOLERANCE_PTH = "";
			if (vDrillHoleOption.ContainsKey("DG_TOLERANCE_PTH") && vDrillHoleOption["DG_TOLERANCE_PTH"] != null && vDrillHoleOption["DG_TOLERANCE_PTH"].ToString().Trim() != "")
			{
				vDG_TOLERANCE_PTH = vDrillHoleOption["DG_TOLERANCE_PTH"].ToString().Trim();
			}
			string vDG_TOLERANCE_NPTH = "";
			if (vDrillHoleOption.ContainsKey("DG_TOLERANCE_NPTH") && vDrillHoleOption["DG_TOLERANCE_NPTH"] != null && vDrillHoleOption["DG_TOLERANCE_NPTH"].ToString().Trim() != "")
			{
				vDG_TOLERANCE_NPTH = vDrillHoleOption["DG_TOLERANCE_NPTH"].ToString().Trim();
			}
			int i = TempTable.Rows.Count;
			for (int j = 0; j < i; j++)
			{
				if (TempTable.Rows[j]["DRILL_DIA_DISP"] != DBNull.Value && TempTable.Rows[j]["DRILL_DIA_DISP"].ToString().Trim() != "" && this.IsNumberic(TempTable.Rows[j]["DRILL_DIA_DISP"].ToString()))
				{
					double tmpval = Convert.ToDouble(TempTable.Rows[j]["DRILL_DIA_DISP"].ToString());
					if (vPTHNULL_NOTOPERATE == 0 || (TempTable.Rows[j]["PTH"] != DBNull.Value && TempTable.Rows[j]["PTH"].ToString().Trim() != ""))
					{
						if (TempTable.Rows[j]["PTH"] != DBNull.Value && (TempTable.Rows[j]["PTH"].ToString() == vPTH || TempTable.Rows[j]["PTH"].ToString() == "Y" || TempTable.Rows[j]["PTH"].ToString() == "P"))
						{
							if (tmpval >= vRANGE_FROM && tmpval <= vRANGE_TO)
							{
								if (TempTable.Rows[j]["HOLES_DIA_DISP"] == DBNull.Value || TempTable.Rows[j]["HOLES_DIA_DISP"].ToString().Trim() == "")
								{
									TempTable.Rows[j]["HOLES_DIA_DISP"] = (tmpval - vDG_ADJ_PTH).ToString(U_point);
								}
								if (TempTable.Rows[j]["TOLERANCE"] == DBNull.Value || TempTable.Rows[j]["TOLERANCE"].ToString().Trim() == "")
								{
									TempTable.Rows[j]["TOLERANCE"] = vDG_TOLERANCE_PTH;
								}
							}
						}
						else if (tmpval >= vRANGE_FROM && tmpval <= vRANGE_TO)
						{
							if (TempTable.Rows[j]["HOLES_DIA_DISP"] == DBNull.Value || TempTable.Rows[j]["HOLES_DIA_DISP"].ToString().Trim() == "")
							{
								TempTable.Rows[j]["HOLES_DIA_DISP"] = (tmpval - vDG_ADJ_NPTH).ToString(U_point);
							}
							if (TempTable.Rows[j]["TOLERANCE"] == DBNull.Value || TempTable.Rows[j]["TOLERANCE"].ToString().Trim() == "")
							{
								TempTable.Rows[j]["TOLERANCE"] = vDG_TOLERANCE_NPTH;
							}
						}
					}
				}
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, TempTable.Rows.Count, "success", JsonConvert.SerializeObject(TempTable)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x06000257 RID: 599 RVA: 0x00052F34 File Offset: 0x00051134
		public ActionResult DrillSelect()
		{
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			JObject jo = (JObject)JsonConvert.DeserializeObject(myJson);
			string strDrillSelectOption = jo["DrillSelectOption"].ToString();
			string strdata29 = jo["data0029"].ToString();
			Dictionary<string, object> vDrillSelectOption = JsonConvert.DeserializeObject<Dictionary<string, object>>(strDrillSelectOption);
			DataTable TempTable = TableHelp.JsonToDataTable(strdata29);
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			string U_point = "#0.00";
			if (vDrillSelectOption.ContainsKey("FORMAT") && vDrillSelectOption["FORMAT"].ToString().Trim() != "")
			{
				switch (Convert.ToInt32(vDrillSelectOption["FORMAT"].ToString()))
				{
				case 0:
					U_point = "#0";
					break;
				case 1:
					U_point = "#0.0";
					break;
				case 2:
					U_point = "#0.00";
					break;
				case 3:
					U_point = "#0.000";
					break;
				case 4:
					U_point = "#0.0000";
					break;
				case 5:
					U_point = "#0.00000";
					break;
				case 6:
					U_point = "#0.000000";
					break;
				}
			}
			string vPTH = "PTH";
			if (vDrillSelectOption.ContainsKey("PTH") && vDrillSelectOption["PTH"] != null && vDrillSelectOption["PTH"].ToString().Trim() != "")
			{
				vPTH = vDrillSelectOption["PTH"].ToString().Trim();
			}
			double vRANGE_FROM = 0.1;
			if (vDrillSelectOption.ContainsKey("RANGE_FROM") && vDrillSelectOption["RANGE_FROM"] != null && vDrillSelectOption["RANGE_FROM"].ToString().Trim() != "")
			{
				vRANGE_FROM = Convert.ToDouble(vDrillSelectOption["RANGE_FROM"].ToString().Trim());
			}
			double vRANGE_TO = 9.0;
			if (vDrillSelectOption.ContainsKey("RANGE_FROM") && vDrillSelectOption["RANGE_TO"] != null && vDrillSelectOption["RANGE_TO"].ToString().Trim() != "")
			{
				vRANGE_TO = Convert.ToDouble(vDrillSelectOption["RANGE_TO"].ToString().Trim());
			}
			double vDG_ADJ_PTH = 0.0;
			if (vDrillSelectOption.ContainsKey("DG_ADJ_PTH") && vDrillSelectOption["DG_ADJ_PTH"] != null && vDrillSelectOption["DG_ADJ_PTH"].ToString().Trim() != "")
			{
				vDG_ADJ_PTH = Convert.ToDouble(vDrillSelectOption["DG_ADJ_PTH"].ToString().Trim());
			}
			if (vDrillSelectOption.ContainsKey("DG_ADJ_NPTH") && vDrillSelectOption["DG_ADJ_NPTH"] != null && vDrillSelectOption["DG_ADJ_NPTH"].ToString().Trim() != "")
			{
				double vDG_ADJ_NPTH = Convert.ToDouble(vDrillSelectOption["DG_ADJ_NPTH"].ToString().Trim());
			}
			if (vDrillSelectOption.ContainsKey("DG_TOLERANCE_PTH") && vDrillSelectOption["DG_TOLERANCE_PTH"] != null && vDrillSelectOption["DG_TOLERANCE_PTH"].ToString().Trim() != "")
			{
				string vDG_TOLERANCE_PTH = vDrillSelectOption["DG_TOLERANCE_PTH"].ToString().Trim();
			}
			string vDG_TOLERANCE_NPTH = "";
			if (vDrillSelectOption.ContainsKey("DG_TOLERANCE_NPTH") && vDrillSelectOption["DG_TOLERANCE_NPTH"] != null && vDrillSelectOption["DG_TOLERANCE_NPTH"].ToString().Trim() != "")
			{
				vDG_TOLERANCE_NPTH = vDrillSelectOption["DG_TOLERANCE_NPTH"].ToString().Trim();
			}
			double tmpminqty = 100.0;
			int i = TempTable.Rows.Count;
			for (int j = 0; j < i; j++)
			{
				if (TempTable.Rows[j]["HOLES_DIA_DISP"] != DBNull.Value && TempTable.Rows[j]["HOLES_DIA_DISP"].ToString().Trim() != "")
				{
					if (TempTable.Rows[j]["PTH"] != DBNull.Value && (TempTable.Rows[j]["PTH"].ToString() == vPTH || TempTable.Rows[j]["PTH"].ToString() == "Y" || TempTable.Rows[j]["PTH"].ToString() == "P"))
					{
						double tmpval = Convert.ToDouble(TempTable.Rows[j]["HOLES_DIA_DISP"].ToString());
						if (tmpval >= vRANGE_FROM && tmpval <= vRANGE_TO)
						{
							if (TempTable.Rows[j]["DRILL_DIA_DISP"] == DBNull.Value || TempTable.Rows[j]["DRILL_DIA_DISP"].ToString().Trim() == "")
							{
								double tmpqty2 = (tmpval + Convert.ToDouble(vDrillSelectOption["DG_ADJ_PTH"].ToString())) * 1000.0;
								double tmpqty3 = Math.Ceiling(tmpqty2);
								double tmpqty4 = tmpqty3 % 50.0;
								if (tmpqty4 != 0.0)
								{
									tmpqty3 -= tmpqty4;
									if (tmpqty4 >= 25.0)
									{
										tmpqty3 += 50.0;
									}
								}
								double tmpqty5 = tmpqty3 / 1000.0;
								TempTable.Rows[j]["DRILL_DIA_DISP"] = tmpqty5.ToString(U_point);
								if (tmpminqty > tmpqty5)
								{
									tmpminqty = tmpqty5;
								}
							}
							if (TempTable.Rows[j]["TOLERANCE"] == DBNull.Value || TempTable.Rows[j]["TOLERANCE"].ToString().Trim() == "")
							{
								TempTable.Rows[j]["TOLERANCE"] = vDG_TOLERANCE_NPTH;
							}
						}
					}
					else
					{
						double tmpval2 = Convert.ToDouble(TempTable.Rows[j]["HOLES_DIA_DISP"].ToString());
						if (tmpval2 >= vRANGE_FROM && tmpval2 <= vRANGE_TO)
						{
							if (TempTable.Rows[j]["DRILL_DIA_DISP"] == DBNull.Value || TempTable.Rows[j]["DRILL_DIA_DISP"].ToString().Trim() == "")
							{
								double tmpqty6 = (tmpval2 + vDG_ADJ_PTH) * 1000.0;
								double tmpqty7 = Math.Ceiling(tmpqty6);
								double tmpqty8 = tmpqty7 % 50.0;
								if (tmpqty8 != 0.0)
								{
									tmpqty7 -= tmpqty8;
									if (tmpqty8 >= 25.0)
									{
										tmpqty7 += 50.0;
									}
								}
								double tmpqty9 = tmpqty7 / 1000.0;
								TempTable.Rows[j]["DRILL_DIA_DISP"] = tmpqty9.ToString(U_point);
								if (tmpminqty > tmpqty9)
								{
									tmpminqty = tmpqty9;
								}
							}
							if (TempTable.Rows[j]["TOLERANCE"] == DBNull.Value || TempTable.Rows[j]["TOLERANCE"].ToString().Trim() == "")
							{
								TempTable.Rows[j]["TOLERANCE"] = vDG_TOLERANCE_NPTH;
							}
						}
					}
				}
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, TempTable.Rows.Count, "success", JsonConvert.SerializeObject(TempTable)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x06000258 RID: 600 RVA: 0x000537C0 File Offset: 0x000519C0
		public ActionResult DRILL_CHECK()
		{
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			string vPTH = "PTH";
			string sql1192 = "select top 1  data1192.DRILL_PTH,data1192.DRILL_NPTH from data1192(nolock)";
			DataTable vData1192 = DBHelper.ExecuteDataTable(sql1192, new SqlParameter[0]);
			if (vData1192.Rows.Count > 0)
			{
				if (vData1192.Rows[0]["DRILL_PTH"] != DBNull.Value && vData1192.Rows[0]["DRILL_PTH"].ToString().Trim() != "")
				{
					vPTH = vData1192.Rows[0]["DRILL_PTH"].ToString().Trim();
				}
				if (vData1192.Rows[0]["DRILL_NPTH"] != DBNull.Value && vData1192.Rows[0]["DRILL_NPTH"].ToString().Trim() != "")
				{
					string vNPTH = vData1192.Rows[0]["DRILL_NPTH"].ToString().Trim();
				}
			}
			DataTable TempTable = TableHelp.JsonToDataTable(myJson);
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			double vDG_ADJ_PTH = 0.0;
			double vDG_ADJ_NPTH = 0.0;
			string sql1193 = string.Format(" SELECT RKEY, EST_SCRAP1_LMT, EST_SCRAP_LTM, PROD_CODE, PRODUCT_NAME,\r\n       isnull(DG_ADJ_PTH,0) as DG_ADJ_PTH, isnull(DG_ADJ_NPTH,0) as DG_ADJ_NPTH, IES_FLOW_PTR, ANALYSIS_CODE2,\r\n\t   isnull(DG_TOLERANCE_PTH,'') as DG_TOLERANCE_PTH, isnull(DG_TOLERANCE_NPTH,'') as DG_TOLERANCE_NPTH\r\n                                  FROM DATA0008(nolock) where RKEY IN (SELECT PROD_CODE_PTR FROM DATA0025(NOLOCK) WHERE RKEY={0}) ", vD25RKEY);
			DataTable vData1193 = DBHelper.ExecuteDataTable(sql1193, new SqlParameter[0]);
			vData1193.PrimaryKey = new DataColumn[]
			{
				vData1193.Columns["RKEY"]
			};
			if (vData1193.Rows.Count > 0)
			{
				if (vData1193.Rows[0]["DG_ADJ_PTH"] != DBNull.Value)
				{
					vDG_ADJ_PTH = Convert.ToDouble(vData1193.Rows[0]["DG_ADJ_PTH"].ToString());
				}
				if (vData1193.Rows[0]["DG_ADJ_NPTH"] != DBNull.Value)
				{
					vDG_ADJ_NPTH = Convert.ToDouble(vData1193.Rows[0]["DG_ADJ_NPTH"].ToString());
				}
			}
			int i = TempTable.Rows.Count;
			for (int j = 0; j < i; j++)
			{
				if (TempTable.Rows[j]["HOLES_DIA_DISP"] != DBNull.Value && TempTable.Rows[j]["HOLES_DIA_DISP"].ToString().Trim() != "")
				{
					if (TempTable.Rows[j]["PTH"] != DBNull.Value && (TempTable.Rows[j]["PTH"].ToString() == vPTH || TempTable.Rows[j]["PTH"].ToString() == "Y" || TempTable.Rows[j]["PTH"].ToString() == "P" || TempTable.Rows[j]["PTH"].ToString() == "PTH"))
					{
						if (TempTable.Rows[j]["DRILL_DIA_DISP"] != DBNull.Value && TempTable.Rows[j]["DRILL_DIA_DISP"].ToString().Trim() != "" && TempTable.Rows[j]["TOLERANCE"] != DBNull.Value && TempTable.Rows[j]["TOLERANCE"].ToString().Trim() != "")
						{
							string TmpStr = TempTable.Rows[j]["TOLERANCE"].ToString().Trim();
							double wqty = Convert.ToDouble(TmpStr.Substring(1, TmpStr.IndexOf("/") - 1));
							double wqty2 = Convert.ToDouble(TmpStr.Substring(TmpStr.IndexOf("/") + 2, TmpStr.Length - TmpStr.IndexOf("/") - 2));
							double tmpqty = Convert.ToDouble(TempTable.Rows[j]["HOLES_DIA_DISP"].ToString().Trim()) + vDG_ADJ_PTH;
							double tmpqty2 = tmpqty + wqty;
							double tmpqty3 = tmpqty - wqty2;
							double tmpqty4 = Convert.ToDouble(TempTable.Rows[j]["DRILL_DIA_DISP"].ToString().Trim());
							if (tmpqty4 > tmpqty2 || tmpqty4 < tmpqty3)
							{
								builder.Append(LangHelper.GetString("第") + j.ToString() + LangHelper.GetString("行钻咀超出允许公差范围"));
							}
						}
					}
					else if (TempTable.Rows[j]["DRILL_DIA_DISP"] != DBNull.Value && TempTable.Rows[j]["DRILL_DIA_DISP"].ToString().Trim() != "" && TempTable.Rows[j]["TOLERANCE"] != DBNull.Value && TempTable.Rows[j]["TOLERANCE"].ToString().Trim() != "")
					{
						string TmpStr = TempTable.Rows[j]["TOLERANCE"].ToString().Trim();
						double wqty = Convert.ToDouble(TmpStr.Substring(1, TmpStr.IndexOf("/") - 1));
						double wqty2 = Convert.ToDouble(TmpStr.Substring(TmpStr.IndexOf("/") + 2, TmpStr.Length - TmpStr.IndexOf("/") - 2));
						double tmpqty = Convert.ToDouble(TempTable.Rows[j]["HOLES_DIA_DISP"].ToString().Trim()) + vDG_ADJ_NPTH;
						double tmpqty2 = tmpqty + wqty;
						double tmpqty3 = tmpqty - wqty2;
						double tmpqty4 = Convert.ToDouble(TempTable.Rows[j]["DRILL_DIA_DISP"].ToString().Trim());
						if (tmpqty4 > tmpqty2 || tmpqty4 < tmpqty3)
						{
							builder.Append(LangHelper.GetString("第") + j.ToString() + LangHelper.GetString("行钻咀超出允许公差范围"));
						}
					}
				}
			}
			if (builder.ToString().Trim() == "")
			{
				builder.Append(LangHelper.GetString("检查完毕ok"));
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, 1, builder.ToString(), null), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x06000259 RID: 601 RVA: 0x00053F54 File Offset: 0x00052154
		public ActionResult DrillSave()
		{
			int vGroup_mode_flag = Convert.ToInt32(base.Request.QueryString["vGroup_mode_flag"]);
			int vFLOW_WHSE_PTR = Convert.ToInt32(base.Request.QueryString["vFLOW_WHSE_PTR"]);
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			int vD50RKEY = Convert.ToInt32(base.Request.QueryString["D50RKEY"]);
			int iaddmodi = Convert.ToInt32(base.Request.QueryString["iaddmodi"]);
			int vD05RKEY = Convert.ToInt32(base.Request.QueryString["D05RKEY"]);
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			JObject jo = (JObject)JsonConvert.DeserializeObject(myJson);
			string strdata229 = jo["data0229"].ToString();
			string strdata230 = jo["data0230"].ToString();
			string strdata231 = jo["data002900"].ToString();
			string strdata232 = jo["data002901"].ToString();
			string strdata233 = jo["data002902"].ToString();
			string strdata234 = jo["data002903"].ToString();
			string strdata235 = jo["data002904"].ToString();
			string strdata236 = jo["data002905"].ToString();
			string strdata237 = jo["data002906"].ToString();
			string strdata238 = jo["data002907"].ToString();
			string strdata239 = jo["data002908"].ToString();
			string strdata240 = jo["data002909"].ToString();
			string strdata241 = jo["data002910"].ToString();
			string strdata242 = jo["data002911"].ToString();
			string strdata243 = jo["data002912"].ToString();
			string strdata244 = jo["data002913"].ToString();
			string strdata245 = jo["data002914"].ToString();
			string strdata246 = jo["data002915"].ToString();
			DataTable vdata229 = TableHelp.JsonToDataTable2(strdata229);
			DataTable vdata230 = TableHelp.JsonToDataTable2(strdata230);
			DataTable vdata231 = TableHelp.JsonToDataTable2(strdata231);
			DataTable vdata232 = TableHelp.JsonToDataTable2(strdata232);
			DataTable vdata233 = TableHelp.JsonToDataTable2(strdata233);
			DataTable vdata234 = TableHelp.JsonToDataTable2(strdata234);
			DataTable vdata235 = TableHelp.JsonToDataTable2(strdata235);
			DataTable vdata236 = TableHelp.JsonToDataTable2(strdata236);
			DataTable vdata237 = TableHelp.JsonToDataTable2(strdata237);
			DataTable vdata238 = TableHelp.JsonToDataTable2(strdata238);
			DataTable vdata239 = TableHelp.JsonToDataTable2(strdata239);
			DataTable vdata240 = TableHelp.JsonToDataTable2(strdata240);
			DataTable vdata241 = TableHelp.JsonToDataTable2(strdata241);
			DataTable vdata242 = TableHelp.JsonToDataTable2(strdata242);
			DataTable vdata243 = TableHelp.JsonToDataTable2(strdata243);
			DataTable vdata244 = TableHelp.JsonToDataTable2(strdata244);
			DataTable vdata245 = TableHelp.JsonToDataTable2(strdata245);
			DataTable vdata246 = TableHelp.JsonToDataTable2(strdata246);
			Dictionary<string, object> data25 = JsonConvert.DeserializeObject<Dictionary<string, object>>(jo["data0025"].ToString());
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			if (ihave == 0)
			{
				string sql1195 = "SELECT TOP 1 DATA1195.TABSHT_DRILL1_VFLAG,DATA1195.TABSHT_DRILL2_VFLAG,DATA1195.TABSHT_DRILL3_VFLAG,DATA1195.TABSHT_DRILL4_VFLAG\r\n,DATA1195.TABSHT_DRILL5_VFLAG,DATA1195.TABSHT_DRILL6_VFLAG,DATA1195.TABSHT_DRILL7_VFLAG,DATA1195.TABSHT_DRILL8_VFLAG\r\n,DATA1195.TABSHT_DRILL9_VFLAG,DATA1195.TABSHT_DRILL10_VFLAG,DATA1195.TABSHT_DRILL11_VFLAG,DATA1195.TABSHT_DRILL12_VFLAG\r\n,DATA1195.TABSHT_DRILL13_VFLAG,DATA1195.TABSHT_DRILL14_VFLAG,DATA1195.TABSHT_DRILL15_VFLAG,DATA1195.TABSHT_DRILL16_VFLAG\r\n,DATA1195.TABSHT_DRILL1,DATA1195.TABSHT_DRILL2,DATA1195.TABSHT_DRILL3,DATA1195.TABSHT_DRILL4\r\n,DATA1195.TABSHT_DRILL5,DATA1195.TABSHT_DRILL6,DATA1195.TABSHT_DRILL7,DATA1195.TABSHT_DRILL8\r\n,DATA1195.TABSHT_DRILL9,DATA1195.TABSHT_DRILL10,DATA1195.TABSHT_DRILL11,DATA1195.TABSHT_DRILL12\r\n,DATA1195.TABSHT_DRILL13,DATA1195.TABSHT_DRILL14,DATA1195.TABSHT_DRILL15,DATA1195.TABSHT_DRILL16\r\n,DATA1195.drill_load_sum\r\nFROM DATA1195(NOLOCK)";
				DataTable vdata247 = DBHelper.ExecuteDataTable(sql1195, new SqlParameter[0]);
				SqlTransaction myTrans = DBHelper.GetTransaction();
				try
				{
					string sqlitem = "";
					double vZmin = 0.0;
					if (data25.ContainsKey("ZMIN") && data25["ZMIN"] != null && data25["ZMIN"].ToString().Trim() != "")
					{
						vZmin = Convert.ToDouble(data25["ZMIN"].ToString());
					}
					if (vZmin > 0.0)
					{
						sqlitem += string.Format(" SET ZMIN={0}", vZmin);
					}
					else
					{
						sqlitem += " SET ZMIN=ZMIN";
					}
					int vZcount = 0;
					if (data25.ContainsKey("ZCOUNT") && data25["ZCOUNT"] != null && data25["ZCOUNT"].ToString().Trim() != "")
					{
						vZcount = Convert.ToInt32(data25["ZCOUNT"].ToString());
					}
					if (vZcount > 0)
					{
						sqlitem += string.Format(" , ZCOUNT={0}", vZcount);
					}
					for (int i = 0; i < 16; i++)
					{
						if (Convert.ToBoolean(vdata247.Rows[0]["TABSHT_DRILL" + (i + 1).ToString() + "_VFLAG"].ToString()))
						{
							double vZMIN = 0.0;
							if (data25.ContainsKey("ZMIN" + (i + 1).ToString()) && data25["ZMIN" + (i + 1).ToString()] != null && data25["ZMIN" + (i + 1).ToString()].ToString().Trim() != "")
							{
								vZMIN = Convert.ToDouble(data25["ZMIN" + (i + 1).ToString()].ToString());
							}
							sqlitem += string.Format(",ZMIN{0}={1}", i + 1, vZMIN);
							int vZCOUNT = 0;
							if (data25.ContainsKey("ZCOUNT" + (i + 1).ToString()) && data25["ZCOUNT" + (i + 1).ToString()] != null && data25["ZCOUNT" + (i + 1).ToString()].ToString().Trim() != "")
							{
								vZCOUNT = Convert.ToInt32(data25["ZCOUNT" + (i + 1).ToString()].ToString());
							}
							sqlitem += string.Format(",ZCOUNT{0}={1}", i + 1, vZCOUNT);
							int vSLOTDRILLCOUNT = 0;
							if (data25.ContainsKey("SLOTDRILLCOUNT" + (i + 1).ToString()) && data25["SLOTDRILLCOUNT" + (i + 1).ToString()] != null && data25["SLOTDRILLCOUNT" + (i + 1).ToString()].ToString().Trim() != "")
							{
								vSLOTDRILLCOUNT = Convert.ToInt32(data25["SLOTDRILLCOUNT" + (i + 1).ToString()].ToString());
							}
							sqlitem += string.Format(",SLOTDRILLCOUNT{0}={1}", i + 1, vSLOTDRILLCOUNT);
							string vZTITLE = "";
							if (data25.ContainsKey("ZTITLE" + (i + 1).ToString()) && data25["ZTITLE" + (i + 1).ToString()] != null && data25["ZTITLE" + (i + 1).ToString()].ToString().Trim() != "")
							{
								vZTITLE = data25["ZTITLE" + (i + 1).ToString()].ToString();
							}
							sqlitem += string.Format(",ZTITLE{0}='{1}'", i + 1, vZTITLE);
							string vZFILE = "";
							if (data25.ContainsKey("ZFILE" + (i + 1).ToString()) && data25["ZFILE" + (i + 1).ToString()] != null && data25["ZFILE" + (i + 1).ToString()].ToString().Trim() != "")
							{
								vZFILE = data25["ZFILE" + (i + 1).ToString()].ToString();
							}
							sqlitem += string.Format(",ZFILE{0}='{1}'", i + 1, vZFILE);
						}
					}
					string sql1196 = " UPDATE DATA0025 " + sqlitem + string.Format(" WHERE RKEY= {0}", vD25RKEY);
					DBHelper.ExecuteNonQuery(myTrans, sql1196, new SqlParameter[0]);
					string sql1197 = " UPDATE DATA8025 " + sqlitem + string.Format(" WHERE D0025_PTR= {0} AND isnull(FLOW_WHSE_PTR,0)={1}", vD25RKEY, vFLOW_WHSE_PTR);
					DBHelper.ExecuteNonQuery(myTrans, sql1197, new SqlParameter[0]);
					string sql229del = "delete from data0229 where SOURCE_PTR=@RKEY25 AND FLOW_WHSE_PTR=@FLOW_WHSE_PTR";
					SqlParameter[] p229del = new SqlParameter[2];
					p229del[0] = new SqlParameter("@RKEY25", SqlDbType.Int);
					p229del[0].Value = vD25RKEY;
					p229del[1] = new SqlParameter("@FLOW_WHSE_PTR", SqlDbType.Int);
					if (vGroup_mode_flag == 1)
					{
						p229del[1].Value = vFLOW_WHSE_PTR;
					}
					else
					{
						p229del[1].Value = 0;
					}
					DBHelper.ExecuteNonQuery(myTrans, sql229del, p229del);
					string sql1198 = "SELECT DATA0229.RKEY,DATA0229.SEQ_NO,DATA0229.SOURCE_PTR,DATA0229.DRILL_DIA,DATA0229.BASE_DRILL_DIA,\r\nDATA0229.AL_DRILL_DIA,DATA0229.BKUP_DRILL_DIA,DATA0229.UNIT,DATA0229.REMARK,DATA0229.DRILL_DIA2,\r\nDATA0229.BASE_DRILL_DIA2,DATA0229.AL_DRILL_DIA2,DATA0229.BKUP_DRILL_DIA2,DATA0229.UNIT2,DATA0229.REMARK2,\r\nDATA0229.FLOW_WHSE_PTR,DATA0229.MARK,DATA0229.MARK2\r\nFROM DATA0229\r\nWHERE DATA0229.RKEY=0";
					SqlDataAdapter sda229 = new SqlDataAdapter();
					DataSet vds229 = DBHelper.ExecuteDataSet(myTrans, sda229, sql1198, new SqlParameter[0]);
					vds229.Tables[0].PrimaryKey = new DataColumn[]
					{
						vds229.Tables[0].Columns["RKEY"]
					};
					int j = vdata229.Rows.Count;
					if (j > 0)
					{
						int vLINE = 0;
						for (int k = 0; k < j; k++)
						{
							vLINE++;
							DataRow dr229 = vds229.Tables[0].NewRow();
							dr229["RKEY"] = k;
							dr229["SOURCE_PTR"] = vdata229;
							dr229["FLOW_WHSE_PTR"] = vFLOW_WHSE_PTR;
							dr229["SEQ_NO"] = vLINE;
							dr229["DRILL_DIA"] = vdata229.Rows[k]["DRILL_DIA"];
							dr229["MARK"] = vdata229.Rows[k]["MARK"];
							dr229["BASE_DRILL_DIA"] = vdata229.Rows[k]["BASE_DRILL_DIA"];
							dr229["AL_DRILL_DIA"] = vdata229.Rows[k]["AL_DRILL_DIA"];
							dr229["BKUP_DRILL_DIA"] = vdata229.Rows[k]["BKUP_DRILL_DIA"];
							dr229["UNIT"] = vdata229.Rows[k]["UNIT"];
							dr229["REMARK"] = vdata229.Rows[k]["REMARK"];
							dr229["DRILL_DIA2"] = vdata229.Rows[k]["DRILL_DIA2"];
							dr229["MARK2"] = vdata229.Rows[k]["MARK2"];
							dr229["BASE_DRILL_DIA2"] = vdata229.Rows[k]["BASE_DRILL_DIA2"];
							dr229["AL_DRILL_DIA2"] = vdata229.Rows[k]["AL_DRILL_DIA2"];
							dr229["BKUP_DRILL_DIA2"] = vdata229.Rows[k]["BKUP_DRILL_DIA2"];
							dr229["UNIT2"] = vdata229.Rows[k]["UNIT2"];
							dr229["REMARK2"] = vdata229.Rows[k]["REMARK2"];
							vds229.Tables[0].Rows.Add(dr229);
						}
					}
					SqlCommandBuilder scb229 = new SqlCommandBuilder(sda229);
					sda229.Update(vds229, vds229.Tables[0].TableName);
					string sql230del = "delete from data0230 where SOURCE_PTR=@RKEY25 AND FLOW_WHSE_PTR=@FLOW_WHSE_PTR";
					SqlParameter[] p230del = new SqlParameter[2];
					p230del[0] = new SqlParameter("@RKEY25", SqlDbType.Int);
					p230del[0].Value = vD25RKEY;
					p230del[1] = new SqlParameter("@FLOW_WHSE_PTR", SqlDbType.Int);
					p230del[1].Value = vFLOW_WHSE_PTR;
					DBHelper.ExecuteNonQuery(myTrans, sql230del, p230del);
					string sql1199 = "SELECT DATA0230.RKEY,DATA0230.SOURCE_PTR,DATA0230.SEQ_NO,DATA0230.ROUTER_DIA,DATA0230.ROUTE_LNGTH,\r\nDATA0230.REMARK,DATA0230.SCH_NO,DATA0230.MILL_FLAG,DATA0230.FLOW_WHSE_PTR,DATA0230.FLOW_NO,DATA0230.ROUTE_LNGTH_B\r\nFROM DATA0230\r\nWHERE DATA0230.RKEY=0";
					SqlDataAdapter sda230 = new SqlDataAdapter();
					DataSet vds230 = DBHelper.ExecuteDataSet(myTrans, sda230, sql1199, new SqlParameter[0]);
					vds230.Tables[0].PrimaryKey = new DataColumn[]
					{
						vds230.Tables[0].Columns["RKEY"]
					};
					j = vdata230.Rows.Count;
					if (j > 0)
					{
						int vLINE2 = 0;
						for (int l = 0; l < j; l++)
						{
							vLINE2++;
							DataRow dr230 = vds230.Tables[0].NewRow();
							dr230["RKEY"] = l;
							dr230["SOURCE_PTR"] = vD25RKEY;
							dr230["FLOW_WHSE_PTR"] = vFLOW_WHSE_PTR;
							dr230["FLOW_NO"] = 0;
							dr230["SEQ_NO"] = vLINE2;
							dr230["SCH_NO"] = vdata230.Rows[l]["SCH_NO"];
							dr230["ROUTER_DIA"] = vdata230.Rows[l]["ROUTER_DIA"];
							dr230["ROUTE_LNGTH"] = vdata230.Rows[l]["ROUTE_LNGTH"];
							dr230["ROUTE_LNGTH_B"] = vdata230.Rows[l]["ROUTE_LNGTH_B"];
							dr230["MILL_FLAG"] = vdata230.Rows[l]["MILL_FLAG"];
							vds230.Tables[0].Rows.Add(dr230);
						}
					}
					SqlCommandBuilder scb230 = new SqlCommandBuilder(sda230);
					sda230.Update(vds230, vds230.Tables[0].TableName);
					string sqldel = "delete from data0029 where SOURCE_PTR=@RKEY25 AND isnull(FLOW_WHSE_PTR,0)=@FLOW_WHSE_PTR";
					SqlParameter[] pdel = new SqlParameter[2];
					pdel[0] = new SqlParameter("@RKEY25", SqlDbType.Int);
					pdel[0].Value = vD25RKEY;
					pdel[1] = new SqlParameter("@FLOW_WHSE_PTR", SqlDbType.Int);
					if (vGroup_mode_flag == 1)
					{
						pdel[1].Value = vFLOW_WHSE_PTR;
					}
					else
					{
						pdel[1].Value = 0;
					}
					DBHelper.ExecuteNonQuery(myTrans, sqldel, pdel);
					for (int m = 0; m < 16; m++)
					{
						if (Convert.ToBoolean(vdata247.Rows[0]["TABSHT_DRILL" + (m + 1).ToString() + "_VFLAG"].ToString()))
						{
							DataTable vdata248 = null;
							switch (m)
							{
							case 0:
								vdata248 = vdata231;
								break;
							case 1:
								vdata248 = vdata232;
								break;
							case 2:
								vdata248 = vdata233;
								break;
							case 3:
								vdata248 = vdata234;
								break;
							case 4:
								vdata248 = vdata235;
								break;
							case 5:
								vdata248 = vdata236;
								break;
							case 6:
								vdata248 = vdata237;
								break;
							case 7:
								vdata248 = vdata238;
								break;
							case 8:
								vdata248 = vdata239;
								break;
							case 9:
								vdata248 = vdata240;
								break;
							case 10:
								vdata248 = vdata241;
								break;
							case 11:
								vdata248 = vdata242;
								break;
							case 12:
								vdata248 = vdata243;
								break;
							case 13:
								vdata248 = vdata244;
								break;
							case 14:
								vdata248 = vdata245;
								break;
							case 15:
								vdata248 = vdata246;
								break;
							}
							string sql1200 = "SELECT DATA0029.RKEY,DATA0029.SOURCE_PTR,DATA0029.UNIT,DATA0029.MARK,DATA0029.HOLES_DIA,DATA0029.TOLERANCE,\r\nDATA0029.DRILL_DIA,DATA0029.PANEL_A,DATA0029.PANEL_B,DATA0029.PTH,DATA0029.REMARK,DATA0029.SEQ_NO,DATA0029.SEQ_NR,\r\nDATA0029.PANEL_C,DATA0029.PANEL_D,DATA0029.FLOW_NO,DATA0029.SLOT_LENGTH,DATA0029.SPEC_MARK,DATA0029.SLOT_HQTY,DATA0029.GRIND_TIMES,\r\nDATA0029.SLOT_HDIA,DATA0029.SETS,DATA0029.PAD,DATA0029.GROUP_DESC,DATA0029.IMAGE_MARK,DATA0029.FLOW_WHSE_PTR,DATA0029.SLOT_HQTY2,DATA0029.COUNT_FLAG\r\nFROM DATA0029\r\nWHERE DATA0029.RKEY=0";
							SqlDataAdapter sda231 = new SqlDataAdapter();
							DataSet vds231 = DBHelper.ExecuteDataSet(myTrans, sda231, sql1200, new SqlParameter[0]);
							vds231.Tables[0].PrimaryKey = new DataColumn[]
							{
								vds231.Tables[0].Columns["RKEY"]
							};
							j = vdata248.Rows.Count;
							if (j > 0)
							{
								int vLINE3 = 0;
								for (int n = 0; n < j; n++)
								{
									vLINE3++;
									DataRow dr231 = vds231.Tables[0].NewRow();
									dr231["RKEY"] = n;
									dr231["SOURCE_PTR"] = vD25RKEY;
									dr231["FLOW_WHSE_PTR"] = vFLOW_WHSE_PTR;
									dr231["FLOW_NO"] = m;
									dr231["SEQ_NO"] = vLINE3;
									if (vdata248.Columns.Contains("SEQ_NR"))
									{
										if (vdata248.Rows[n]["SEQ_NR"] != DBNull.Value)
										{
											dr231["SEQ_NR"] = vdata248.Rows[n]["SEQ_NR"];
										}
										else
										{
											dr231["SEQ_NR"] = "";
										}
									}
									else
									{
										dr231["SEQ_NR"] = "";
									}
									if (vdata248.Columns.Contains("MARK"))
									{
										if (vdata248.Rows[n]["MARK"] != DBNull.Value)
										{
											dr231["MARK"] = vdata248.Rows[n]["MARK"];
										}
										else
										{
											dr231["MARK"] = "";
										}
									}
									else
									{
										dr231["MARK"] = "";
									}
									if (vdata248.Columns.Contains("HOLES_DIA_DISP"))
									{
										if (vdata248.Rows[n]["HOLES_DIA_DISP"] != DBNull.Value)
										{
											dr231["HOLES_DIA"] = vdata248.Rows[n]["HOLES_DIA_DISP"];
										}
										else
										{
											dr231["HOLES_DIA"] = "";
										}
									}
									else
									{
										dr231["HOLES_DIA"] = "";
									}
									if (vdata248.Columns.Contains("PTH"))
									{
										if (vdata248.Rows[n]["PTH"] != DBNull.Value)
										{
											dr231["PTH"] = vdata248.Rows[n]["PTH"];
										}
										else
										{
											dr231["PTH"] = "";
										}
									}
									else
									{
										dr231["PTH"] = "";
									}
									if (vdata248.Columns.Contains("TOLERANCE"))
									{
										if (vdata248.Rows[n]["TOLERANCE"] != DBNull.Value)
										{
											dr231["TOLERANCE"] = vdata248.Rows[n]["TOLERANCE"];
										}
										else
										{
											dr231["TOLERANCE"] = "";
										}
									}
									else
									{
										dr231["TOLERANCE"] = "";
									}
									if (vdata248.Columns.Contains("DRILL_DIA_DISP"))
									{
										if (vdata248.Rows[n]["DRILL_DIA_DISP"] != DBNull.Value)
										{
											dr231["DRILL_DIA"] = vdata248.Rows[n]["DRILL_DIA_DISP"];
										}
										else
										{
											dr231["DRILL_DIA"] = "";
										}
									}
									else
									{
										dr231["DRILL_DIA"] = "";
									}
									if (vdata248.Columns.Contains("PAD"))
									{
										if (vdata248.Rows[n]["PAD"] != DBNull.Value)
										{
											dr231["PAD"] = vdata248.Rows[n]["PAD"];
										}
										else
										{
											dr231["PAD"] = "";
										}
									}
									else
									{
										dr231["PAD"] = "";
									}
									if (vdata248.Columns.Contains("UNIT"))
									{
										if (vdata248.Rows[n]["UNIT"] != DBNull.Value)
										{
											dr231["UNIT"] = vdata248.Rows[n]["UNIT"];
										}
										else
										{
											dr231["UNIT"] = "";
										}
									}
									else
									{
										dr231["UNIT"] = "";
									}
									if (vdata248.Columns.Contains("SETS"))
									{
										if (vdata248.Rows[n]["SETS"] != DBNull.Value)
										{
											dr231["SETS"] = vdata248.Rows[n]["SETS"];
										}
										else
										{
											dr231["SETS"] = "";
										}
									}
									else
									{
										dr231["SETS"] = "";
									}
									if (vdata248.Columns.Contains("SLOT_LENGTH"))
									{
										if (vdata248.Rows[n]["SLOT_LENGTH"] != DBNull.Value)
										{
											dr231["SLOT_LENGTH"] = vdata248.Rows[n]["SLOT_LENGTH"];
										}
										else
										{
											dr231["SLOT_LENGTH"] = "";
										}
									}
									else
									{
										dr231["SLOT_LENGTH"] = "";
									}
									if (vdata248.Columns.Contains("PANEL_A"))
									{
										if (vdata248.Rows[n]["PANEL_A"] != DBNull.Value)
										{
											dr231["PANEL_A"] = vdata248.Rows[n]["PANEL_A"];
										}
										else
										{
											dr231["PANEL_A"] = "";
										}
									}
									else
									{
										dr231["PANEL_A"] = "";
									}
									if (vdata248.Columns.Contains("PANEL_B"))
									{
										if (vdata248.Rows[n]["PANEL_B"] != DBNull.Value)
										{
											dr231["PANEL_B"] = vdata248.Rows[n]["PANEL_B"];
										}
										else
										{
											dr231["PANEL_B"] = "";
										}
									}
									else
									{
										dr231["PANEL_B"] = "";
									}
									if (vdata248.Columns.Contains("PANEL_C"))
									{
										if (vdata248.Rows[n]["PANEL_C"] != DBNull.Value)
										{
											dr231["PANEL_C"] = vdata248.Rows[n]["PANEL_C"];
										}
										else
										{
											dr231["PANEL_C"] = "";
										}
									}
									else
									{
										dr231["PANEL_C"] = "";
									}
									if (vdata248.Columns.Contains("PANEL_D"))
									{
										if (vdata248.Rows[n]["PANEL_D"] != DBNull.Value)
										{
											dr231["PANEL_D"] = vdata248.Rows[n]["PANEL_D"];
										}
										else
										{
											dr231["PANEL_D"] = "";
										}
									}
									else
									{
										dr231["PANEL_D"] = "";
									}
									if (vdata248.Columns.Contains("SPEC_MARK"))
									{
										if (vdata248.Rows[n]["SPEC_MARK"] != DBNull.Value)
										{
											dr231["SPEC_MARK"] = vdata248.Rows[n]["SPEC_MARK"];
										}
										else
										{
											dr231["SPEC_MARK"] = "";
										}
									}
									else
									{
										dr231["SPEC_MARK"] = "";
									}
									if (vdata248.Columns.Contains("GRIND_TIMES"))
									{
										if (vdata248.Rows[n]["GRIND_TIMES"] != DBNull.Value)
										{
											dr231["GRIND_TIMES"] = vdata248.Rows[n]["GRIND_TIMES"];
										}
										else
										{
											dr231["GRIND_TIMES"] = "";
										}
									}
									else
									{
										dr231["GRIND_TIMES"] = "";
									}
									if (vdata248.Columns.Contains("REMARK"))
									{
										if (vdata248.Rows[n]["REMARK"] != DBNull.Value)
										{
											dr231["REMARK"] = vdata248.Rows[n]["REMARK"];
										}
										else
										{
											dr231["REMARK"] = "";
										}
									}
									else
									{
										dr231["REMARK"] = "";
									}
									if (vdata248.Columns.Contains("SLOT_HQTY"))
									{
										if (vdata248.Rows[n]["SLOT_HQTY"] != DBNull.Value)
										{
											dr231["SLOT_HQTY"] = vdata248.Rows[n]["SLOT_HQTY"];
										}
										else
										{
											dr231["SLOT_HQTY"] = "";
										}
									}
									else
									{
										dr231["SLOT_HQTY"] = "";
									}
									if (vdata248.Columns.Contains("SLOT_HQTY2"))
									{
										if (vdata248.Rows[n]["SLOT_HQTY2"] != DBNull.Value)
										{
											dr231["SLOT_HQTY2"] = vdata248.Rows[n]["SLOT_HQTY2"];
										}
										else
										{
											dr231["SLOT_HQTY2"] = "";
										}
									}
									else
									{
										dr231["SLOT_HQTY2"] = "";
									}
									if (vdata248.Columns.Contains("SLOT_HDIA"))
									{
										if (vdata248.Rows[n]["SLOT_HDIA"] != DBNull.Value)
										{
											dr231["SLOT_HDIA"] = vdata248.Rows[n]["SLOT_HDIA"];
										}
										else
										{
											dr231["SLOT_HDIA"] = "";
										}
									}
									else
									{
										dr231["SLOT_HDIA"] = "";
									}
									int vCOUNT_FLAG = 0;
									if (vdata248.Columns.Contains("COUNT_FLAG") && vdata248.Rows[n]["COUNT_FLAG"] != DBNull.Value)
									{
										vCOUNT_FLAG = Convert.ToInt32(vdata248.Rows[n]["COUNT_FLAG"].ToString());
									}
									dr231["COUNT_FLAG"] = vCOUNT_FLAG;
									vds231.Tables[0].Rows.Add(dr231);
								}
							}
							SqlCommandBuilder scb231 = new SqlCommandBuilder(sda231);
							sda231.Update(vds231, vds231.Tables[0].TableName);
						}
					}
					string sql50uplast = string.Format(" update data0050 set MI_LAST_MODIFIED_BY_PTR={0},MI_LAST_MODIFIED_DATE=getdate() where rkey={1} ", vD05RKEY, vD50RKEY);
					DBHelper.ExecuteNonQuery(myTrans, sql50uplast, new SqlParameter[0]);
					if (vGroup_mode_flag == 1)
					{
						string sql8054uplast = string.Format("update data8054 set MI_LAST_MODIFIED_BY_PTR={0},MI_LAST_MODIFIED_DATE=getdate() where CUST_PART_PTR={1} and FLOW_WHSE_PTR={2}", vD05RKEY, vD50RKEY, vFLOW_WHSE_PTR);
						DBHelper.ExecuteNonQuery(myTrans, sql8054uplast, new SqlParameter[0]);
					}
					myTrans.Commit();
				}
				catch (Exception ex)
				{
					myTrans.Rollback();
					ihave = 1;
					builder.Append(LangHelper.GetString("保存失败,数据回滚!错误:") + ex.Message);
				}
				finally
				{
					if (myTrans.Connection != null)
					{
						myTrans.Rollback();
					}
				}
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, 1, LangHelper.GetString("保存成功完成"), null), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x0600025A RID: 602 RVA: 0x00055C20 File Offset: 0x00053E20
		public ActionResult Check()
		{
			int vGroup_mode_flag = Convert.ToInt32(base.Request.QueryString["vGroup_mode_flag"]);
			int vFLOW_WHSE_PTR = Convert.ToInt32(base.Request.QueryString["vFLOW_WHSE_PTR"]);
			int vD25RKEY = Convert.ToInt32(base.Request.QueryString["D25RKEY"]);
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int nohave = 0;
			int vset_pcs = 0;
			int vpnl_pcs = 0;
			string sql25 = string.Format(" SELECT  DATA0047.RKEY,DATA0047.PARAMETER_VALUE,DATA0278.SPEC_RKEY\r\n                  FROM data0047(NOLOCK)\r\n                  INNER JOIN DATA0278(NOLOCK) ON DATA0047.PARAMETER_PTR=DATA0278.RKEY\r\n                  where  data0047.source_pointer={0} ", vD25RKEY);
			if (vGroup_mode_flag == 1)
			{
				sql25 += string.Format(" AND isnull(data0047.FLOW_WHSE_PTR,0)={0}", vFLOW_WHSE_PTR);
			}
			DataTable vdata47 = DBHelper.ExecuteDataTable(sql25, new SqlParameter[0]);
			if (vdata47.Rows.Count > 0)
			{
				DataRow[] dr = vdata47.Select("SPEC_RKEY='B'");
				if (dr.Length != 0)
				{
					if (dr[0]["PARAMETER_VALUE"] != DBNull.Value)
					{
						vset_pcs = Convert.ToInt32(dr[0]["PARAMETER_VALUE"].ToString());
					}
					else
					{
						nohave = 1;
					}
				}
				else
				{
					nohave = 1;
				}
				DataRow[] dr2 = vdata47.Select("SPEC_RKEY='C'");
				if (dr2.Length != 0)
				{
					if (dr2[0]["PARAMETER_VALUE"] != DBNull.Value)
					{
						vpnl_pcs = Convert.ToInt32(dr2[0]["PARAMETER_VALUE"].ToString());
					}
					else
					{
						nohave = 1;
					}
				}
				else
				{
					nohave = 1;
				}
			}
			else
			{
				nohave = 1;
			}
			if (nohave != 0)
			{
				return base.Json(new ReturnJsonInfo(400, 0, LangHelper.GetString("统计单位没有或不正确"), null), 0);
			}
			if (vset_pcs == 0 || vpnl_pcs == 0)
			{
				return base.Json(new ReturnJsonInfo(400, 0, LangHelper.GetString("有统计单位为0"), null), 0);
			}
			var Data = new
			{
				set_pcs = vset_pcs,
				pnl_pcs = vpnl_pcs
			};
			return base.Json(new ReturnJsonInfo(100, 1, builder.ToString(), Data), 0);
		}

		// Token: 0x0600025B RID: 603 RVA: 0x00055DF0 File Offset: 0x00053FF0
		public ActionResult UploadSave()
		{
			int vTTYPE = Convert.ToInt32(base.Request.QueryString["TTYPE"]);
			int vset_pcs = Convert.ToInt32(base.Request.QueryString["set_pcs"]);
			int vpnl_pcs = Convert.ToInt32(base.Request.QueryString["pnl_pcs"]);
			int num = base.Request.Files.Count;
			HttpPostedFileBase file = base.Request.Files[0];
			NameValueCollection formData = base.Request.Form;
			string jsonParams = formData["jsonParams"];
			JObject jo = (JObject)JsonConvert.DeserializeObject(jsonParams);
			int ihave = 0;
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			DataTable vtableItems = null;
			if (jo["vtableItems"] != null && jo["vtableItems"].ToString().Length > 4)
			{
				vtableItems = TableHelp.JsonToDataTable(jo["vtableItems"].ToString());
			}
			Dictionary<string, object> vDrillLoadOption = JsonConvert.DeserializeObject<Dictionary<string, object>>(jo["vDrillLoadOption"].ToString());
			Dictionary<string, object> vData25 = JsonConvert.DeserializeObject<Dictionary<string, object>>(jo["vdata0025"].ToString());
			DataTable vData26 = null;
			int ADDFLAG = 0;
			if (vDrillLoadOption.ContainsKey("ADDFLAG") && vDrillLoadOption["ADDFLAG"] != null)
			{
				ADDFLAG = Convert.ToInt32(vDrillLoadOption["ADDFLAG"].ToString());
			}
			if (ADDFLAG == 1)
			{
				if (jo["vdata0029"] != null && jo["vdata0029"].ToString().Length > 4)
				{
					vData26 = TableHelp.JsonToDataTable(jo["vdata0029"].ToString());
					vData26.PrimaryKey = new DataColumn[]
					{
						vData26.Columns["RKEY"]
					};
				}
				else
				{
					ADDFLAG = 0;
				}
			}
			if (file.ContentLength > 0)
			{
				string directoryName = base.Server.MapPath("../../UploadFile");
				if (!Directory.Exists(directoryName))
				{
					Directory.CreateDirectory(directoryName);
				}
				string[] Files = Directory.GetFiles(base.Server.MapPath("../../UploadFile/"), "*");
				if (Files.Length != 0)
				{
					for (int i = 0; i < Files.Length; i++)
					{
						FileInfo objFI = new FileInfo(Files[i].ToString());
						int vhours = DateTime.Now.Subtract(objFI.LastWriteTime).Hours;
						if (vhours > 1)
						{
							File.Delete(Files[i].ToString());
						}
					}
				}
				string title = string.Empty;
				title = Guid.NewGuid().ToString() + "_" + Path.GetFileName(file.FileName);
				string path = "../../UploadFile/" + title;
				string path2 = "/UploadFile/" + title;
				path = HttpContext.Current.Server.MapPath(path);
				if (File.Exists(path))
				{
					File.Delete(path);
				}
				file.SaveAs(path);
				if (ADDFLAG == 0)
				{
					SqlParameter[] p29 = new SqlParameter[2];
					p29[0] = new SqlParameter("@vptr", SqlDbType.Int);
					p29[0].Value = -1;
					p29[1] = new SqlParameter("@vFLOW_WHSE_PTR", SqlDbType.Int);
					p29[1].Value = -1;
					vData26 = DBHelper.ExecuteStoredDataTable("ENGI03V;164", p29);
					vData26.PrimaryKey = new DataColumn[]
					{
						vData26.Columns["RKEY"]
					};
				}
				string sql1192 = "select top 1 data1192.drill_pcs_set_pnl\r\n,data1192.drill_order_by\r\n,data1192.drill_remark_list\r\n,data1192.drillfile_include_pnumber\r\n,DATA1195.drill_load_sum\r\nfrom data1192(nolock),data1195(nolock)";
				DataTable vdata1192 = DBHelper.ExecuteDataTable(sql1192, new SqlParameter[0]);
				int vLoadTtype = 0;
				if (vDrillLoadOption.ContainsKey("ttype") && vDrillLoadOption["ttype"] != null)
				{
					vLoadTtype = Convert.ToInt32(vDrillLoadOption["ttype"].ToString());
				}
				if (vLoadTtype == 0)
				{
					this.DRILL_LOAD_FmDrl(ref vData26, vdata1192, vDrillLoadOption, vset_pcs, vpnl_pcs, path, ref vData25, vTTYPE, ADDFLAG);
				}
				else if (vLoadTtype == 1)
				{
					this.DRILL_LOAD_FmExcel(ref vData26, vtableItems, vdata1192, vDrillLoadOption, vset_pcs, vpnl_pcs, path, ref vData25, vTTYPE, ADDFLAG);
				}
				builder.Append(LangHelper.GetString("导入成功"));
			}
			else
			{
				ihave = 1;
				builder.Append(LangHelper.GetString("文件导入失败"));
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonMo2(100, 1, builder.ToString(), JsonConvert.SerializeObject(vData26), JsonConvert.SerializeObject(vData25)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x0600025C RID: 604 RVA: 0x00056250 File Offset: 0x00054450
		public ActionResult UploadExcelSave()
		{
			int vTTYPE = Convert.ToInt32(base.Request.QueryString["TTYPE"]);
			int vset_pcs = Convert.ToInt32(base.Request.QueryString["set_pcs"]);
			int vpnl_pcs = Convert.ToInt32(base.Request.QueryString["pnl_pcs"]);
			int num = base.Request.Files.Count;
			HttpPostedFileBase file = base.Request.Files[0];
			NameValueCollection formData = base.Request.Form;
			string jsonParams = formData["jsonParams"];
			JObject jo = (JObject)JsonConvert.DeserializeObject(jsonParams);
			int ihave = 0;
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			DataTable dataTable = new DataTable();
			if (file.ContentLength > 0)
			{
				string directoryName = base.Server.MapPath("../../UploadFile");
				if (!Directory.Exists(directoryName))
				{
					Directory.CreateDirectory(directoryName);
				}
				string[] Files = Directory.GetFiles(base.Server.MapPath("../../UploadFile/"), "*");
				if (Files.Length != 0)
				{
					for (int i = 0; i < Files.Length; i++)
					{
						FileInfo objFI = new FileInfo(Files[i].ToString());
						int vhours = DateTime.Now.Subtract(objFI.LastWriteTime).Hours;
						if (vhours > 1)
						{
							File.Delete(Files[i].ToString());
						}
					}
				}
				string title = Guid.NewGuid().ToString() + "_" + file.FileName;
				string path = "../../UploadFile/" + title;
				string path2 = "/UploadFile/" + title;
				path = HttpContext.Current.Server.MapPath(path);
				if (File.Exists(path))
				{
					File.Delete(path);
				}
				file.SaveAs(path);
				FileStream fs = new FileStream(path, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);
				IWorkbook workbook = null;
				if (Path.GetExtension(fs.Name) == ".xls")
				{
					workbook = new HSSFWorkbook(fs);
				}
				else if (Path.GetExtension(fs.Name) == ".xlsx")
				{
					workbook = new XSSFWorkbook(fs);
				}
				ISheet sheet = workbook.GetSheetAt(0);
				int m = sheet.LastRowNum;
				int m2 = 0;
				if (m > 0)
				{
					IRow row0 = sheet.GetRow(0);
					if (row0 != null)
					{
						m2 = row0.Cells.Count<ICell>();
						for (int j2 = 0; j2 < m2; j2++)
						{
							ICell newCell = row0.Cells[j2];
							if (newCell != null && newCell.ToString() != "{}")
							{
								string vtex = this.GetCellValue(newCell);
								dataTable.Columns.Add(vtex.ToString(), Type.GetType("System.String"));
							}
						}
					}
					if (m2 > 0)
					{
						for (int j3 = 1; j3 < sheet.LastRowNum; j3++)
						{
							IRow row = sheet.GetRow(j3);
							if (row != null)
							{
								DataRow drnew = dataTable.NewRow();
								for (int j4 = 0; j4 < m2; j4++)
								{
									ICell newCell2 = row.Cells[j4];
									if (newCell2 != null && newCell2.ToString() != "{}")
									{
										string vtex2 = this.GetCellValue(newCell2);
										drnew[dataTable.Columns[j4]] = vtex2;
									}
								}
								dataTable.Rows.Add(drnew);
							}
						}
					}
				}
				builder.Append(LangHelper.GetString("导入成功"));
			}
			else
			{
				ihave = 1;
				builder.Append(LangHelper.GetString("文件导入失败"));
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonMo2(100, 1, builder.ToString(), JsonConvert.SerializeObject(dataTable), JsonConvert.SerializeObject(dataTable)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x0600025D RID: 605 RVA: 0x00056628 File Offset: 0x00054828
		private void DRILL_LOAD_FmDrl(ref DataTable TempTable, DataTable vdata1192, Dictionary<string, object> vDrillLoadOption, int vset_pcs, int vpnl_pcs, string FilePath, ref Dictionary<string, object> vdata0025, int vTTYPE, int ADDFLAG)
		{
			int vDRILLSLOT_ITEMCOUNT = 0;
			try
			{
				DataTable vdata1193 = DBHelper.ExecuteDataTable("select top 1 DRILLSLOT_ITEMCOUNT from data0192(nolock) ", new SqlParameter[0]);
				if (vdata1193.Rows.Count > 0 && vdata1193.Rows[0]["DRILLSLOT_ITEMCOUNT"] != DBNull.Value)
				{
					vDRILLSLOT_ITEMCOUNT = Convert.ToInt32(vdata1193.Rows[0]["DRILLSLOT_ITEMCOUNT"].ToString());
				}
			}
			catch
			{
			}
			string U_point = "#0";
			switch (Convert.ToInt32(vDrillLoadOption["FORMAT"].ToString()))
			{
			case 0:
				U_point = "#0";
				break;
			case 1:
				U_point = "#0.0";
				break;
			case 2:
				U_point = "#0.00";
				break;
			case 3:
				U_point = "#0.000";
				break;
			case 4:
				U_point = "#0.0000";
				break;
			case 5:
				U_point = "#0.00000";
				break;
			case 6:
				U_point = "#0.000000";
				break;
			}
			ArrayList drList = new ArrayList();
			ArrayList List = new ArrayList();
			ArrayList List2 = new ArrayList();
			ArrayList List3 = new ArrayList();
			ArrayList List4 = new ArrayList();
			try
			{
				StreamReader srReadLine = new StreamReader(FilePath, Encoding.Default);
				using (new StreamReader(base.Request.InputStream))
				{
					srReadLine.BaseStream.Seek(0L, SeekOrigin.Begin);
					goto IL_161;
				}
				IL_14E:
				string vstr = srReadLine.ReadLine();
				drList.Add(vstr);
				IL_161:
				if (srReadLine.Peek() > -1)
				{
					goto IL_14E;
				}
				srReadLine.Close();
				srReadLine = null;
				bool bhave = false;
				if (drList.Count > 0)
				{
					bhave = true;
				}
				bool Binchtomm = false;
				string sql195 = "select top 1 inchtomm  from data0195(nolock)";
				DataTable vdata1194 = DBHelper.ExecuteDataTable(sql195, new SqlParameter[0]);
				if (vdata1194.Rows.Count > 0 && vdata1194.Rows[0]["inchtomm"] != DBNull.Value && Convert.ToInt32(vdata1194.Rows[0]["inchtomm"].ToString()) == 1)
				{
					Binchtomm = true;
				}
				bool hzk = false;
				bool bunit = false;
				int tmpZeroType = 2;
				int i = 0;
				while (bhave)
				{
					string TmpStr = drList[i].ToString();
					if (TmpStr.IndexOf("M48") != 0)
					{
						bunit = true;
					}
					if (bunit && (TmpStr.IndexOf("LZ") != 0 || TmpStr.IndexOf("TZ") != 0 || TmpStr.IndexOf("METRIC") != 0 || TmpStr.IndexOf("INCH") != 0 || TmpStr.IndexOf("MIL") != 0))
					{
						if (TmpStr.IndexOf("LZ") != 0)
						{
							tmpZeroType = 1;
						}
						else if (TmpStr.IndexOf("TZ") != 0)
						{
							tmpZeroType = 2;
						}
						if (TmpStr.IndexOf("METRIC") == 0)
						{
							if (TmpStr.IndexOf("INCH") == 0)
							{
								if (TmpStr.IndexOf("MIL") != 0)
								{
								}
							}
						}
						bunit = false;
					}
					if (hzk && TmpStr.IndexOf("T") != 0)
					{
						bhave = false;
					}
					else if (hzk)
					{
						List.Add(TmpStr.Substring(0, TmpStr.IndexOf("C")));
						TmpStr = TmpStr.Substring(TmpStr.IndexOf("C") + 1 - 1, TmpStr.Length - TmpStr.IndexOf("C"));
						int j = 1;
						while (j < TmpStr.Length && new ArrayList(new char[]
						{
							'0',
							'1',
							'2',
							'3',
							'4',
							'5',
							'6',
							'7',
							'8',
							'9',
							'.'
						}).Contains(TmpStr[j]))
						{
							j++;
						}
						string strkj = TmpStr.Substring(1, j - 1);
						List2.Add(Convert.ToDouble(strkj).ToString(U_point));
					}
					else if (TmpStr.IndexOf("T01C") >= 0)
					{
						hzk = true;
						List.Add(TmpStr.Substring(0, TmpStr.IndexOf("C")));
						TmpStr = TmpStr.Substring(TmpStr.IndexOf("C") + 1 - 1, TmpStr.Length - TmpStr.IndexOf("C"));
						int j = 1;
						while (j < TmpStr.Length && new ArrayList(new char[]
						{
							'0',
							'1',
							'2',
							'3',
							'4',
							'5',
							'6',
							'7',
							'8',
							'9',
							'.'
						}).Contains(TmpStr[j]))
						{
							j++;
						}
						string strkj = TmpStr.Substring(1, j - 1);
						List2.Add(Convert.ToDouble(strkj).ToString(U_point));
					}
					if (i >= drList.Count - 1)
					{
						bhave = false;
					}
					i++;
				}
				int vtmpi = i;
				for (int k = 0; k < List.Count; k++)
				{
					int l = 0;
					string SLOTremark = "";
					double xhcount = 0.0;
					bool xhave = false;
					double douCaliber = Convert.ToDouble(List2[k].ToString());
					double douSeparated = 2.0 * Math.Sqrt(Math.Pow(douCaliber / 2.0, 2.0) - Math.Pow(douCaliber / 2.0 - 0.0127, 2.0));
					string strtype = List[k].ToString();
					string strremark = "";
					bool bend = false;
					i = vtmpi;
					while (i <= drList.Count - 1 && !bend)
					{
						string TmpStr = drList[i].ToString();
						if (TmpStr.IndexOf(List[k].ToString()) >= 0)
						{
							l = 0;
							SLOTremark = "";
							strtype = List[k].ToString();
						}
						else if (k < List.Count - 1 && TmpStr.IndexOf(List[k + 1].ToString()) >= 0)
						{
							List3.Add(l.ToString());
							List4.Add(SLOTremark);
							l = 0;
							SLOTremark = "";
							bend = true;
						}
						else
						{
							if (TmpStr.IndexOf("M25") >= 0)
							{
								xhcount = 0.0;
								xhave = true;
							}
							if (TmpStr.IndexOf("M02X") >= 0)
							{
								l += Convert.ToInt32(xhcount);
							}
							if (TmpStr.IndexOf("M08") >= 0)
							{
								xhcount = 0.0;
								xhave = false;
							}
							if (TmpStr.IndexOf('M') < 0 && TmpStr.IndexOf('T') < 0)
							{
								if (TmpStr.IndexOf("G85") > 0)
								{
									if (Convert.ToInt32(vDrillLoadOption["drillRadioGroup1"].ToString()) == 3)
									{
										strremark = "SLOTB";
									}
									else
									{
										strremark = "SLOT";
									}
									string tmpLine = TmpStr;
									int intLocal = tmpLine.IndexOf('Y');
									string strX = tmpLine.Substring(1, intLocal - 1);
									int LB = 0;
									while (LB < strX.Length && !new ArrayList(new char[]
									{
										'0',
										'1',
										'2',
										'3',
										'4',
										'5',
										'6',
										'7',
										'8',
										'9',
										'.'
									}).Contains(strX[LB]))
									{
										LB++;
									}
									strX = strX.Substring(LB, strX.Length - LB);
									strX = this.FullCoordinate2(strX, tmpZeroType);
									double douX = Convert.ToDouble(strX) / 1000.0;
									tmpLine = tmpLine.Substring(intLocal, tmpLine.Length - intLocal);
									intLocal = tmpLine.IndexOf("G85");
									string strY = tmpLine.Substring(1, intLocal - 1);
									LB = 0;
									while (LB < strY.Length && !new ArrayList(new char[]
									{
										'0',
										'1',
										'2',
										'3',
										'4',
										'5',
										'6',
										'7',
										'8',
										'9',
										'.'
									}).Contains(strY[LB]))
									{
										LB++;
									}
									strY = strY.Substring(LB, strY.Length - LB);
									strY = this.FullCoordinate2(strY, tmpZeroType);
									double douY = Convert.ToDouble(strY) / 1000.0;
									tmpLine = tmpLine.Substring(intLocal + 3, tmpLine.Length - intLocal - 3);
									intLocal = tmpLine.IndexOf('Y');
									string strX2 = tmpLine.Substring(1, intLocal - 1);
									LB = 0;
									while (LB < strX2.Length && !new ArrayList(new char[]
									{
										'0',
										'1',
										'2',
										'3',
										'4',
										'5',
										'6',
										'7',
										'8',
										'9',
										'.'
									}).Contains(strX2[LB]))
									{
										LB++;
									}
									strX2 = strX2.Substring(LB, strX2.Length - LB);
									strX2 = this.FullCoordinate2(strX2, tmpZeroType);
									double douX2 = Convert.ToDouble(strX2) / 1000.0;
									tmpLine = tmpLine.Substring(intLocal, tmpLine.Length - intLocal);
									string strY2 = tmpLine.Substring(1, tmpLine.Length - 1);
									LB = 0;
									while (LB < strY2.Length && !new ArrayList(new char[]
									{
										'0',
										'1',
										'2',
										'3',
										'4',
										'5',
										'6',
										'7',
										'8',
										'9',
										'.'
									}).Contains(strY2[LB]))
									{
										LB++;
									}
									strY2 = strY2.Substring(LB, strY2.Length - LB);
									strY2 = this.FullCoordinate2(strY2, tmpZeroType);
									double douY2 = Convert.ToDouble(strY2) / 1000.0;
									double douPathLength = Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0));
									double douDrillCount = Math.Floor(douPathLength / douSeparated * 1.0) + 2.0;
									if (vDRILLSLOT_ITEMCOUNT == 1)
									{
										l++;
									}
									else
									{
										l += Convert.ToInt32(douDrillCount);
									}
									double vSLOTLEN = Math.Round((douPathLength + douCaliber) * 100.0) * 0.01;
									if (!SLOTremark.Contains(Convert.ToString(douCaliber) + "x" + Convert.ToString(vSLOTLEN) + " "))
									{
										SLOTremark = string.Concat(new string[]
										{
											SLOTremark,
											Convert.ToString(douCaliber),
											"x",
											Convert.ToString(vSLOTLEN),
											" "
										});
									}
									if (xhave)
									{
										xhcount += douDrillCount;
									}
								}
								else
								{
									l++;
									if (xhave)
									{
										xhcount += 1.0;
									}
								}
							}
						}
						if (i == drList.Count - 2)
						{
							List3.Add(Convert.ToString(l));
							List4.Add(SLOTremark);
							SLOTremark = "";
							bend = true;
						}
						i++;
					}
					double vVIA = Convert.ToDouble(List2[k].ToString());
					if (Binchtomm)
					{
						vVIA = Convert.ToDouble((vVIA * 25.4).ToString(U_point));
					}
					string cVIA = vVIA.ToString(U_point);
					bhave = false;
					DataRow dr29;
					if (ADDFLAG == 1)
					{
						if (Convert.ToInt32(vDrillLoadOption["drillRadioGroup2"].ToString()) == 0)
						{
							DataRow[] dv0029v = TempTable.Select("HOLES_DIA_DISP='" + cVIA.ToString() + "'");
							if (dv0029v.Count<DataRow>() > 0)
							{
								dr29 = dv0029v[0];
								bhave = true;
							}
							else
							{
								dr29 = TempTable.NewRow();
								bhave = false;
							}
						}
						else
						{
							DataRow[] dv0029v = TempTable.Select("DRILL_DIA_DISP='" + cVIA.ToString() + "'");
							if (dv0029v.Count<DataRow>() > 0)
							{
								dr29 = dv0029v[0];
								bhave = true;
							}
							else
							{
								dr29 = TempTable.NewRow();
								bhave = false;
							}
						}
					}
					else
					{
						dr29 = TempTable.NewRow();
					}
					int newid = this.GetData0029NewId(TempTable);
					int vSEQ_NO = this.GetData0029NewSEQ_NO(TempTable);
					dr29["RKEY"] = newid;
					dr29["SEQ_NO"] = vSEQ_NO;
					dr29["SEQ_NR"] = List[k].ToString();
					int vpnl_set = vpnl_pcs / vset_pcs;
					switch (Convert.ToInt32(vDrillLoadOption["drillRadioGroup1"].ToString()))
					{
					case 0:
						dr29["UNIT"] = List3[k].ToString();
						if (Convert.ToInt32(vdata1192.Rows[0]["drill_pcs_set_pnl"].ToString()) == 1)
						{
							dr29["SETS"] = Math.Round(Convert.ToDouble(List3[k].ToString()) * (double)vset_pcs);
							dr29["PANEL_A"] = Math.Round(Convert.ToDouble(List3[k].ToString()) * (double)vpnl_pcs);
						}
						if (strremark.Contains("SLOT"))
						{
							if (dr29["REMARK"] != DBNull.Value)
							{
								dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString();
							}
							else
							{
								dr29["REMARK"] = strremark + " " + List4[k].ToString();
							}
							dr29["SLOT_HQTY"] = List3[k].ToString();
						}
						else if (dr29["REMARK"] != DBNull.Value)
						{
							dr29["REMARK"] = dr29["REMARK"].ToString() + strremark;
						}
						else
						{
							dr29["REMARK"] = strremark;
						}
						if (Convert.ToInt32(vDrillLoadOption["drillRadioGroup2"].ToString()) == 0)
						{
							dr29["HOLES_DIA_DISP"] = cVIA;
						}
						else
						{
							dr29["DRILL_DIA_DISP"] = cVIA;
						}
						break;
					case 1:
						dr29["SETS"] = List3[k].ToString();
						if (Convert.ToInt32(vdata1192.Rows[0]["drill_pcs_set_pnl"].ToString()) == 1)
						{
							dr29["UNIT"] = Math.Round(Convert.ToDouble(List3[k].ToString()) * (double)vset_pcs);
							dr29["PANEL_A"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_set);
						}
						break;
					case 2:
						dr29["PANEL_A"] = List3[k].ToString();
						if (Convert.ToInt32(vdata1192.Rows[0]["drill_pcs_set_pnl"].ToString()) == 1)
						{
							dr29["UNIT"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_pcs);
							dr29["SETS"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_set);
						}
						if (strremark.Contains("SLOT"))
						{
							if (dr29["REMARK"] != DBNull.Value)
							{
								dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString();
							}
							else
							{
								dr29["REMARK"] = strremark + " " + List4[k].ToString();
							}
							dr29["SLOT_HQTY"] = List3[k].ToString();
						}
						else if (dr29["REMARK"] != DBNull.Value)
						{
							dr29["REMARK"] = dr29["REMARK"].ToString() + strremark;
						}
						else
						{
							dr29["REMARK"] = strremark;
						}
						if (Convert.ToInt32(vDrillLoadOption["drillRadioGroup2"].ToString()) == 0)
						{
							dr29["HOLES_DIA_DISP"] = cVIA;
						}
						else
						{
							dr29["DRILL_DIA_DISP"] = cVIA;
						}
						break;
					case 3:
						dr29["PANEL_B"] = List3[k].ToString();
						if (Convert.ToInt32(vdata1192.Rows[0]["drill_pcs_set_pnl"].ToString()) == 1)
						{
							dr29["UNIT"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_pcs);
							dr29["SETS"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_set);
						}
						if (strremark.Contains("SLOT"))
						{
							if (dr29["REMARK"] != DBNull.Value)
							{
								dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString();
							}
							else
							{
								dr29["REMARK"] = strremark + " " + List4[k].ToString();
							}
							dr29["SLOT_HQTY2"] = List3[k].ToString();
						}
						else if (dr29["REMARK"] != DBNull.Value)
						{
							dr29["REMARK"] = dr29["REMARK"].ToString() + strremark;
						}
						else
						{
							dr29["REMARK"] = strremark;
						}
						if (Convert.ToInt32(vDrillLoadOption["drillRadioGroup2"].ToString()) == 0)
						{
							dr29["HOLES_DIA_DISP"] = cVIA;
						}
						else
						{
							dr29["DRILL_DIA_DISP"] = cVIA;
						}
						break;
					case 4:
						dr29["PANEL_C"] = List3[k].ToString();
						if (Convert.ToInt32(vdata1192.Rows[0]["drill_pcs_set_pnl"].ToString()) == 1)
						{
							dr29["UNIT"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_pcs);
							dr29["SETS"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_set);
						}
						if (strremark.Contains("SLOT"))
						{
							if (dr29["REMARK"] != DBNull.Value)
							{
								dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString();
							}
							else
							{
								dr29["REMARK"] = strremark + " " + List4[k].ToString();
							}
							dr29["SLOT_HQTY2"] = List3[k].ToString();
						}
						else if (dr29["REMARK"] != DBNull.Value)
						{
							dr29["REMARK"] = dr29["REMARK"].ToString() + strremark;
						}
						else
						{
							dr29["REMARK"] = strremark;
						}
						if (Convert.ToInt32(vDrillLoadOption["drillRadioGroup2"].ToString()) == 0)
						{
							dr29["HOLES_DIA_DISP"] = cVIA;
						}
						else
						{
							dr29["DRILL_DIA_DISP"] = cVIA;
						}
						break;
					case 5:
						dr29["PANEL_D"] = List3[k].ToString();
						if (Convert.ToInt32(vdata1192.Rows[0]["drill_pcs_set_pnl"].ToString()) == 1)
						{
							dr29["UNIT"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_pcs);
							dr29["SETS"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_set);
						}
						if (strremark.Contains("SLOT"))
						{
							if (dr29["REMARK"] != DBNull.Value)
							{
								dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString();
							}
							else
							{
								dr29["REMARK"] = strremark + " " + List4[k].ToString();
							}
							dr29["SLOT_HQTY2"] = List3[k].ToString();
						}
						else if (dr29["REMARK"] != DBNull.Value)
						{
							dr29["REMARK"] = dr29["REMARK"].ToString() + strremark;
						}
						else
						{
							dr29["REMARK"] = strremark;
						}
						if (Convert.ToInt32(vDrillLoadOption["drillRadioGroup2"].ToString()) == 0)
						{
							dr29["HOLES_DIA_DISP"] = cVIA;
						}
						else
						{
							dr29["DRILL_DIA_DISP"] = cVIA;
						}
						break;
					}
					int num = Convert.ToInt32(vDrillLoadOption["drillRadioGroup3"].ToString());
					if (num != 0)
					{
						if (num == 1)
						{
							string vNPTH = "NPTH";
							if (vDrillLoadOption.ContainsKey("NPTH") && vDrillLoadOption["NPTH"] != null && vDrillLoadOption["NPTH"].ToString().Trim() != "")
							{
								vNPTH = vDrillLoadOption["NPTH"].ToString().Trim();
							}
							dr29["PTH"] = vNPTH;
						}
					}
					else
					{
						string vPTH = "PTH";
						if (vDrillLoadOption.ContainsKey("PTH") && vDrillLoadOption["PTH"] != null && vDrillLoadOption["PTH"].ToString().Trim() != "")
						{
							vPTH = vDrillLoadOption["PTH"].ToString().Trim();
						}
						dr29["PTH"] = vPTH;
					}
					if (!bhave)
					{
						TempTable.Rows.Add(dr29);
					}
				}
				if (Convert.ToInt32(vdata1192.Rows[0]["drill_load_sum"].ToString()) == 1)
				{
					string vFieldName = "UNIT";
					switch (Convert.ToInt32(vDrillLoadOption["drillRadioGroup1"].ToString()))
					{
					case 0:
						vFieldName = "UNIT";
						break;
					case 1:
						vFieldName = "SETS";
						break;
					case 2:
						vFieldName = "PANEL_A";
						break;
					case 3:
						vFieldName = "PANEL_B";
						break;
					case 4:
						vFieldName = "PANEL_C";
						break;
					case 5:
						vFieldName = "PANEL_D";
						break;
					}
					this.Drills_Sum(ref TempTable, vFieldName, ref vdata0025, vTTYPE);
				}
			}
			finally
			{
				drList.Clear();
				List.Clear();
				List2.Clear();
				List3.Clear();
				List4.Clear();
			}
		}

		// Token: 0x0600025E RID: 606 RVA: 0x00057D68 File Offset: 0x00055F68
		private void DRILL_LOAD_FmExcel(ref DataTable TempTable, DataTable vtableItems, DataTable vdata1192, Dictionary<string, object> vDrillLoadOption, int vset_pcs, int vpnl_pcs, string FilePath, ref Dictionary<string, object> vdata0025, int vTTYPE, int ADDFLAG)
		{
			switch (Convert.ToInt32(vDrillLoadOption["FORMAT"].ToString()))
			{
			}
			ArrayList drList = new ArrayList();
			ArrayList List = new ArrayList();
			ArrayList List2 = new ArrayList();
			ArrayList List3 = new ArrayList();
			ArrayList List4 = new ArrayList();
			try
			{
				FileStream fs = new FileStream(FilePath, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);
				IWorkbook workbook = null;
				if (Path.GetExtension(fs.Name) == ".xls")
				{
					workbook = new HSSFWorkbook(fs);
				}
				else if (Path.GetExtension(fs.Name) == ".xlsx")
				{
					workbook = new XSSFWorkbook(fs);
				}
				ISheet sheet = workbook.GetSheetAt(0);
				int m = sheet.LastRowNum;
				if (m > 0)
				{
					for (int j = 1; j <= sheet.LastRowNum; j++)
					{
						IRow row = sheet.GetRow(j);
						if (row != null)
						{
							DataRow dr29 = TempTable.NewRow();
							int newid = this.GetData0029NewId(TempTable);
							int vSEQ_NO = this.GetData0029NewSEQ_NO(TempTable);
							dr29["RKEY"] = newid;
							dr29["SEQ_NO"] = vSEQ_NO;
							int m2 = row.Cells.Count<ICell>();
							if (m2 > vtableItems.Rows.Count)
							{
								m2 = vtableItems.Rows.Count;
							}
							if (m2 > 0)
							{
								for (int j2 = 0; j2 < m2; j2++)
								{
									ICell newCell = row.Cells[j2];
									if (newCell != null && newCell.ToString() != "{}")
									{
										string vtex = this.GetCellValue(newCell).ToUpper();
										dr29[vtableItems.Rows[j2]["prop"].ToString()] = vtex;
									}
								}
							}
							TempTable.Rows.Add(dr29);
						}
					}
				}
				if (Convert.ToInt32(vdata1192.Rows[0]["drill_load_sum"].ToString()) == 1)
				{
					string vFieldName = "UNIT";
					switch (Convert.ToInt32(vDrillLoadOption["drillRadioGroup1"].ToString()))
					{
					case 0:
						vFieldName = "UNIT";
						break;
					case 1:
						vFieldName = "SETS";
						break;
					case 2:
						vFieldName = "PANEL_A";
						break;
					case 3:
						vFieldName = "PANEL_B";
						break;
					case 4:
						vFieldName = "PANEL_C";
						break;
					case 5:
						vFieldName = "PANEL_D";
						break;
					}
					this.Drills_Sum(ref TempTable, vFieldName, ref vdata0025, vTTYPE);
				}
			}
			finally
			{
				drList.Clear();
				List.Clear();
				List2.Clear();
				List3.Clear();
				List4.Clear();
			}
		}

		// Token: 0x0600025F RID: 607 RVA: 0x00058090 File Offset: 0x00056290
		private string GetCellValue(ICell cell)
		{
			if (cell == null)
			{
				return string.Empty;
			}
			switch (cell.CellType)
			{
			case 0:
				return cell.ToString();
			case 1:
				return cell.StringCellValue;
			case 2:
				return "=" + cell.CellFormula;
			case 3:
				return string.Empty;
			case 4:
				return cell.BooleanCellValue.ToString();
			case 5:
				return cell.ErrorCellValue.ToString();
			}
			return cell.ToString();
		}

		// Token: 0x06000260 RID: 608 RVA: 0x0005811C File Offset: 0x0005631C
		private string FullCoordinate2(string tmpCoordnate, int tmpZeroType)
		{
			int intLen = tmpCoordnate.Length;
			string tmpResult;
			if (intLen < 6)
			{
				int intRecruit = 6 - intLen;
				if (tmpZeroType == 1)
				{
					tmpResult = tmpCoordnate + "0000000".Substring(0, 6 - intLen);
				}
				else
				{
					tmpResult = "0000000".Substring(0, 6 - intLen) + tmpCoordnate;
				}
			}
			else
			{
				tmpResult = tmpCoordnate;
			}
			return tmpResult;
		}

		// Token: 0x06000261 RID: 609 RVA: 0x00058170 File Offset: 0x00056370
		private void Drills_Sum(ref DataTable TempTable, string vFieldName, ref Dictionary<string, object> vdata0025, int vTabName)
		{
			double tmpqtysum = 0.0;
			double tmpminqty = 100.0;
			double tmpqtysum2 = 0.0;
			double tmpSLOTqtysumA = 0.0;
			double tmpSLOTqtysumB = 0.0;
			int i = TempTable.Rows.Count;
			for (int j = 0; j < i; j++)
			{
				if ((TempTable.Rows[j]["DRILL_DIA_DISP"] != DBNull.Value && TempTable.Rows[j]["DRILL_DIA_DISP"].ToString() != "") || (TempTable.Rows[j]["HOLES_DIA_DISP"] != DBNull.Value && TempTable.Rows[j]["HOLES_DIA_DISP"].ToString() != ""))
				{
					double tmpHolefloat = 0.0;
					double tmpDrillfloat = 0.0;
					string TmpStr = TempTable.Rows[j]["DRILL_DIA_DISP"].ToString();
					if (TmpStr != "" && this.IsNumberic(TmpStr))
					{
						tmpDrillfloat = Convert.ToDouble(TmpStr);
					}
					string TmpStr2 = TempTable.Rows[j]["HOLES_DIA_DISP"].ToString();
					if (TmpStr2 != "" && this.IsNumberic(TmpStr2))
					{
						tmpHolefloat = Convert.ToDouble(TmpStr2);
					}
					if (tmpHolefloat > 0.0 || tmpDrillfloat > 0.0)
					{
						string sqlremove = string.Format(" SELECT 1 FROM drills_count_remove(NOLOCK) WHERE DRILL_DIA={0}  OR DRILL_DIA={1}   and  TTYPE=0 ", tmpDrillfloat, tmpHolefloat);
						DataTable vdatahave = DBHelper.ExecuteDataTable(sqlremove, new SqlParameter[0]);
						if (vdatahave.Rows.Count <= 0 && tmpDrillfloat > 0.0 && tmpminqty > tmpDrillfloat)
						{
							tmpminqty = tmpDrillfloat;
						}
						if (TempTable.Columns.Contains("SLOT_HQTY") && TempTable.Rows[j]["SLOT_HQTY"] != DBNull.Value && TempTable.Rows[j]["SLOT_HQTY"].ToString() != "")
						{
							tmpSLOTqtysumA += Convert.ToDouble(TempTable.Rows[j]["SLOT_HQTY"].ToString());
						}
						if (TempTable.Columns.Contains("SLOT_HQTY2") && TempTable.Rows[j]["SLOT_HQTY2"] != DBNull.Value && TempTable.Rows[j]["SLOT_HQTY2"].ToString() != "")
						{
							tmpSLOTqtysumB += Convert.ToDouble(TempTable.Rows[j]["SLOT_HQTY2"].ToString());
						}
						if (TempTable.Rows[j][vFieldName] != DBNull.Value && TempTable.Rows[j][vFieldName].ToString() != "" && this.IsNumberic(TempTable.Rows[j][vFieldName].ToString()))
						{
							double tmpfloat = Convert.ToDouble(TempTable.Rows[j][vFieldName].ToString());
							if (tmpHolefloat > 0.0)
							{
								string sqlremove2 = string.Format("SELECT 1 FROM drills_count_remove(NOLOCK) WHERE DRILL_DIA={0} and TTYPE=1 ", tmpHolefloat);
								DataTable vdatahave2 = DBHelper.ExecuteDataTable(sqlremove2, new SqlParameter[0]);
								if (vdatahave2.Rows.Count <= 0)
								{
									tmpqtysum2 += tmpfloat;
									tmpqtysum += tmpfloat;
								}
								else
								{
									tmpqtysum2 += tmpfloat;
								}
							}
							else
							{
								TmpStr = TempTable.Rows[j][vFieldName].ToString().Trim();
								if (TmpStr != "")
								{
									string sqlv = "SELECT " + TmpStr + " as vqty ";
									DataTable vdatatable = DBHelper.ExecuteDataTable(sqlv, new SqlParameter[0]);
									if (vdatatable.Rows.Count > 0 && vdatatable.Rows[0]["vqty"] != DBNull.Value)
									{
										tmpfloat = Convert.ToDouble(vdatatable.Rows[0]["vqty"].ToString());
										tmpqtysum2 += tmpfloat;
										tmpqtysum += tmpfloat;
									}
								}
							}
						}
					}
				}
			}
			tmpqtysum = tmpqtysum2;
			if (tmpqtysum2 > 0.0)
			{
				bool havesum = false;
				DataRow[] dr = TempTable.Select("COUNT_FLAG=1");
				if (dr.Length != 0)
				{
					havesum = true;
					dr[0][vFieldName] = tmpqtysum.ToString();
				}
				else
				{
					DataRow[] dr2 = TempTable.Select("DRILL_DIA_DISP='" + LangHelper.GetString("合计") + "'");
					if (dr2.Length != 0)
					{
						havesum = true;
						dr2[0][vFieldName] = tmpqtysum.ToString();
						dr2[0]["COUNT_FLAG"] = 1;
					}
				}
				if (!havesum)
				{
					DataRow drnew = TempTable.NewRow();
					int newid = this.GetData0029NewId(TempTable);
					drnew["RKEY"] = newid;
					if (TempTable.Columns.Contains("SEQ_NO"))
					{
						int vSEQ_NO = this.GetData0029NewSEQ_NO(TempTable);
						drnew["SEQ_NO"] = vSEQ_NO;
					}
					drnew["COUNT_FLAG"] = 1;
					if (TempTable.Columns.Contains("DRILL_DIA_DISP"))
					{
						drnew["DRILL_DIA_DISP"] = LangHelper.GetString("合计");
					}
					drnew[vFieldName] = tmpqtysum.ToString();
					TempTable.Rows.Add(drnew);
				}
				vdata0025["ZMIN" + (vTabName - 1).ToString()] = tmpminqty.ToString();
				vdata0025["ZCOUNT" + (vTabName - 1).ToString()] = tmpqtysum2.ToString();
				vdata0025["SLOTDRILLCOUNT" + (vTabName - 1).ToString()] = (tmpSLOTqtysumA + tmpSLOTqtysumB).ToString();
				vdata0025["DRILLCOUNT" + (vTabName - 1).ToString()] = (tmpqtysum2 - tmpSLOTqtysumA - tmpSLOTqtysumB).ToString();
			}
			this.Drills_Total(ref vdata0025);
		}

		// Token: 0x06000262 RID: 610 RVA: 0x000587F4 File Offset: 0x000569F4
		public ActionResult SumCount()
		{
			string vFieldName = base.Request.QueryString["vFieldName"];
			int vTabName = Convert.ToInt32(base.Request.QueryString["vTabName"].ToString());
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			JObject jo = (JObject)JsonConvert.DeserializeObject(myJson);
			DataTable vdata29 = TableHelp.JsonToDataTable2(jo["vdata0029"].ToString());
			Dictionary<string, object> vdata30 = JsonConvert.DeserializeObject<Dictionary<string, object>>(jo["vdata0025"].ToString());
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			this.Drills_Sum(ref vdata29, vFieldName, ref vdata30, vTabName);
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonMo2(100, vdata29.Rows.Count, LangHelper.GetString("统计完成"), JsonConvert.SerializeObject(vdata29), JsonConvert.SerializeObject(vdata30)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x06000263 RID: 611 RVA: 0x00058930 File Offset: 0x00056B30
		public ActionResult DRILL_Remark()
		{
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			DataTable vdata29 = TableHelp.JsonToDataTable(myJson);
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			int s = vdata29.Rows.Count;
			for (int c = 0; c < s; c++)
			{
				if (vdata29.Rows[c]["DRILL_DIA_DISP"] != DBNull.Value && vdata29.Rows[c]["DRILL_DIA_DISP"].ToString().Trim() != "" && this.IsNumberic(vdata29.Rows[c]["DRILL_DIA_DISP"].ToString()))
				{
					double tmpfloat = Convert.ToDouble(vdata29.Rows[c]["DRILL_DIA_DISP"].ToString().Trim());
					string sqlv = string.Format("select * from drills_remark(nolock) where DRILL_DIA= {0} ", tmpfloat);
					DataTable vdatatable = DBHelper.ExecuteDataTable(sqlv, new SqlParameter[0]);
					if (vdatatable.Rows.Count > 0 && vdatatable.Rows[0]["DRILL_REMARK"] != DBNull.Value && vdatatable.Rows[0]["DRILL_REMARK"].ToString().Trim() != "")
					{
						if (vdata29.Rows[c]["REMARK"] != DBNull.Value && vdata29.Rows[c]["REMARK"].ToString().Trim() != "")
						{
							vdata29.Rows[c]["REMARK"] = vdata29.Rows[c]["REMARK"].ToString().Trim() + vdatatable.Rows[0]["DRILL_REMARK"].ToString().Trim();
						}
						else
						{
							vdata29.Rows[c]["REMARK"] = vdatatable.Rows[0]["DRILL_REMARK"].ToString().Trim();
						}
					}
				}
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, vdata29.Rows.Count, LangHelper.GetString("备注完成"), JsonConvert.SerializeObject(vdata29)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x06000264 RID: 612 RVA: 0x00058C18 File Offset: 0x00056E18
		public ActionResult SPEC_MARK_NPTH()
		{
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			DataTable vdata29 = TableHelp.JsonToDataTable(myJson);
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			int s = vdata29.Rows.Count;
			for (int c = 0; c < s; c++)
			{
				if (vdata29.Rows[c]["DRILL_DIA_DISP"] != DBNull.Value && vdata29.Rows[c]["DRILL_DIA_DISP"].ToString().Trim() != "" && this.IsNumberic(vdata29.Rows[c]["DRILL_DIA_DISP"].ToString()))
				{
					double tmpfloat = Convert.ToDouble(vdata29.Rows[c]["DRILL_DIA_DISP"].ToString().Trim());
					double tmpfloat2 = Math.Floor(tmpfloat * 100.0) * 10.0;
					if (tmpfloat * 1000.0 - tmpfloat2 == 3.0)
					{
						vdata29.Rows[c]["SPEC_MARK"] = "无铜孔";
					}
				}
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, vdata29.Rows.Count, LangHelper.GetString("无铜孔类型标识完成"), JsonConvert.SerializeObject(vdata29)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x06000265 RID: 613 RVA: 0x00058DE8 File Offset: 0x00056FE8
		public ActionResult ClearCount()
		{
			string vFieldName = base.Request.QueryString["vFieldName"];
			string myJson = string.Empty;
			base.Request.InputStream.Position = 0L;
			using (StreamReader sr = new StreamReader(base.Request.InputStream))
			{
				myJson = sr.ReadToEnd();
			}
			DataTable TempTable = TableHelp.JsonToDataTable(myJson);
			StringBuilder builder = new StringBuilder();
			builder.Clear();
			int ihave = 0;
			int i = TempTable.Rows.Count;
			for (int j = 0; j < i; j++)
			{
				TempTable.Rows[j][vFieldName] = DBNull.Value;
			}
			if (ihave == 0)
			{
				return base.Json(new ReturnJsonInfo(100, TempTable.Rows.Count, LangHelper.GetString("清除完成"), JsonConvert.SerializeObject(TempTable)), 0);
			}
			return base.Json(new ReturnJsonInfo(400, 0, builder.ToString(), null), 0);
		}

		// Token: 0x06000266 RID: 614 RVA: 0x00058EF0 File Offset: 0x000570F0
		private bool IsNumberic(string str)
		{
			double vsNum;
			return double.TryParse(str, NumberStyles.Float, NumberFormatInfo.InvariantInfo, out vsNum);
		}

		// Token: 0x06000267 RID: 615 RVA: 0x00058F14 File Offset: 0x00057114
		private int GetData0029NewId(DataTable table)
		{
			if (table.Rows.Count == 0)
			{
				return 1;
			}
			int max = Convert.ToInt32(table.Rows[0]["RKEY"]);
			for (int i = 1; i < table.Rows.Count; i++)
			{
				if (Convert.ToInt32(table.Rows[i]["RKEY"]) > max)
				{
					max = Convert.ToInt32(table.Rows[i]["RKEY"]);
				}
			}
			return max + 1;
		}

		// Token: 0x06000268 RID: 616 RVA: 0x00058FA0 File Offset: 0x000571A0
		private int GetData0029NewSEQ_NO(DataTable table)
		{
			if (table.Rows.Count == 0)
			{
				return 1;
			}
			int max = 0;
			if (table.Rows[0]["SEQ_NO"] != DBNull.Value)
			{
				max = Convert.ToInt32(table.Rows[0]["SEQ_NO"]);
			}
			for (int i = 1; i < table.Rows.Count; i++)
			{
				if (table.Rows[i]["SEQ_NO"] != DBNull.Value && Convert.ToInt32(table.Rows[i]["SEQ_NO"]) > max)
				{
					max = Convert.ToInt32(table.Rows[i]["SEQ_NO"]);
				}
			}
			return max + 1;
		}

		// Token: 0x06000269 RID: 617 RVA: 0x00059068 File Offset: 0x00057268
		private void Drills_Total(ref Dictionary<string, object> data0025)
		{
			string sql1195 = "SELECT TOP 1 DATA1195.TABSHT_DRILL1_VFLAG,DATA1195.TABSHT_DRILL2_VFLAG,DATA1195.TABSHT_DRILL3_VFLAG,DATA1195.TABSHT_DRILL4_VFLAG\r\n,DATA1195.TABSHT_DRILL5_VFLAG,DATA1195.TABSHT_DRILL6_VFLAG,DATA1195.TABSHT_DRILL7_VFLAG,DATA1195.TABSHT_DRILL8_VFLAG\r\n,DATA1195.TABSHT_DRILL9_VFLAG,DATA1195.TABSHT_DRILL10_VFLAG,DATA1195.TABSHT_DRILL11_VFLAG,DATA1195.TABSHT_DRILL12_VFLAG\r\n,DATA1195.TABSHT_DRILL13_VFLAG,DATA1195.TABSHT_DRILL14_VFLAG,DATA1195.TABSHT_DRILL15_VFLAG,DATA1195.TABSHT_DRILL16_VFLAG\r\n,DATA1195.TABSHT_DRILL1,DATA1195.TABSHT_DRILL2,DATA1195.TABSHT_DRILL3,DATA1195.TABSHT_DRILL4\r\n,DATA1195.TABSHT_DRILL5,DATA1195.TABSHT_DRILL6,DATA1195.TABSHT_DRILL7,DATA1195.TABSHT_DRILL8\r\n,DATA1195.TABSHT_DRILL9,DATA1195.TABSHT_DRILL10,DATA1195.TABSHT_DRILL11,DATA1195.TABSHT_DRILL12\r\n,DATA1195.TABSHT_DRILL13,DATA1195.TABSHT_DRILL14,DATA1195.TABSHT_DRILL15,DATA1195.TABSHT_DRILL16\r\n,DATA1195.drill_load_sum\r\nFROM DATA1195(NOLOCK)";
			DataTable vdata1195 = DBHelper.ExecuteDataTable(sql1195, new SqlParameter[0]);
			int tmpqtysum = 0;
			double tmpminqty = 100.0;
			for (int i = 0; i < 16; i++)
			{
				if ((bool)vdata1195.Rows[0]["TABSHT_DRILL" + (i + 1).ToString() + "_VFLAG"])
				{
					double vZMIN = 0.0;
					if (data0025.ContainsKey("ZMIN" + (i + 1).ToString()) && data0025["ZMIN" + (i + 1).ToString()] != null && data0025["ZMIN" + (i + 1).ToString()].ToString().Trim() != "")
					{
						vZMIN = Convert.ToDouble(data0025["ZMIN" + (i + 1).ToString()].ToString());
					}
					if (vZMIN > 0.0 && vZMIN < tmpminqty)
					{
						tmpminqty = vZMIN;
					}
					int vZCOUNT = 0;
					if (data0025.ContainsKey("ZCOUNT" + (i + 1).ToString()) && data0025["ZCOUNT" + (i + 1).ToString()] != null && data0025["ZCOUNT" + (i + 1).ToString()].ToString().Trim() != "")
					{
						vZCOUNT = Convert.ToInt32(data0025["ZCOUNT" + (i + 1).ToString()].ToString());
					}
					tmpqtysum += vZCOUNT;
				}
			}
			data0025["ZMIN"] = tmpminqty.ToString();
			data0025["ZCOUNT"] = tmpqtysum.ToString();
		}
	}
}
