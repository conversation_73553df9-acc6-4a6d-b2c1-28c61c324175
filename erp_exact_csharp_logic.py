#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全基于C#代码逻辑的ERP系统实现
根据drillRadioGroup1的值使用不同的计算公式
"""

import json
import re
import math

def get_erp_parameters():
    """从b.json获取ERP参数"""
    with open('c.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 从接口参数获取
    # /DrillData/UploadSave?TTYPE=2&set_pcs=1&pnl_pcs=8
    vset_pcs = 1  # set_pcs=1
    vpnl_pcs = 8  # pnl_pcs=8
    
    # 从b.json获取drillRadioGroup1
    with open('b.json', 'r', encoding='utf-8') as f:
        b_data = json.load(f)
    
    drill_radio_group1 = b_data['vDrillLoadOption']['drillRadioGroup1']
    
    # 假设drill_pcs_set_pnl = 1 (从C#代码逻辑推断)
    drill_pcs_set_pnl = 1
    
    return {
        'vset_pcs': vset_pcs,
        'vpnl_pcs': vpnl_pcs,
        'drill_radio_group1': drill_radio_group1,
        'drill_pcs_set_pnl': drill_pcs_set_pnl
    }

def calculate_panel_a_by_csharp_logic():
    """完全按照C#代码逻辑计算PANEL_A"""
    print("🎯 完全基于C#代码逻辑的ERP计算")
    print("=" * 60)
    
    # 获取ERP参数
    params = get_erp_parameters()
    vset_pcs = params['vset_pcs']
    vpnl_pcs = params['vpnl_pcs']
    drill_radio_group1 = params['drill_radio_group1']
    drill_pcs_set_pnl = params['drill_pcs_set_pnl']
    
    print(f"📊 ERP参数:")
    print(f"   vset_pcs = {vset_pcs}")
    print(f"   vpnl_pcs = {vpnl_pcs}")
    print(f"   drill_radio_group1 = {drill_radio_group1}")
    print(f"   drill_pcs_set_pnl = {drill_pcs_set_pnl}")
    
    vpnl_set = vpnl_pcs // vset_pcs  # C#: int vpnl_set = vpnl_pcs / vset_pcs;
    print(f"   vpnl_set = {vpnl_set}")
    
    # 读取DRL文件计算List3[k] (基础钻孔数)
    with open('z0l04p0t500365a0.drl', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 解析工具定义
    tools = {}
    tool_pattern = re.compile(r'T(\d+)C([\d\.]+)')
    
    for line in lines:
        line = line.strip()
        match = tool_pattern.match(line)
        if match:
            tool_num = int(match.group(1))
            diameter = float(match.group(2))
            tools[tool_num] = diameter
    
    # 统计每个工具的坐标数和G85槽数
    tool_coords = {}
    tool_g85_slots = {}
    current_tool = None
    
    coord_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)')
    g85_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)G85X([\d\-\.]+)Y([\d\-\.]+)')
    
    for line in lines:
        line = line.strip()
        
        # 检查工具切换
        if line.startswith('T') and line[1:].isdigit():
            current_tool = int(line[1:])
            if current_tool not in tool_coords:
                tool_coords[current_tool] = 0
                tool_g85_slots[current_tool] = []
            continue
        
        if current_tool is None:
            continue
        
        # 检查G85槽
        g85_match = g85_pattern.match(line)
        if g85_match:
            x1, y1, x2, y2 = map(float, g85_match.groups())
            tool_g85_slots[current_tool].append((x1, y1, x2, y2))
            continue
        
        # 检查普通坐标
        coord_match = coord_pattern.match(line)
        if coord_match:
            tool_coords[current_tool] += 1
    
    # 计算List3[k] (基础钻孔数)
    list3_values = {}
    for tool_num in sorted(tools.keys()):
        if tool_num not in tool_coords:
            continue
            
        coord_count = tool_coords[tool_num]
        g85_count = len(tool_g85_slots[tool_num])
        
        # List3[k] = 普通坐标数 + G85槽数 (每个槽计算1个)
        list3_values[tool_num] = coord_count + g85_count
    
    # 读取ERP数据进行对比
    with open('c.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    tools_data = json.loads(data['Data'])
    
    # 创建ERP数据字典
    erp_data = {}
    for tool in tools_data:
        if tool['SEQ_NR'] and tool['SEQ_NR'].startswith('T'):
            tool_num = int(tool['SEQ_NR'][1:])
            erp_data[tool_num] = {
                'panel_a': int(tool['PANEL_A']),
                'slot_hqty': int(tool['SLOT_HQTY']) if tool['SLOT_HQTY'] else 0,
                'remark': tool.get('REMARK', '')
            }
    
    # 按照C#代码逻辑计算PANEL_A
    print(f"\n📋 按C#代码逻辑计算 (drillRadioGroup1 = {drill_radio_group1}):")
    print(f"{'工具':<8} {'List3[k]':<8} {'计算公式':<20} {'PANEL_A':<8} {'ERP':<8} {'匹配':<8}")
    print("-" * 70)
    
    total_panel_a = 0
    total_slot_panel_a = 0
    
    for tool_num in sorted(tools.keys()):
        if tool_num not in list3_values:
            continue
            
        list3_k = list3_values[tool_num]
        erp_panel_a = erp_data.get(tool_num, {}).get('panel_a', 0)
        remark = erp_data.get(tool_num, {}).get('remark', '')
        is_slot = 'SLOT' in remark
        
        # 按照C#代码的switch逻辑计算PANEL_A
        if drill_radio_group1 == 0:
            # case 0: dr29["PANEL_A"] = Math.Round(Convert.ToDouble(List3[k].ToString()) * (double)vpnl_pcs);
            if drill_pcs_set_pnl == 1:
                panel_a = round(list3_k * vpnl_pcs)
                formula = f"{list3_k} * {vpnl_pcs}"
            else:
                panel_a = list3_k
                formula = f"{list3_k}"
                
        elif drill_radio_group1 == 1:
            # case 1: dr29["PANEL_A"] = Math.Round(Convert.ToDouble(List3[k].ToString()) / (double)vpnl_set);
            if drill_pcs_set_pnl == 1:
                panel_a = round(list3_k / vpnl_set)
                formula = f"{list3_k} / {vpnl_set}"
            else:
                panel_a = list3_k
                formula = f"{list3_k}"
                
        elif drill_radio_group1 == 2:
            # case 2: dr29["PANEL_A"] = List3[k].ToString();
            panel_a = list3_k
            formula = f"{list3_k}"
            
        elif drill_radio_group1 == 3:
            # case 3: dr29["PANEL_B"] = List3[k].ToString(); (PANEL_A不在这里设置)
            panel_a = list3_k
            formula = f"{list3_k}"
            
        elif drill_radio_group1 == 4:
            # case 4: dr29["PANEL_C"] = List3[k].ToString(); (PANEL_A不在这里设置)
            panel_a = list3_k
            formula = f"{list3_k}"
            
        elif drill_radio_group1 == 5:
            # case 5: dr29["PANEL_D"] = List3[k].ToString(); (PANEL_A不在这里设置)
            panel_a = list3_k
            formula = f"{list3_k}"
        else:
            panel_a = list3_k
            formula = f"{list3_k}"
        
        total_panel_a += panel_a
        
        if is_slot:
            total_slot_panel_a += panel_a
        
        match_status = "✅" if panel_a == erp_panel_a else "❌"
        
        if erp_panel_a > 0:  # 只显示ERP中有数据的工具
            print(f"T{tool_num:02d}     {list3_k:<8} {formula:<20} {panel_a:<8} {erp_panel_a:<8} {match_status:<8}")
    
    # 计算汇总数据
    erp_summary = json.loads(data['Data2'])
    erp_zcount = int(erp_summary['ZCOUNT1'])
    erp_slotdrillcount = int(erp_summary['SLOTDRILLCOUNT1'])
    erp_drillcount = int(erp_summary['DRILLCOUNT1'])
    
    calculated_drillcount = total_panel_a - total_slot_panel_a
    
    print("-" * 70)
    print(f"📊 汇总对比:")
    print(f"   ZCOUNT: ERP={erp_zcount}, Python={total_panel_a}, 匹配={'✅' if total_panel_a == erp_zcount else '❌'}")
    print(f"   SLOTDRILLCOUNT: ERP={erp_slotdrillcount}, Python={total_slot_panel_a}, 匹配={'✅' if total_slot_panel_a == erp_slotdrillcount else '❌'}")
    print(f"   DRILLCOUNT: ERP={erp_drillcount}, Python={calculated_drillcount}, 匹配={'✅' if calculated_drillcount == erp_drillcount else '❌'}")
    
    if total_panel_a == erp_zcount and total_slot_panel_a == erp_slotdrillcount:
        print(f"\n🎉 完美匹配！C#逻辑实现成功！")
    else:
        print(f"\n🔍 还有差异，需要进一步分析...")
        
        # 分析差异原因
        print(f"\n🔍 差异分析:")
        if total_panel_a != erp_zcount:
            print(f"   ZCOUNT差异: {total_panel_a - erp_zcount}")
        if total_slot_panel_a != erp_slotdrillcount:
            print(f"   SLOTDRILLCOUNT差异: {total_slot_panel_a - erp_slotdrillcount}")

if __name__ == "__main__":
    calculate_panel_a_by_csharp_logic()
