from openpyxl import load_workbook

def get_first_digit(file_path):
    try:
        # 加载工作簿
        wb = load_workbook(file_path, keep_vba=True)
        
        # 遍历所有工作表
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            for row in ws.iter_rows(values_only=False):
                for cell in row:
                    if isinstance(cell.value, (int, float)):
                        # 将数字转为字符串并取第一位
                        cell_str = str(abs(int(cell.value)))
                        # 获取第一位数字
                        first_digit = int(cell_str[0])
                        cell.value = first_digit
        
        # 保存修改后的文件
        wb.save(file_path)
        print(f'处理完成，结果保存至: {file_path}')
    
    except Exception as e:
        print(f'处理数据时出错: {str(e)}')

if __name__ == "__main__":
    file_path = r'D:\origin\工作簿1.xlsm'
    get_first_digit(file_path)