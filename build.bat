@echo off
echo 开始打包应用程序...

REM 安装打包工具（如果需要）
pip install pyinstaller

REM 打包 api.py
echo 正在打包 api.py...
pyinstaller --noconfirm --onefile --windowed --icon=icon.ico --name="api" api.py

REM 打包 out.py
echo 正在打包 out.py...
cd 月度考勤
pyinstaller --noconfirm --onefile --windowed --icon=../icon.ico --name="day" out-new.py
cd ..

REM 创建发布目录
echo 正在创建发布目录...
mkdir release
copy dist\海康数据同步.exe release\
copy 月度考勤\dist\考勤计算.exe release\

echo 打包完成！文件保存在 release 文件夹中。
pause 