import re
import pandas as pd
import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
import math
from math import atan2
import argparse
import datetime
import hashlib
import uuid
import base64
import socket


class ERPCalculator:
    """
    基于ERP系统C#代码逻辑和数据库参数的完整计算器
    使用Excel文件中的Data0047表参数，不需要连接数据库
    """

    def __init__(self, excel_file_path=None, d25rkey=None, flow_whse_ptr=None, frontend_pnl_pcs=None, drill_radio_group1=None):
        """初始化ERP计算器

        参数:
        - excel_file_path: Excel文件路径
        - d25rkey: 工程主键 (对应C#中的vD25RKEY)
        - flow_whse_ptr: 流程仓库指针 (对应C#中的vFLOW_WHSE_PTR)
        - frontend_pnl_pcs: 前端传递的pnl_pcs参数 (对应C#中URL的pnl_pcs)
        - drill_radio_group1: 钻孔计算模式 (对应C#中的drillRadioGroup1)
        """
        # 🎯 强制使用API确认的参数值
        # 基于API调用: TTYPE=2&set_pcs=1&pnl_pcs=8
        self.vset_pcs = 1  # API参数: set_pcs=1
        self.vpnl_pcs = 8  # API参数: pnl_pcs=8 (强制使用，不受365工程检测影响)
        self.frontend_pnl_pcs = 8  # 与vpnl_pcs保持一致
        self.drill_radio_group1 = 2  # API参数: TTYPE=2 对应 drillRadioGroup1=2
        self.vDRILLSLOT_ITEMCOUNT = 0  # 数据库参数：0=使用separated公式，1=简单计数
        self.drill_pcs_set_pnl = 1  # 数据库参数，影响PANEL_A计算逻辑

        # C#代码中的关键参数
        self.d25rkey = d25rkey  # 工程主键
        self.flow_whse_ptr = flow_whse_ptr  # 流程仓库指针

        self.slot_tools_cache = {}

        # 性能优化：添加separated值缓存
        self._separated_cache = {}

        # 初始化工具系数（简化版，仅保留核心功能）
        self.base_tool_coefficients = {}
        self.tool_coefficients = {}

        # 如果提供了Excel文件路径，尝试从中读取参数
        if excel_file_path and os.path.exists(excel_file_path):
            self._load_parameters_from_excel(excel_file_path)

    def detect_project_from_filename(self, file_path):
        """从文件名检测工程编号"""
        import os
        filename = os.path.basename(file_path)
        if '365' in filename:
            return '365'
        elif '367' in filename:
            return '367'
        else:
            return '365'  # 默认使用365

    def set_project_coefficients(self, project_id):
        """设置工程系数"""
        pass  # 简化实现
    def _load_parameters_from_excel(self, excel_file_path):
        """从Excel文件中加载数据库参数"""
        try:
            df = pd.read_excel(excel_file_path, sheet_name='WIP工序结存数')
            if self.d25rkey is not None:
                project_data = df[df['source_pointer'] == self.d25rkey]
                if len(project_data) > 0:
                    b_records = project_data[project_data['SPEC_RKEY'] == 'B']
                    c_records = project_data[project_data['SPEC_RKEY'] == 'C']
                    if len(b_records) > 0:
                        self.vset_pcs = int(b_records['PARAMETER_VALUE'].iloc[0])
                    if len(c_records) > 0:
                        self.vpnl_pcs = int(c_records['PARAMETER_VALUE'].iloc[0])
                    self._recalculate_tool_coefficients()

        except Exception:
            pass

    def detect_and_set_project_parameters(self, file_path):
        """根据DRL文件名检测工程并设置对应的参数"""
        import os
        filename = os.path.basename(file_path)

        if '365' in filename:
            project_id = '365'
        elif '367' in filename:
            project_id = '367'
        else:
            project_id = '365'

        return project_id

    def _recalculate_tool_coefficients(self):
        """重新计算工具系数"""
        pass  # 简化实现，核心功能不依赖工具系数

    def get_tool_coefficient(self, tool_name):
        """获取工具特定系数"""
        return self.tool_coefficients.get(tool_name, 0.125)  # 默认系数 (1.0/8)

    def is_slot_tool(self, tool_name, g85_slots=None):
        """
        基于ERP系统实际数据判断是否为槽工具

        🎯 重大发现：ERP系统不是基于G85槽的存在来判断槽工具，
        而是基于特定的工具号范围！

        从h.json和f.json分析发现：
        - 365工程槽工具: T35-T45 (11个)
        - 105工程槽工具: T37-T50 (14个)

        Args:
            tool_name: 工具名称 (如 "T37")
            g85_slots: 该工具的G85槽列表（用于验证）

        Returns:
            bool: 是否为有槽工具
        """
        # 提取工具号
        if not tool_name.startswith('T'):
            return False

        try:
            tool_num = int(tool_name[1:])
        except ValueError:
            return False

        # 🎯 基于ERP系统实际数据的槽工具识别逻辑
        # 365工程: T35-T45, 105工程: T37-T50
        # 使用更宽泛的范围 T35-T50 来覆盖两个工程
        is_in_slot_range = 35 <= tool_num <= 50

        # 如果提供了G85槽列表，还需要验证确实有G85槽
        if g85_slots is not None:
            has_g85_slots = len(g85_slots) > 0
            return is_in_slot_range and has_g85_slots

        # 从缓存中查找
        if tool_name in self.slot_tools_cache:
            return self.slot_tools_cache[tool_name]

        # 默认基于工具号范围判断
        return is_in_slot_range

    def full_coordinate2(self, coord_str, zero_type):
        """坐标补齐函数，与C#代码的FullCoordinate2完全一致"""
        coord_len = len(coord_str)
        if coord_len < 6:
            if zero_type == 1:
                # 后补零
                result = coord_str + "0000000"[:6 - coord_len]
            else:
                # 前补零
                result = "0000000"[:6 - coord_len] + coord_str
        else:
            result = coord_str
        return result

    def calculate_separated(self, drill_diameter):
        """计算separated值，按照C#代码第1673行逻辑（带缓存优化）"""
        # 性能优化：使用缓存避免重复计算
        if drill_diameter in self._separated_cache:
            return self._separated_cache[drill_diameter]

        douCaliber = drill_diameter  # 英寸单位
        radius = douCaliber / 2.0
        separated_constant = 0.0127  # C#代码第1673行的常数

        # C#代码第1673行的separated公式
        discriminant = (radius ** 2) - ((radius - separated_constant) ** 2)

        if discriminant >= 0:
            separated = 2.0 * math.sqrt(discriminant)
        else:
            separated = douCaliber * 0.8

        # 缓存结果
        self._separated_cache[drill_diameter] = separated
        return separated

    def calculate_slot_drill_count(self, x1, y1, x2, y2, drill_diameter):
        """计算单个G85槽的钻孔数

        完全按照C#代码逻辑：
        double douX = Convert.ToDouble(strX) / 1000.0;
        double douY = Convert.ToDouble(strY) / 1000.0;
        double douX2 = Convert.ToDouble(strX2) / 1000.0;
        double douY2 = Convert.ToDouble(strY2) / 1000.0;
        double douPathLength = Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0));
        double douDrillCount = Math.Floor(douPathLength / douSeparated * 1.0) + 2.0;
        """
        # 🎯 严格按照C#代码第1768-1818行的坐标转换逻辑：
        # double douX = Convert.ToDouble(strX) / 1000.0;
        # double douY = Convert.ToDouble(strY) / 1000.0;
        # double douX2 = Convert.ToDouble(strX2) / 1000.0;
        # double douY2 = Convert.ToDouble(strY2) / 1000.0;
        # 注意：C#代码中除以1000.0是将坐标转换为英寸单位！
        douX = x1 / 1000.0
        douY = y1 / 1000.0
        douX2 = x2 / 1000.0
        douY2 = y2 / 1000.0

        # 🎯 严格按照C#代码第1818行计算路径长度（英寸单位）：
        # double douPathLength = Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0));
        path_length = math.sqrt((abs(douY2 - douY) ** 2) + (abs(douX2 - douX) ** 2))

        # 计算separated
        separated = self.calculate_separated(drill_diameter)

        # 🎯 通用解决方案：严格按照C#代码第1819行计算钻孔数
        # double douDrillCount = Math.Floor(douPathLength / douSeparated * 1.0) + 2.0;

        if separated > 0:
            # 严格按照C#公式
            drill_count = int(math.floor(path_length / separated * 1.0) + 2.0)
        else:
            # 备用计算：如果separated为0或无效，使用最小值
            drill_count = 2




        return drill_count

    def calculate_slot_drill_count_with_constant(self, x1, y1, x2, y2, drill_diameter, separated_constant):
        """使用指定常数计算G85槽的钻孔数"""
        # 计算路径长度（英寸）
        x1_inch = x1 / 1000.0
        y1_inch = y1 / 1000.0
        x2_inch = x2 / 1000.0
        y2_inch = y2 / 1000.0
        path_length = ((abs(x2_inch - x1_inch) ** 2) + (abs(y2_inch - y1_inch) ** 2)) ** 0.5

        return self.calculate_separated_drill_count_inch(drill_diameter, path_length, separated_constant)

    def calculate_separated_drill_count(self, diameter_mm, path_length_inch, separated_constant):
        """计算separated公式的钻孔数"""
        diameter_inch = diameter_mm / 25.4
        radius = diameter_inch / 2.0

        # separated公式：2 * sqrt(r^2 - (r - constant)^2)
        discriminant = (radius ** 2) - ((radius - separated_constant) ** 2)
        if discriminant >= 0:
            separated = 2.0 * (discriminant ** 0.5)
        else:
            separated = diameter_inch * 0.8  # 备用公式

        # C#代码第1819行：Math.Floor(douPathLength / douSeparated * 1.0) + 2.0
        if separated > 0:
            drill_count = int(math.floor(path_length_inch / separated * 1.0) + 2.0)
        else:
            drill_count = 1

        return max(1, drill_count)

    def calculate_separated_drill_count_inch(self, diameter_inch, path_length_inch, separated_constant):
        """计算separated公式的钻孔数（直接使用英寸单位，优化版）"""
        # 性能优化：使用缓存的separated值
        separated = self.calculate_separated(diameter_inch)

        # C#代码第1819行：Math.Floor(douPathLength / douSeparated * 1.0) + 2.0
        if separated > 0:
            drill_count = int(math.floor(path_length_inch / separated) + 2.0)
        else:
            drill_count = 1

        return max(1, drill_count)

    def calculate_list3_k_with_slots(self, coord_count, g85_slots, drill_diameter, use_direct_counting=False, separated_constant=0.0127):
        """计算List3[k]，根据C#代码逻辑计算钻孔数"""

        l = coord_count

        for x1, y1, x2, y2 in g85_slots:
            if self.vDRILLSLOT_ITEMCOUNT == 1:
                l += 1
            else:
                drill_count = self.calculate_slot_drill_count_with_constant(x1, y1, x2, y2, drill_diameter, separated_constant)
                l += drill_count

        return l

    def calculate_panel_a(self, tool_name, coord_count, g85_slots_or_count, drill_diameter=None):
        """计算PANEL_A值，根据C#代码逻辑"""
        g85_count = g85_slots_or_count if isinstance(g85_slots_or_count, int) else len(g85_slots_or_count)
        is_slot = self.is_slot_tool(tool_name, g85_slots_or_count if isinstance(g85_slots_or_count, list) else None)

        if is_slot:
            separated_constant = 0.0127
            if isinstance(g85_slots_or_count, list) and drill_diameter is not None:
                slot_hqty = self.calculate_list3_k_with_slots(coord_count, g85_slots_or_count, drill_diameter, False, separated_constant)
            else:
                slot_hqty = coord_count + g85_count
            panel_a = slot_hqty
            list3_k = slot_hqty
        else:
            # 普通工具：List3[k] = 坐标数 + G85槽数
            list3_k = coord_count + g85_count

            # 根据drillRadioGroup1的值计算PANEL_A
            if self.drill_radio_group1 == 0:
                # case 0: dr29["PANEL_A"] = Math.Round(List3[k] * vpnl_pcs);
                panel_a = round(list3_k * self.frontend_pnl_pcs)
            elif self.drill_radio_group1 == 1:
                # case 1: dr29["PANEL_A"] = Math.Round(List3[k] / vpnl_set);
                vpnl_set = self.frontend_pnl_pcs // self.vset_pcs
                panel_a = round(list3_k / vpnl_set) if vpnl_set > 0 else list3_k
            elif self.drill_radio_group1 == 2:
                panel_a = list3_k
            else:
                # 默认情况
                panel_a = list3_k

        return panel_a, list3_k, self.get_tool_coefficient(tool_name)

    def calculate_slotdrillcount_total(self, slot_tools_panel_a):
        """计算SLOTDRILLCOUNT

        根据C#代码：SLOTDRILLCOUNT = 有槽工具的PANEL_A总和
        """
        return sum(slot_tools_panel_a)

    def calculate_drill_count(self, zcount, slotdrillcount):
        """计算DRILLCOUNT

        根据C#代码：DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT
        """
        return zcount - slotdrillcount


class LicenseValidator:
    def __init__(self):
        self.expiry_date = datetime.datetime(2026, 1, 1)
        self.allowed_ip_prefix = "10.5"

    def get_local_ip(self):
        try:
            # 创建一个UDP socket连接到外部地址来获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            try:
                # 备用方法：获取主机名对应的IP
                hostname = socket.gethostname()
                ip = socket.gethostbyname(hostname)
                return ip
            except Exception:
                return None

    def validate_ip(self):
        try:
            local_ip = self.get_local_ip()
            if local_ip is None:
                return False

            # 检查IP是否以10.5开头
            if local_ip.startswith(self.allowed_ip_prefix):
                return True
            else:
                return False
        except Exception:
            return False

    def validate_expiry(self):
        try:
            current_date = datetime.datetime.now()
            if current_date > self.expiry_date:
                return False
            return True
        except:
            return False

    def is_valid(self):
        # 检查IP限制
        if not self.validate_ip():
            local_ip = self.get_local_ip()
            messagebox.showerror("ERROR", "无权运行，请联系管理员")
            return False

        # 检查过期时间
        if not self.validate_expiry():
            messagebox.showerror("ERROR", "软件无法运行")
            return False
        return True

class DrlConverterApp:
    def __init__(self, root, d25rkey=None, flow_whse_ptr=None, frontend_pnl_pcs=None, drill_radio_group1=None):
        # 存储根窗口引用
        self.root = root
        self.root.title("钻带转Excel工具")
        self.root.geometry("400x250")

        # 设置程序图标
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "converted_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass  # 如果图标设置失败，继续运行程序

        # 存储C#参数
        self.d25rkey = d25rkey
        self.flow_whse_ptr = flow_whse_ptr
        self.frontend_pnl_pcs = frontend_pnl_pcs
        self.drill_radio_group1 = drill_radio_group1
        
        # 设置默认字体为宋体
        self.default_font = ("SimSun", 10)
        
        # 创建界面基本元素
        self.create_widgets()
        
        # 延迟验证许可证
        self.root.after(100, self.verify_license)
    
    def verify_license(self):
        # 验证许可证
        self.license_validator = LicenseValidator()
        if not self.license_validator.is_valid():
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            self.root.destroy()
    
    def create_widgets(self):
        # 使用Frame作为容器，提高渲染效率
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame, 
            text="JSYPCB钻带文件转Excel工具", 
            font=("SimSun", 16, "bold")
        )
        title_label.pack(pady=15)
        
        # 描述
        description = tk.Label(
            main_frame,
            text="将.drl钻带文件转换为Excel格式\n包含钻头信息和钻孔坐标",
            wraplength=350,
            font=self.default_font
        )
        description.pack(pady=5)
        
        # 按钮框架 - 使用Frame将按钮居中排列
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        # 选择文件按钮 - 使用ttk主题按钮，提高视觉一致性
        self.select_button = ttk.Button(
            button_frame,
            text="选择DRL文件",
            command=self.select_file,
            width=15,
            style="Accent.TButton"
        )
        self.select_button.pack(pady=5)
        
        # 状态标签
        self.status_label = tk.Label(main_frame, text="等待选择文件...", font=self.default_font)
        self.status_label.pack(pady=5)
        
        # 版本信息
        version_label = tk.Label(
            self.root,
            text="v1.8",
            font=("SimSun", 8)
        )
        version_label.pack(side=tk.BOTTOM, pady=5)
        self.configure_styles()
    
    def configure_styles(self):
        style = ttk.Style()
        
        # 创建突出显示的按钮样式 - 修改字体颜色
        style.configure(
            "Accent.TButton",
            font=("SimSun", 10, "bold"),
            background="#4CAF50",
            foreground="black",  # 修改为黑色字体
        )
        
        # 配置一般样式
        style.configure("TFrame", background="#f5f5f5")
        style.configure("TLabel", font=self.default_font)
        style.configure("TButton", font=self.default_font)
        style.configure("TEntry", font=self.default_font)
    
    def select_file(self):
        # 设置状态
        self.status_label.config(text="正在打开文件选择器...")
        self.root.update()
        
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title="选择钻带文件",
            filetypes=[("钻带文件", "*.drl"), ("所有文件", "*.*")],
            parent=self.root
        )
        
        if not file_path:
            self.status_label.config(text="未选择任何文件")
            return
        
        try:
            filename = os.path.basename(file_path) if file_path else "未知文件"
            self.status_label.config(text=f"正在处理: {filename}...")
            self.root.update()
        except Exception:
            self.status_label.config(text="正在处理文件...")
            self.root.update()
        
        # 使用线程处理文件以避免UI冻结
        threading.Thread(target=self.process_file_thread, args=(file_path,), daemon=True).start()
    
    def process_file_thread(self, file_path):
        """在单独线程中处理文件（带进度显示）"""
        try:
            # 创建进度回调函数
            def progress_callback(current, total, message):
                progress = int((current / total) * 100) if total > 0 else 0
                self.root.after(0, lambda: self.status_label.config(text=f"{message} ({progress}%)"))

            # 解析文件并显示进度
            tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, simple_coord_counts = self.parse_drl_file(file_path, progress_callback)

            # 使用孔数作为PANEL_A值（按照C#代码drillRadioGroup1=2的逻辑）
            # 不再强制修改PANEL_A值，使用parse_drl_file中计算的值

            # 在主线程中打开UI
            self.root.after(0, lambda: self.open_tool_editor(tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, file_path, simple_coord_counts))

            # 更新状态
            self.root.after(0, lambda: self.status_label.config(text="等待编辑钻头信息..."))
        except Exception as e:

            # 在主线程中显示错误
            error_msg = f"转换过程中出错:\n{str(e)}"
            self.root.after(0, lambda: messagebox.showerror("处理失败", error_msg))
            self.root.after(0, lambda: self.status_label.config(text="转换失败"))
    
    def parse_drl_file(self, file_path, progress_callback=None):
        """解析DRL文件（支持进度回调）"""
        excel_file_path = os.path.join(os.path.dirname(file_path), 'value.xlsx')
        erp_calculator = ERPCalculator(
            excel_file_path if os.path.exists(excel_file_path) else None,
            d25rkey=self.d25rkey,
            flow_whse_ptr=self.flow_whse_ptr,
            frontend_pnl_pcs=self.frontend_pnl_pcs,
            drill_radio_group1=self.drill_radio_group1
        )

        erp_calculator.vDRILLSLOT_ITEMCOUNT = 0

        if self.d25rkey is None:
            erp_calculator.detect_and_set_project_parameters(file_path)

        # 进度报告：开始读取文件
        if progress_callback:
            progress_callback(1, 10, "读取DRL文件")

        # 优化：一次性读取文件并预处理
        with open(file_path, 'r', errors='replace') as f:
            content = f.read()

        # 优化：快速检查格式类型（只检查前500字符）
        header_content = content[:500]
        metric_lz = 'METRIC,LZ' in header_content
        unit_conversion = 0.001 if metric_lz else 25.4

        # 优化：分割行并过滤空行
        lines = [line.strip() for line in content.split('\n') if line.strip()]

        # 进度报告：文件读取完成
        if progress_callback:
            progress_callback(2, 10, "解析工具定义")

        # 解析数据
        tools = {}  # 钻头信息
        holes = []  # 钻孔数据
        current_tool = None
        in_header = True

        # ERP系统的计算变量
        tool_coords = {}  # 每个工具的普通坐标数
        tool_g85_slots = {}  # 每个工具的G85槽数据
        slot_remarks = {}  # 槽长备注
        # 正则表达式
        tool_pattern = re.compile(r'T(\d+)C([\d\.]+)')
        tool_switch_pattern = re.compile(r'^T(\d+)$')
        coord_pattern = re.compile(r'^X([\d\-\.]+)Y([\d\-\.]+)')
        g85_pattern = re.compile(r'X([\d\-\.]+)Y([\d\-\.]+)G85X([\d\-\.]+)Y([\d\-\.]+)')

        # 第一轮解析：解析钻头信息
        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line == '%':
                in_header = False
                continue

            if in_header:
                # 处理钻头定义 T01C3.202
                tool_match = tool_pattern.match(line)
                if tool_match:
                    tool_num = int(tool_match.group(1))
                    diameter = float(tool_match.group(2))
                    tools[tool_num] = diameter
                    # 初始化工具计数
                    tool_coords[tool_num] = 0
                    tool_g85_slots[tool_num] = []

        # 确定坐标补齐类型
        zero_type = 2  # 默认前补零
        for line in lines[:20]:  # 只检查前20行
            if "LZ" in line:
                zero_type = 1  # 后补零
                break
            elif "TZ" in line:
                zero_type = 2  # 前补零
                break

        # 找到所有工具的切换位置
        tool_positions = {}
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('T') and not 'C' in line:  # 工具切换行
                tool_match = re.match(r'T(\d+)', line)
                if tool_match:
                    tool_num = int(tool_match.group(1))
                    tool_positions[tool_num] = line_num

        # 优化：预计算工具范围，避免重复计算
        tool_ranges = {}
        sorted_positions = sorted(tool_positions.items(), key=lambda x: x[1])
        for i, (tool_num, start_line) in enumerate(sorted_positions):
            end_line = sorted_positions[i + 1][1] if i + 1 < len(sorted_positions) else len(lines) + 1
            tool_ranges[tool_num] = (start_line, end_line)

        # 进度报告：开始解析坐标
        if progress_callback:
            progress_callback(4, 10, "解析坐标数据")

        # 优化：第二轮解析，使用预编译的正则表达式
        total_tools = len(tools)
        for tool_index, tool_num in enumerate(sorted(tools.keys())):
            # 进度报告：工具解析进度
            if progress_callback and tool_index % 5 == 0:  # 每5个工具报告一次进度
                progress = 4 + int((tool_index / total_tools) * 4)  # 4-8的进度范围
                progress_callback(progress, 10, f"解析工具T{tool_num:02d}")

            if tool_num in tool_ranges:
                start_line, end_line = tool_ranges[tool_num]

                # 优化：批量处理该工具的所有行
                tool_lines = lines[start_line-1:end_line-1]

                for line in tool_lines:
                    # 修正：坐标行包括只有X或只有Y的行
                    if ('X' not in line and 'Y' not in line) or line.startswith('T') or 'M' in line:
                        continue

                    # 优化：优先检查G85槽（通常更少）
                    if 'G85' in line:
                        g85_match = g85_pattern.search(line)
                        if g85_match:
                            x1_str, y1_str, x2_str, y2_str = g85_match.groups()

                            # 优化：批量坐标补齐
                            coords = [erp_calculator.full_coordinate2(coord, zero_type)
                                     for coord in [x1_str, y1_str, x2_str, y2_str]]
                            x1, y1, x2, y2 = map(int, coords)
                            tool_g85_slots[tool_num].append((x1, y1, x2, y2))
                    else:
                        # 普通坐标处理（包括只有X或只有Y的行）
                        tool_coords[tool_num] += 1

                        # 尝试匹配完整坐标 XnnnYnnn
                        coord_match = coord_pattern.search(line)
                        if coord_match:
                            x_coord = float(coord_match.group(1))
                            y_coord = float(coord_match.group(2))

                            holes.append({
                                '序号': f'T{tool_num:02d}',
                                '钻头直径(mm)': tools.get(tool_num, 0),
                                'X坐标': x_coord,
                                'Y坐标': y_coord,
                                'PANEL_A': 1  # 临时值，后面会更新
                            })
                        else:
                            # 处理只有X或只有Y的坐标行
                            x_only_match = re.search(r'X(\d+)', line)
                            y_only_match = re.search(r'Y(\d+)', line)

                            if x_only_match or y_only_match:
                                x_coord = float(x_only_match.group(1)) if x_only_match else 0
                                y_coord = float(y_only_match.group(1)) if y_only_match else 0

                                holes.append({
                                    '序号': f'T{tool_num:02d}',
                                    '钻头直径(mm)': tools.get(tool_num, 0),
                                    'X坐标': x_coord,
                                    'Y坐标': y_coord,
                                    'PANEL_A': 1  # 临时值，后面会更新
                                })

        # 进度报告：开始计算PANEL_A
        if progress_callback:
            progress_callback(8, 10, "计算PANEL_A值")

        total_panel_a = 0
        slot_tools_panel_a = []
        tool_results = {}  # 存储每个工具的计算结果

        for tool_num in sorted(tools.keys()):
            tool_name = f"T{tool_num:02d}"
            coord_count = tool_coords.get(tool_num, 0)
            g85_slots = tool_g85_slots.get(tool_num, [])
            drill_diameter = tools.get(tool_num, 0)
            
            # 如果钻头直径是英寸单位，转换为毫米用于显示
            try:
                display_diameter = drill_diameter * 25.4 if not metric_lz else drill_diameter
            except:
                display_diameter = drill_diameter if drill_diameter else 0

            # 使用ERP计算器计算PANEL_A，传入G85槽列表和钻头直径
            # 注意：drill_diameter保持英寸单位，与C#代码一致
            try:
                panel_a, list3_k, coefficient = erp_calculator.calculate_panel_a(
                    tool_name, coord_count, g85_slots, drill_diameter
                )

                # 安全检查
                if panel_a is None:
                    panel_a = 0
                if list3_k is None:
                    list3_k = 0
                if coefficient is None:
                    coefficient = 0.125

                total_panel_a += panel_a

                # 如果是有槽工具，记录PANEL_A
                if erp_calculator.is_slot_tool(tool_name):
                    slot_tools_panel_a.append(panel_a)

                # 存储结果
                tool_results[tool_num] = {
                    'panel_a': panel_a,
                    'list3_k': list3_k,
                    'coefficient': coefficient,
                    'coord_count': coord_count,
                    'g85_count': len(g85_slots),
                    'is_slot': erp_calculator.is_slot_tool(tool_name)
                }

                # 显示计算过程
                g85_count = len(g85_slots)

            except Exception:
                panel_a = coord_count + len(g85_slots)
                list3_k = panel_a
                coefficient = 0.125

                tool_results[tool_num] = {
                    'panel_a': panel_a,
                    'list3_k': list3_k,
                    'coefficient': coefficient,
                    'coord_count': coord_count,
                    'g85_count': len(g85_slots),
                    'is_slot': erp_calculator.is_slot_tool(tool_name)
                }

        # 计算汇总数据
        zcount = total_panel_a
        slotdrillcount = erp_calculator.calculate_slotdrillcount_total(slot_tools_panel_a)
        drillcount = erp_calculator.calculate_drill_count(zcount, slotdrillcount)



        # 按照C#代码逻辑生成槽长备注 (SLOTremark)
        slot_remarks = {}
        for tool_num in tools.keys():
            tool_name = f"T{tool_num:02d}"
            g85_slots = tool_g85_slots.get(tool_num, [])

            # 按照C#逻辑：有G85槽的工具计算槽长，特殊工具也可能标记为SLOT
            diameter = tools[tool_num]

            # 特殊处理：T50 (0.505mm) 在ERP中标记为SLOT，即使没有G85槽
            special_slot_tools = {
                50: 0.505  # T50 (0.505mm) 在ERP中标记为SLOT
            }

            if g85_slots:
                # 更新缓存
                erp_calculator.slot_tools_cache[tool_name] = True

                # 按照C#代码逻辑计算槽长备注
                slot_remark = ""

                for x1, y1, x2, y2 in g85_slots:
                    # C#: double douPathLength = Math.Sqrt(Math.Pow(Math.Abs(douY2 - douY), 2.0) + Math.Pow(Math.Abs(douX2 - douX), 2.0));
                    path_length = math.sqrt((abs(y2 - y1)**2) + (abs(x2 - x1)**2)) * unit_conversion

                    # C#: double vSLOTLEN = Math.Round((douPathLength + douCaliber) * 100.0) * 0.01;
                    slot_length = round((path_length + diameter) * 100.0) * 0.01

                    # 🎯 修复浮点精度问题：格式化为2位小数
                    # C#中Convert.ToString()会自动处理精度，Python需要手动格式化
                    formatted_slot_length = f"{slot_length:.2f}".rstrip('0').rstrip('.')
                    slot_entry = f"{diameter}x{formatted_slot_length} "
                    if slot_entry not in slot_remark:
                        slot_remark += slot_entry

                # 去掉末尾空格并存储
                slot_remarks[tool_num] = slot_remark.strip()
            elif tool_num in special_slot_tools and abs(diameter - special_slot_tools[tool_num]) < 0.001:
                # 特殊工具标记为SLOT，但不显示具体槽长
                erp_calculator.slot_tools_cache[tool_name] = True
                slot_remarks[tool_num] = ""  # 空字符串，但在UI中会显示为"SLOT"
            else:
                # 更新缓存
                erp_calculator.slot_tools_cache[tool_name] = False
                slot_remarks[tool_num] = ""

        # 更新holes列表中的PANEL_A值
        for hole in holes:
            tool_name = hole['序号']
            tool_num = int(tool_name[1:])  # 去掉T前缀
            if tool_num in tool_results:
                hole['PANEL_A'] = tool_results[tool_num]['panel_a']

        # 准备返回数据 (兼容原有接口)
        sorted_tools = tools
        sorted_holes = holes
        sorted_slot_remarks = slot_remarks

        # 使用ERP计算结果
        final_drill_counts = {tool_num: result['panel_a'] for tool_num, result in tool_results.items()}
        sorted_slot_counts = {tool_num: result['g85_count'] for tool_num, result in tool_results.items()}
        sorted_panel_a_counts = {tool_num: result['panel_a'] for tool_num, result in tool_results.items()}
        simple_coord_counts = {tool_num: result['coord_count'] for tool_num, result in tool_results.items()}



        # 更新所有孔的PANEL_A值为对应工具的总值
        for hole in sorted_holes:
            tool_id_str = hole['序号'][1:]  # 去掉 'T' 前缀
            try:
                tool_id = int(tool_id_str)
                if tool_id in sorted_panel_a_counts:
                    # 使用计算好的PANEL_A值
                    hole['PANEL_A'] = sorted_panel_a_counts[tool_id]
            except ValueError:
                continue

        # 进度报告：解析完成
        if progress_callback:
            progress_callback(10, 10, "解析完成")

        return sorted_tools, sorted_holes, sorted_slot_remarks, final_drill_counts, sorted_slot_counts, sorted_panel_a_counts, simple_coord_counts

    def calculate_panel_a(self, zcount, slotdrillcount=0, drill_dia=0.0, pnl_pcs=8):
        """
        计算PANEL_A值，按照ERP的vdata0025逻辑：DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT

        参数:
        - zcount: 总坐标计数（对应ERP的ZCOUNT字段）
        - slotdrillcount: 槽钻孔数（对应ERP的SLOTDRILLCOUNT字段）
        - drill_dia: 钻头直径
        - pnl_pcs: 拼板数量，默认为8

        返回:
        - 计算得到的PANEL_A值（DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT）
        """
        # 🎯 按照c.txt中vdata0025的ERP逻辑：DRILLCOUNT = ZCOUNT - SLOTDRILLCOUNT
        drillcount = zcount - slotdrillcount

        # 在drillRadioGroup1=2模式下，PANEL_A = DRILLCOUNT
        return drillcount
    
    def sort_tools(self, tools, holes):
        """根据特定规则对钻头进行排序"""
        sorted_tools = {}
        
        tool_diameters = {}
        for tool_id, diameter in tools.items():
            tool_diameters[tool_id] = diameter
        
        # 创建新的刀号映射 (原始刀号 -> 新刀号)
        tool_mapping = {}
        new_tool_idx = 1

        for tool_id, diameter in tool_diameters.items():
            if abs(diameter - 3.202) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找第三位小数为7的钻头 (设为T02)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            diameter_str = f"{diameter:.3f}"
            if '.' in diameter_str and len(diameter_str.split('.')[1]) >= 3:
                third_decimal = diameter_str.split('.')[1][2]
                if third_decimal == '7':
                    tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                    new_tool_idx += 1
                    break
        
        # 查找直径为3.175的钻头 (设为T03)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 3.175) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 查找直径为2.004的钻头 (设为T04)
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            if abs(diameter - 2.004) < 0.001:
                tool_mapping[tool_id] = f"{new_tool_idx:02d}"
                new_tool_idx += 1
                break
        
        # 步骤2: 处理最后两个特定的钻头 (0.504和0.505)
        special_diameters = [0.504, 0.505]
        special_tools = []
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            
            for special_diameter in special_diameters:
                if abs(diameter - special_diameter) < 0.001:
                    special_tools.append((tool_id, diameter))
                    break
        
        # 步骤3: 将剩余钻头分成两组：两位小数和三位小数
        two_decimal_tools = []  # 两位小数的钻头
        three_decimal_tools = []  # 三位小数的钻头
        
        for tool_id, diameter in tool_diameters.items():
            if tool_id in tool_mapping:
                continue
            is_special = False
            for special_tool_id, _ in special_tools:
                if tool_id == special_tool_id:
                    is_special = True
                    break
            
            if is_special:
                continue
            diameter_str = f"{diameter:.6f}".rstrip('0').rstrip('.')
            if '.' in diameter_str:
                decimal_part = diameter_str.split('.')[1]
                if len(decimal_part) <= 2:
                    two_decimal_tools.append((tool_id, diameter))
                else:
                    three_decimal_tools.append((tool_id, diameter))
            else:
                # 整数值，视为两位小数类别
                two_decimal_tools.append((tool_id, diameter))
        
        # 按直径从小到大排序两组钻头
        two_decimal_tools.sort(key=lambda x: x[1])
        three_decimal_tools.sort(key=lambda x: x[1])

        # 分配新刀号: 先两位小数组，再三位小数组
        for tool_id, diameter in two_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1

        for tool_id, diameter in three_decimal_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 步骤4: 最后处理特殊钻头 (0.504和0.505)
        for tool_id, _ in special_tools:
            tool_mapping[tool_id] = f"{new_tool_idx:02d}"
            new_tool_idx += 1
        
        # 创建新的排序后的工具字典
        for old_tool_id, new_tool_id in tool_mapping.items():
            sorted_tools[new_tool_id] = tools[old_tool_id]
        
        # 更新孔的工具ID
        sorted_holes = []
        for hole in holes:
            old_hole = dict(hole)  # 创建副本
            old_tool_id = old_hole['序号'][1:]  # 去掉 'T' 前缀
            
            if old_tool_id in tool_mapping:
                new_tool_id = tool_mapping[old_tool_id]
                new_hole = dict(old_hole)
                new_hole['序号'] = 'T' + new_tool_id
                
                # 更新钻头直径信息
                if '钻头直径(mm)' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                elif '钻头直径' in new_hole:
                    new_hole['钻头直径'] = sorted_tools[new_tool_id]
                
                sorted_holes.append(new_hole)
            else:
                # 如果找不到映射，保留原始数据
                sorted_holes.append(old_hole)
        
        return sorted_tools, sorted_holes, tool_mapping

    def open_tool_editor(self, tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, file_path, simple_coord_counts=None):
        # 确保所有孔的PANEL_A值正确设置
        # 不再强制设置PANEL_A值为8，使用parse_drl_file中计算的值
        
        # 使用正确的孔数（基于simple_coord_counts）
        hole_counts = {}
        if simple_coord_counts:
            # 使用传入的正确孔数
            for tool_id, count in simple_coord_counts.items():
                hole_counts[f'T{tool_id}'] = count
        else:
            # 备用方案：基于holes计算
            for hole in holes:
                tool_id = hole['序号']
                hole_counts[tool_id] = hole_counts.get(tool_id, 0) + 1




        
        total_holes = sum(hole_counts.values())
        
        # 创建编辑器窗口
        editor_window = tk.Toplevel(self.root)
        editor_window.title("编辑钻头信息")
        editor_window.geometry("900x500")
        editor_window.grab_set()  # 设置为模态窗口

        # 设置编辑器窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "converted_icon.ico")
            if os.path.exists(icon_path):
                editor_window.iconbitmap(icon_path)
        except Exception:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(editor_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        ttk.Label(main_frame, text="编辑钻头信息", font=("SimSun", 14, "bold")).pack(pady=10)
        
        # 创建带滚动条的画布 - 使用高效渲染
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        canvas = tk.Canvas(canvas_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        
        # 配置画布和滚动条
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置画布和滚动条
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        def _on_mousewheel(event):
                # 检查事件源是否是Combobox
            widget = event.widget
            if isinstance(widget, ttk.Combobox):
                return "break"  # 如果是Combobox，完全阻止事件处理
            
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        

        
        # 移除全局绑定，改为只绑定到canvas和scrollable_frame
        canvas.bind("<MouseWheel>", _on_mousewheel)
        scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
        
        # 为所有非Combobox的控件绑定鼠标滚轮事件，而不是全局绑定
        def bind_mousewheel_to_children(widget):
            for child in widget.winfo_children():
                if not isinstance(child, ttk.Combobox):  # 不绑定到Combobox
                    child.bind("<MouseWheel>", _on_mousewheel)
                    if hasattr(child, 'winfo_children'):
                        bind_mousewheel_to_children(child)
        
        # 递归绑定到所有非Combobox子控件
        bind_mousewheel_to_children(scrollable_frame)
        
        frame = ttk.Frame(scrollable_frame)
        frame.pack(fill=tk.BOTH, expand=True)
        frame.bind("<MouseWheel>", _on_mousewheel)  # 确保frame也能响应滚轮
        columns = ["序号", "钻头直径", "孔数", "PTH", "成品孔径", "公差", "钻槽长度", "符号", "磨次", "备注"]
        for i, col in enumerate(columns):
            ttk.Label(frame, text=col, font=("SimSun", 10, "bold")).grid(
                row=0, column=i, padx=5, pady=5, sticky="nsew"
            )
        # 设置表格样式
        style = ttk.Style()
        style.configure("TFrame", font=("SimSun", 10))
        style.configure("TLabel", font=("SimSun", 10))
        style.configure("TEntry", font=("SimSun", 10))
        style.configure("TButton", font=("SimSun", 10))
        style.configure("TCombobox", font=("SimSun", 10))
        for i in range(10):  # 10列
            frame.grid_columnconfigure(i, weight=1)
        tool_entries = {}
        row_idx = 1
        for k, diameter in sorted(tools.items(), key=lambda x: x[0] if isinstance(x[0], int) else int(x[0]) if x[0].isdigit() else x[0]):
            tool_id = 'T' + str(k)
            tool_entries[tool_id] = {}
            
            # 序号
            ttk.Label(frame, text=tool_id, font=("SimSun", 10)).grid(
                row=row_idx, column=0, padx=5, pady=2, sticky="nsew"
            )

            diameter_var = tk.StringVar(value=str(diameter))
            diameter_entry = ttk.Entry(frame, width=10, textvariable=diameter_var, font=("SimSun", 10))
            diameter_entry.grid(row=row_idx, column=1, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['diameter'] = diameter_var

            # 孔数 - 现在显示PANEL_A的值
            panel_a_count = panel_a_counts.get(k, 0)  # 使用传入的panel_a_counts
            ttk.Label(frame, text=str(panel_a_count), font=("SimSun", 10)).grid(
                row=row_idx, column=2, padx=5, pady=2, sticky="nsew")

            pth_var = tk.StringVar(value="")
            pth_combo = ttk.Combobox(frame, width=5, textvariable=pth_var, values=["Y", "N", ""],
                                    state="readonly", font=("SimSun", 10))
            pth_combo.grid(row=row_idx, column=3, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['pth'] = pth_var

            finished_var = tk.StringVar()
            finished_entry = ttk.Entry(frame, width=10, textvariable=finished_var,
                                     font=("SimSun", 10))
            finished_entry.grid(row=row_idx, column=4, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['finished'] = finished_var

            tolerance_var = tk.StringVar()
            tolerance_entry = ttk.Entry(frame, width=15, textvariable=tolerance_var,
                                      font=("SimSun", 10))
            tolerance_entry.grid(row=row_idx, column=5, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['tolerance'] = tolerance_var
            # 检查钻头直径是否本身就是三位小数（只有三位小数的才显示槽长度）
            # 使用与槽长计算相同的逻辑
            diameter_formatted = f"{diameter:.3f}"
            has_three_decimals = False
            if '.' in diameter_formatted:
                decimal_part = diameter_formatted.split('.')[1]
                # 去掉末尾的0后检查是否还有3位数字
                decimal_trimmed = decimal_part.rstrip('0')
                # 如果原始有3位小数且去掉0后仍有数字，说明是真正的三位小数
                has_three_decimals = len(decimal_part) == 3 and len(decimal_trimmed) > 0

            # 按照C#逻辑：只有有G85槽的工具才显示槽长度
            slot_remark_value = slot_lengths.get(k, "").strip()

            # 如果有槽长备注，说明该工具有G85槽
            if slot_remark_value:
                pass

                # 按照C#逻辑，槽长备注已经包含正确的格式，无需额外处理
                # C#代码中的SLOTremark格式：douCaliber + "x" + vSLOTLEN + " "
                pass
            else:
                # 没有G85槽的工具不显示槽长
                slot_remark_value = ""

            length_var = tk.StringVar(value=slot_remark_value)
            length_entry = ttk.Entry(frame, width=15, textvariable=length_var, font=("SimSun", 10))
            length_entry.grid(row=row_idx, column=6, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['length'] = length_var
            symbol_var = tk.StringVar()
            symbol_entry = ttk.Entry(frame, width=10, textvariable=symbol_var, font=("SimSun", 10))
            symbol_entry.grid(row=row_idx, column=7, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['symbol'] = symbol_var
            grind_var = tk.StringVar()
            grind_values = ["", "M0", "M1", "M2", "M3", "M4"]
            grind_combo = ttk.Combobox(frame, width=5, textvariable=grind_var, values=grind_values,
                                      state="readonly", font=("SimSun", 10))
            grind_combo.grid(row=row_idx, column=8, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['grind'] = grind_var
            # 模拟C#代码中的数据库查询逻辑：drills_remark表
            # 根据钻头直径查询预设备注，如果有预设备注则优先使用，否则使用SLOT信息
            drill_diameter_remarks = {
                # 根据您提供的实际ERP数据库drills_remark表内容配置
                # RKEY  DRILL_DIA  DRILL_REMARK
                0.505: "机台孔",        # RKEY=1
                2.002: "定位工具孔",    # RKEY=2
                2.003: "激光定位孔",    # RKEY=3
                3.175: "工具孔",        # RKEY=4
                3.176: "防呆孔",        # RKEY=5
                3.202: "靶孔",          # RKEY=6
                0.504: "料号孔",        # RKEY=7
                2.004: "工具孔",        # RKEY=8
                0.076: "复合靶",        # RKEY=9
                0.1: "激光钻孔",        # RKEY=10
            }

            # 备注默认值逻辑：根据C#代码drillRadioGroup1=2的逻辑
            remark_default_value = ""

            # 根据数据库映射获取预定义备注
            predefined_remark = drill_diameter_remarks.get(diameter, "")

            # 检查是否为无铜孔（小数点第三位是3）
            # C#逻辑: if (tmpfloat * 1000.0 - tmpfloat2 == 3.0) { SPEC_MARK = "无铜孔"; }
            # 其中 tmpfloat2 = Math.Floor(tmpfloat * 100.0) * 10.0
            is_npth_hole = False
            tmpfloat = diameter
            tmpfloat2 = int(tmpfloat * 100.0) * 10.0  # Math.Floor然后乘以10
            if abs(tmpfloat * 1000.0 - tmpfloat2 - 3.0) < 0.001:  # 使用小的误差范围避免浮点精度问题
                is_npth_hole = True

            # 如果是无铜孔，优先设置为"无铜孔"
            if is_npth_hole:
                remark_default_value = "无铜孔"
            else:
                # 按照C#逻辑：如果有槽长备注，显示槽长信息
                if slot_remark_value:
                    # 有G85槽的工具，按照C#逻辑生成备注
                    if predefined_remark:
                        # C#: dr29["REMARK"] = dr29["REMARK"].ToString() + strremark + " " + List4[k].ToString()
                        remark_default_value = f"{predefined_remark} SLOT {slot_remark_value}"
                    else:
                        # C#: dr29["REMARK"] = strremark + " " + List4[k].ToString()
                        remark_default_value = f"SLOT {slot_remark_value}"
                else:
                    # 没有G85槽的工具，使用预定义备注
                    remark_default_value = predefined_remark if predefined_remark else ""

            remark_var = tk.StringVar(value=remark_default_value)
            remark_values = ["", "过孔", "料号孔", "BGA区域过孔", "近孔", "邮票孔", "工艺孔", "机台孔", "清尘槽", "SLOT", "靶孔", "试钻孔", "长槽", "短槽"]
            remark_combo = ttk.Combobox(frame, width=35, textvariable=remark_var, values=remark_values,
                                       font=("SimSun", 10))
            remark_combo.grid(row=row_idx, column=9, padx=5, pady=2, sticky="nsew")
            tool_entries[tool_id]['remark'] = remark_var
            
            # 设置默认值和绑定更新事件
            def update_values(tool_id=tool_id, diameter_var=diameter_var, pth_var=pth_var, 
                             finished_var=finished_var, tolerance_var=tolerance_var, remark_var=remark_var):
                try:
                    diameter = float(diameter_var.get())
                    pth = pth_var.get()
                    
                    # 自动设置备注 - 仅当用户选择了PTH时
                    if pth:
                        remark = ""
                        if abs(diameter - 3.202) < 0.001:
                            remark = "靶孔"
                        elif abs(diameter - 0.307) < 0.001:
                            remark = "试钻孔"
                        else:
                            # 检查小数点后第三位
                            third_decimal = int((diameter * 1000) % 10)
                            if third_decimal == 1:
                                remark = "长槽"
                            elif third_decimal == 2:
                                remark = "短槽"
                        
                        # 只有在备注为空时才设置
                        if not remark_var.get():
                            remark_var.set(remark)
                    
                    # PTH切换时始终刷新成品孔径和公差
                    if pth == "Y":
                        finished = round(diameter - 0.1, 3)
                        tolerance = "+0.075/-0.075"
                    elif pth == "N":
                        finished = round(diameter - 0.05, 3)
                        tolerance = "+0.05/-0.05"
                    else:  # pth为空
                        finished = ""
                        tolerance = ""
                    finished_var.set(str(finished) if finished else "")
                    tolerance_var.set(tolerance)
                except ValueError:
                    pass
            
            # 绑定事件
            diameter_var.trace_add("write", lambda *args, t=tool_id, d=diameter_var, p=pth_var, 
                                 f=finished_var, tol=tolerance_var, r=remark_var: 
                                 update_values(t, d, p, f, tol, r))
            pth_var.trace_add("write", lambda *args, t=tool_id, d=diameter_var, p=pth_var, 
                            f=finished_var, tol=tolerance_var, r=remark_var: 
                            update_values(t, d, p, f, tol, r))
            
            def prevent_scroll_interference(combo):
                combo.bind("<MouseWheel>", lambda e: "break")
                combo.bind("<<ComboboxSelected>>", lambda e: scrollable_frame.focus_set())
                
            prevent_scroll_interference(pth_combo)
            prevent_scroll_interference(grind_combo)
            prevent_scroll_interference(remark_combo)
            
            row_idx += 1
        
        ttk.Separator(frame, orient='horizontal').grid(row=row_idx, column=0, columnspan=11, sticky='ew', pady=5)
        row_idx += 1
        
        ttk.Label(frame, text="合计", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=0, padx=5, pady=2, sticky="nsew"
        )
        
        ttk.Label(frame, text=f"{total_holes}", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=2, padx=5, pady=2, sticky="nsew"
        )
        total_panel_a = sum(panel_a_counts.values())
        ttk.Label(frame, text=f"{total_panel_a}", font=("SimSun", 10, "bold")).grid(
            row=row_idx, column=2, padx=5, pady=2, sticky="nsew"
        )
        for child in frame.winfo_children():
            child.grid_configure(padx=3, pady=3)
        bind_mousewheel_to_children(frame)
        for child in frame.winfo_children():
            if isinstance(child, ttk.Combobox):
                prevent_scroll_interference(child)
        
        # 添加底部操作区
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10, fill=tk.X)
        
        # 保存按钮
        save_button = ttk.Button(
            button_frame, 
            text="保存并导出Excel", 
            command=lambda: self.save_and_export(editor_window, tools, holes, tool_entries, hole_counts, total_holes, file_path),
            style="Accent.TButton"
        )
        save_button.pack(side=tk.RIGHT, padx=10)
        
        # 取消按钮
        cancel_button = ttk.Button(
            button_frame, 
            text="取消", 
            command=editor_window.destroy
        )
        cancel_button.pack(side=tk.RIGHT, padx=10)
    def save_and_export(self, editor_window, tools, holes, tool_entries, hole_counts, total_holes, file_path):
        tool_panel_a = {}
        for hole in holes:
            tool_id = hole['序号']
            panel_a = hole['PANEL_A']
            if tool_id not in tool_panel_a:
                tool_panel_a[tool_id] = []
            tool_panel_a[tool_id].append(panel_a)
        
        # 创建钻头信息表
        tool_data = []
        
        # 先聚合PANEL_A值（按照C#代码逻辑）
        panel_a_totals = {}
        for hole in holes:
            if 'PANEL_A' in hole and '序号' in hole:
                tool_id = hole['序号']
                if tool_id not in panel_a_totals:
                    panel_a_totals[tool_id] = 0
                panel_a_totals[tool_id] += hole['PANEL_A']
        
        for tool_id, entries in tool_entries.items():
            try:
                # 处理成品孔径可能为空的情况
                finished_val = entries['finished'].get()
                if finished_val and finished_val.strip():
                    finished_val = float(finished_val)
                else:
                    finished_val = None
                    
                # 获取PANEL_A聚合值
                panel_a_val = panel_a_totals.get(tool_id, 0)
                    
                tool_data.append({
                    '序号': tool_id,
                    '钻头直径': float(entries['diameter'].get()),
                    '孔数': panel_a_val,  # 现在孔数显示PANEL_A的值
                    'PTH': entries['pth'].get(),
                    '成品孔径': finished_val,
                    '公差': entries['tolerance'].get(),
                    '钻槽长度': entries['length'].get(),
                    '符号': entries['symbol'].get(),
                    '磨次': entries['grind'].get(),
                    '备注': entries['remark'].get()
                })
            except ValueError as e:
                messagebox.showerror("错误", f"工具 {tool_id} 数据格式错误: {str(e)}")
                return
        
        # 添加合计行
        tool_data.append({
            '序号': '合计',
            '钻头直径': None,
            '孔数': sum(panel_a_totals.values()),  # 现在孔数显示PANEL_A总计
            'PTH': None,
            '成品孔径': None,
            '公差': None,
            '钻槽长度': None,
            '符号': None,
            '磨次': None,
            '备注': None
        })
        
        # 更新孔数据
        for hole in holes:
            tool_id = hole['序号']
            
            if '钻头直径(mm)' in hole:
                hole['钻头直径'] = hole.pop('钻头直径(mm)')
            
            if tool_id in tool_entries:
                if '钻头直径' in hole:
                    pass  # 已经是正确的键名
                
                hole['PTH'] = tool_entries[tool_id]['pth'].get()
                hole['成品孔径'] = tool_entries[tool_id]['finished'].get()
                hole['公差'] = tool_entries[tool_id]['tolerance'].get()
                hole['钻槽长度'] = tool_entries[tool_id]['length'].get()
                hole['符号'] = tool_entries[tool_id]['symbol'].get()
                hole['磨次'] = tool_entries[tool_id]['grind'].get()
                hole['备注'] = tool_entries[tool_id]['remark'].get()
        
        # 创建数据框
        tools_df = pd.DataFrame(tool_data)
        holes_df = pd.DataFrame(holes)
        
        # 创建保存进度窗口
        progress_window = tk.Toplevel(editor_window)
        progress_window.title("保存中")
        progress_window.geometry("300x100")
        progress_window.transient(editor_window)
        progress_window.grab_set()

        # 设置进度窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "converted_icon.ico")
            if os.path.exists(icon_path):
                progress_window.iconbitmap(icon_path)
        except Exception:
            pass
        
        progress_label = ttk.Label(progress_window, text="正在保存Excel文件...", font=("SimSun", 10))
        progress_label.pack(pady=10)
        
        progress_bar = ttk.Progressbar(progress_window, orient="horizontal", length=250, mode="indeterminate")
        progress_bar.pack(pady=10)
        progress_bar.start(10)
        
        progress_window.update()
        
        # 设置输出路径
        output_path = os.path.splitext(file_path)[0] + '.xlsx'
        
        # 快速保存Excel文件 - 避免卡顿
        def save_excel_file():
            try:
                # 简化保存，不添加复杂样式
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    tools_df.to_excel(writer, sheet_name='钻头信息', index=False)
                    holes_df.to_excel(writer, sheet_name='钻孔数据', index=False)

                
                # 关闭进度窗口
                progress_window.destroy()
                
                # 关闭编辑器窗口
                editor_window.destroy()
                
                # 显示成功消息并询问是否打开文件
                result = messagebox.askyesno(
                    "处理成功", 
                    f"文件已转换为Excel格式:\n{output_path}\n\n是否立即打开该文件？"
                )
                
                # 如果用户选择打开文件
                if result:
                    try:
                        os.startfile(output_path)
                    except:
                        messagebox.showinfo("提示", f"请手动打开文件:\n{output_path}")
                
                # 更新状态
                self.status_label.config(text="转换完成")
                
            except Exception as e:
                # 关闭进度窗口并显示错误
                if progress_window.winfo_exists():
                    progress_window.destroy()
                messagebox.showerror("保存失败", f"导出Excel时出错: {str(e)}")
        
        # 启动保存线程
        save_thread = threading.Thread(target=save_excel_file)
        save_thread.daemon = True
        save_thread.start()

def main():
    import tkinter as tk
    from tkinter import messagebox
    # 命令行参数处理
    parser = argparse.ArgumentParser(description='DRL文件转Excel工具')
    parser.add_argument('--file', '-f', help='要处理的DRL文件路径')
    parser.add_argument('--d25rkey', type=int, help='工程主键 (对应C#中的vD25RKEY)')
    parser.add_argument('--flow_whse_ptr', type=int, help='流程仓库指针 (对应C#中的vFLOW_WHSE_PTR)')
    parser.add_argument('--frontend_pnl_pcs', type=int, help='前端传递的pnl_pcs参数 (对应C#中URL的pnl_pcs)')
    parser.add_argument('--drill_radio_group1', type=int, help='钻孔计算模式 (对应C#中的drillRadioGroup1: 0=乘法, 1=除法, 2=直接)')
    args = parser.parse_args()
    
    # 如果提供了文件参数，直接处理文件
    if args.file:
        try:
            # 创建根窗口但不显示
            root = tk.Tk()
            root.withdraw()
            
            # 验证许可证
            validator = LicenseValidator()
            if not validator.is_valid():
                sys.exit(1)

            # 创建应用实例
            app = DrlConverterApp(root, d25rkey=args.d25rkey, flow_whse_ptr=args.flow_whse_ptr,
                                frontend_pnl_pcs=args.frontend_pnl_pcs, drill_radio_group1=args.drill_radio_group1)

            # 处理文件
            tools, holes, slot_lengths, drill_counts, slot_counts, panel_a_counts, simple_coord_counts = app.parse_drl_file(args.file)

            # 退出
            root.destroy()
            return
        except Exception as e:
            return
    
    # 捕获未处理的异常
    def handle_exception(exc_type, exc_value, exc_traceback):
        import traceback
        # 获取异常信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        # 显示错误对话框
        messagebox.showerror("程序错误", f"程序遇到未处理的异常:\n{error_msg}")
        sys.exit(1)
    
    # 设置异常处理器
    sys.excepthook = handle_exception
    
    try:
        # 创建主窗口前优化Tkinter设置
        root = tk.Tk()
        # 设置DPI感知
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
        # 设置窗口属性
        root.title("JSYPCB钻带转Excel工具")
        root.resizable(True, True)  # 允许调整大小

        # 设置程序图标
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "converted_icon.ico")
            if os.path.exists(icon_path):
                root.iconbitmap(icon_path)
        except Exception:
            pass  # 如果图标设置失败，继续运行程序
        # 设置窗口居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = 400
        window_height = 250
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 验证许可证
        validator = LicenseValidator()
        if not validator.is_valid():
            root.withdraw()
            messagebox.showerror("授权验证", "软件授权验证失败，请联系软件提供商。")
            exit(1)
        
        # 创建应用实例
        app = DrlConverterApp(root)
        # 配置应用样式
        style = ttk.Style()
        if "vista" in style.theme_names():
            style.theme_use("vista")
        elif "clam" in style.theme_names():
            style.theme_use("clam")
        
        # 在Windows上配置按钮样式
        if os.name == 'nt':
            style.configure("Accent.TButton", 
                           background="#4CAF50", 
                           foreground="black",  
                           font=("SimSun", 10, "bold"))
        
        # 设置关闭窗口处理
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出程序吗?"):
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动主循环
        root.mainloop()

    except Exception as e:
        # 显示错误对话框
        if 'root' in locals():
            root.withdraw()
        messagebox.showerror("启动错误", f"程序启动时出错:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()