import pandas as pd
import os
from openpyxl import load_workbook

# 获取桌面路径
desktop = os.path.join(os.path.expanduser("~"), "Desktop")
excel_path = os.path.join(desktop, "origin.xlsx")
output_path = os.path.join(desktop, "processed_origin.xlsx")

# 加载工作簿以保留所有格式
workbook = load_workbook(excel_path)

# 处理每个工作表
for sheet_name in workbook.sheetnames:
    sheet = workbook[sheet_name]
    
    # 遍历每个单元格
    for row in sheet.iter_rows():
        for cell in row:
            if cell.value and isinstance(cell.value, str):
                # 查找以"该员工于"开头的文本，并删除到换行符
                if "该员工于" in cell.value:
                    # 找到该员工于所在的位置
                    start_pos = cell.value.find("该员工于")
                    if start_pos != -1:
                        # 从该位置开始，查找下一个换行符
                        text_to_check = cell.value[start_pos:]
                        newline_pos = text_to_check.find("\n")
                        if newline_pos != -1:
                            # 删除从"该员工于"到换行符(包括换行符)的内容
                            cell.value = cell.value[:start_pos] + text_to_check[newline_pos+1:]
                        else:
                            # 如果没有换行符，则删除从"该员工于"到结尾的所有内容
                            cell.value = cell.value[:start_pos]

# 保存处理后的文件
workbook.save(output_path)
print(f"处理完成，文件已保存至: {output_path}") 