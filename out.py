import pandas as pd
from datetime import datetime, timedelta
import os
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter

def calculate_work_hours(punch_times):
    """
    计算工作时间
    """
    if len(punch_times) < 2:
        return 0
    
    total_minutes = 0
    for i in range(0, len(punch_times)-1, 2):
        start_time = punch_times[i]
        end_time = punch_times[i+1]
        if start_time and end_time:
            h1, m1, _ = map(int, start_time.split(':'))
            h2, m2, _ = map(int, end_time.split(':'))
            minutes = (h2 * 60 + m2) - (h1 * 60 + m1)
            total_minutes += minutes
    
    return round(total_minutes / 60, 1)

def calculate_time_difference(time1, time2):
    """
    计算两个时间的差值（分钟）
    """
    if not time1 or not time2:
        return 0
    
    h1, m1, _ = map(int, time1.split(':'))
    h2, m2, _ = map(int, time2.split(':'))
    
    return abs((h2 * 60 + m2) - (h1 * 60 + m1))

def get_best_punch_time_night(all_punch_times, time_range, is_earliest):
    """
    夜班的打卡时间选择
    """
    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        
        # 处理跨日的情况
        if start_hour > end_hour:  # 例如23:00-01:00
            if (hour >= start_hour) or (hour <= end_hour):
                filtered_times.append((time_part, device))
        else:  # 正常情况，如06:00-09:00
            if start_hour <= hour <= end_hour:
                filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    # 创建一个排序键函数，正确处理跨午夜的时间
    def get_sortable_time(time_str):
        hour = int(time_str[:2])
        minute = int(time_str[3:5])
        second = int(time_str[6:8]) if len(time_str) >= 8 else 0
        
        # 对于23:00-01:00这样的跨午夜范围的特殊处理
        if start_hour > end_hour:
            # 如果是寻找最早时间，我们希望23点时间排在0点前面
            if is_earliest:
                # 23点时间保持较小值
                if hour >= start_hour:
                    return hour * 3600 + minute * 60 + second
                # 0-1点时间给一个很大的值，确保它们排在后面
                elif hour <= end_hour:
                    return (24 + hour) * 3600 + minute * 60 + second
            # 如果是寻找最晚时间，我们希望23点时间排在0点后面
            else:
                if hour >= start_hour:
                    return (24 + hour) * 3600 + minute * 60 + second
                # 0-1点时间保持原值
                elif hour <= end_hour:
                    return hour * 3600 + minute * 60 + second
        
        return hour * 3600 + minute * 60 + second
    
    # 根据排序键函数排序
    if is_earliest:
        return min(filtered_times, key=lambda x: get_sortable_time(x[0]))
    else:
        return max(filtered_times, key=lambda x: get_sortable_time(x[0]))

def get_best_punch_time(all_punch_times, target_time, is_earliest):
    """
    从打卡时间列表中选择最合适的时间（月薪人员）
    """
    target_hour, target_minute = target_time
    filtered_times = []
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        current_minutes = hour * 60 + minute
        target_minutes = target_hour * 60 + target_minute

        if target_hour == 9:  # 9:00 - 取9点之前的打卡记录
            if hour <= 9:
                filtered_times.append((time_part, device))
        elif target_hour == 11:  # 11:00-13:00
            if 11 <= hour <= 13:
                filtered_times.append((time_part, device))
        elif target_hour == 12:  # 12:00-14:00
            if 12 <= hour <= 14:
                filtered_times.append((time_part, device))
        elif target_hour == 17 and target_minute == 30:  # 17:30
            if hour >= 17:
                filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    return min(filtered_times, key=lambda x: x[0]) if is_earliest else max(filtered_times, key=lambda x: x[0])

def get_best_punch_time_hourly(all_punch_times, time_range, is_earliest):
    """
    计时人员的打卡时间选择
    """
    filtered_times = []
    start_hour, start_minute, end_hour, end_minute = time_range
    start_minutes = start_hour * 60 + start_minute
    end_minutes = end_hour * 60 + end_minute
    
    for time_part, device in all_punch_times:
        hour = int(time_part[:2])
        minute = int(time_part[3:5])
        current_minutes = hour * 60 + minute

        if start_minutes <= current_minutes <= end_minutes:
            filtered_times.append((time_part, device))

    if not filtered_times:
        return None
    
    return min(filtered_times, key=lambda x: x[0]) if is_earliest else max(filtered_times, key=lambda x: x[0])

def process_night_shift_record(record, punch_times):
    """
    处理夜班打卡记录
    """
    # 获取各个时间段的打卡记录
    # 打卡时间4：23:00-01:00 最早一次打卡记录
    punch_early_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), True)
    # 打卡时间5：23:00-01:00 最晚一次打卡记录
    punch_late_night = get_best_punch_time_night(punch_times, (23, 0, 1, 0), False)
    # 早上打卡：6:00-9:00 最晚一次打卡记录
    punch_morning = get_best_punch_time_night(punch_times, (6, 0, 9, 0), False)
    # 晚上打卡：17:00-22:00 最早一次打卡记录
    punch_evening = get_best_punch_time_night(punch_times, (17, 0, 22, 0), True)
    
    # 设置打卡时间3（晚上上班）
    if punch_evening:
        record['打卡时间3'] = punch_evening[0]
        hour = int(punch_evening[0][:2])
        record['状态3'] = '迟到' if hour >= 20 else '正常'
    else:
        record['打卡时间3'] = ''
        record['状态3'] = '缺卡'
    
    # 设置打卡时间4（夜里最早打卡）- 简化逻辑
    if punch_early_night:
        record['打卡时间4'] = punch_early_night[0]
        record['状态4'] = '正常'
    else:
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'
    
    # 设置打卡时间5（夜里最晚打卡）
    if punch_late_night:
        record['打卡时间5'] = punch_late_night[0]
        record['状态5'] = '正常'
    else:
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'
    
    # 设置打卡时间1和打卡时间2为空，状态为正常
    record['打卡时间1'] = ''
    record['打卡时间2'] = ''
    record['状态1'] = '正常'
    record['状态2'] = '正常'
    
    # 从当前日期的Excel文件中获取打卡时间6
    try:
        current_date = datetime.now().strftime('%Y%m%d')
        #current_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        current_file = f'D:\\attendance\\origin\\{current_date}.xlsx'
        
        if os.path.exists(current_file):
            # 读取当前日期的打卡数据
            df_current = pd.read_excel(current_file)
            
            # 筛选出当前员工的打卡记录
            employee_records = df_current[df_current['姓名'] == record['姓名']]
            
            # 收集10点之前的打卡记录
            morning_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    if 6< hour < 10:  # 只取10点之前的打卡记录
                        morning_punches.append((time_part, row['打卡设备']))
            
            # 如果有10点之前的打卡记录，取最早的一条
            if morning_punches:
                earliest_punch = min(morning_punches, key=lambda x: x[0])
                record['打卡时间6'] = '次日'+earliest_punch[0]
                # 根据打卡时间判断状态：8点之前为早退，8点之后为正常
                hour = int(earliest_punch[0][:2])
                record['状态6'] = '早退' if hour < 8 else '正常'
            else:
                record['打卡时间6'] = ''
                record['状态6'] = '缺卡'
        else:
            record['打卡时间6'] = ''
            record['状态6'] = '缺卡'
    except Exception as e:
        print(f"读取当前日期打卡数据出错: {str(e)}")
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'
    
    # 计算工时
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        record['加班'] = '3.5'
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_monthly_record(record, punch_times):
    """
    处理月薪人员打卡记录（包含经理、中班和普通月薪逻辑）
    """
    # 中班处理逻辑
    if record['类别'] == '中班':
        # 打卡时间1：11:00-13:00 取最早，需在12:00前
        punch1 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
        if punch1:
            record['打卡时间1'] = punch1[0]
            record['状态1'] = '正常' if punch1[0] <= '12:00:00' else '迟到'
        else:
            record['打卡时间1'] = ''
            record['状态1'] = '缺卡'

        # 打卡时间2：17:00-18:00 取最早，需在17:30后
        punch2 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), True)
        
        if punch2:
            record['打卡时间2'] = punch2[0]
            record['状态2'] = '正常' if punch2[0] >= '17:00:00' else '早退'
        else:
            record['打卡时间2'] = ''
            record['状态2'] = '缺卡'

        # 打卡时间3：17:00-18:00 取最晚
        punch3 = get_best_punch_time_hourly(punch_times, (17, 0, 19, 0), False)
        
        if punch3:
            record['打卡时间3'] = punch3[0]
            if punch3[0] == record['打卡时间2']:
                record['状态3'] = '缺卡'
            else:
                record['状态3'] = '正常' if punch3[0] <= '18:00:00' else '迟到'
        else:
            record['打卡时间3'] = ''
            record['状态3'] = '缺卡'

        # 打卡时间4：23:00-01:00（跨日）取最晚
        night_punches = [(time_part, device) for time_part, device in punch_times 
                        if (time_part >= '23:00:00' or time_part <= '01:00:00')]
        punch4 = max(night_punches, key=lambda x: x[0]) if night_punches else None
        
        if punch4:
            record['打卡时间4'] = punch4[0]
            hour = int(punch4[0][:2])
            record['状态4'] = '正常' if hour == 0 or hour == 1 else '早退'
        else:
            record['打卡时间4'] = ''
            record['状态4'] = '缺卡'

        # 中班不使用的打卡时间设为空
        record['打卡时间5'] = ''
        record['打卡时间6'] = ''
        record['状态5'] = ''
        record['状态6'] = ''

        all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 5))
        if all_status_normal:
            record['正班'] = '8'
            record['加班'] = '3.5'  # 修改这里：当所有状态正常时，加班时间设为3.5
        else:
            record['正班'] = ''
            record['加班'] = ''

    # 经理处理逻辑
    elif record['类别'] == '经理' or record['类别'] == '保洁'  or record['类别'] == '帮厨':
        # 打卡时间1：全天范围取最早，需在8:00前
        punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 12, 0), True)
        if punch1:
            record['打卡时间1'] = punch1[0]
            record['状态1'] = '正常' if punch1[0] <= '08:01:00' else '迟到'
        else:
            record['打卡时间1'] = ''
            record['状态1'] = '缺卡'

        # 打卡时间4：16:00-23:59取最晚，需在17:30后
        punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 23, 59), False)
        if punch4:
            record['打卡时间4'] = punch4[0]
            record['状态4'] = '正常' if punch4[0] >= '17:30:00' else '早退'
        else:
            record['打卡时间4'] = ''
            record['状态4'] = '缺卡'

        # 经理的中午打卡不考核
        record['打卡时间2'] = ''
        record['打卡时间3'] = ''
        record['状态2'] = '正常'
        record['状态3'] = '正常'

        # 经理不使用的打卡时间
        record['打卡时间5'] = ''
        record['打卡时间6'] = ''
        record['状态5'] = '正常'
        record['状态6'] = '正常'

        # 经理工时计算
        record['正班'] = '8' if record['状态1'] == '正常' and record['状态4'] == '正常' else ''
        record['加班'] = '0'

    # 普通月薪处理逻辑
    elif record['类别'] == '月薪':
        # 定义四个关键时间点：(目标时间, 是否取最早)
        time_points = [
            ((9, 0), True),     # 打卡时间1: 9:00前最早
            ((11, 0), True),    # 打卡时间2: 11:00-13:00最早
            ((12, 0), False),   # 打卡时间3: 12:00-14:00最晚
            ((17, 30), False)   # 打卡时间4: 17:30后最晚
        ]

        all_status_normal = True
        for i, (target_time, is_earliest) in enumerate(time_points, 1):
            if i == 2:  # 打卡时间2
                best_punch = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), is_earliest)
            elif i == 3:  # 打卡时间3
                best_punch = get_best_punch_time_hourly(punch_times, (12, 0, 14, 0), is_earliest)
            else:
                best_punch = get_best_punch_time(punch_times, target_time, is_earliest)
            
            if best_punch:
                time_part = best_punch[0]
                record[f'打卡时间{i}'] = time_part
                
                if i == 1:  # 早上
                    record[f'状态{i}'] = '正常' if time_part <= '08:01:00' else '迟到'
                elif i == 2:  # 中午
                    record[f'状态{i}'] = '正常' if '11:00:00' <= time_part <= '13:00:00' else '早退'
                elif i == 3:  # 下午
                    record[f'状态{i}'] = '正常' if time_part <= '14:00:00' else '迟到'
                elif i == 4:  # 晚上
                    record[f'状态{i}'] = '正常' if time_part >= '17:30:00' else '早退'
                
                if record[f'状态{i}'] != '正常':
                    all_status_normal = False
            else:
                record[f'打卡时间{i}'] = ''
                record[f'状态{i}'] = '缺卡'
                all_status_normal = False

        # 月薪不使用的打卡时间
        record['打卡时间5'] = ''
        record['打卡时间6'] = ''
        record['状态5'] = '正常'
        record['状态6'] = '正常'

        # 月薪工时计算
        record['正班'] = '8' if all_status_normal else ''
        record['加班'] = '0'

    return record

def process_hourly_record(record, punch_times):
    """
    处理计时人员打卡记录
    """
    time_rules = [
        ((0, 0, 9, 0), True),      # 打卡时间1: 9点之前最早
        ((11, 30, 13, 30), True),  # 打卡时间2: 11:30-13:30最早
        ((11, 30, 13, 30), False), # 打卡时间3: 11:30-13:30最晚
        ((16, 0, 18, 0), True),    # 打卡时间4: 16:00-18:00最早
        ((17, 0, 18, 0), False),   # 打卡时间5: 17:00-18:00最晚
        ((20, 0, 23, 59), False)   # 打卡时间6: 20:00后最晚
    ]

    valid_times = []
    all_status_normal = True
    for i, (time_range, is_earliest) in enumerate(time_rules, 1):
        best_punch = get_best_punch_time_hourly(punch_times, time_range, is_earliest)
        if best_punch:
            time_part, device = best_punch
            record[f'打卡时间{i}'] = time_part
            valid_times.append(time_part)
            
            if i == 3:
                if record['打卡时间2'] and record['打卡时间2'] <= '12:00:00':
                    # 如果打卡时间2在12点之前，打卡时间3需在打卡时间2后30分钟内
                    time_diff = calculate_time_difference(record['打卡时间2'], time_part)
                    if time_diff > 30:
                        record['状态3'] = '迟到'
                        all_status_normal = False
                    else:
                        record['状态3'] = '正常'
                else:
                    # 如果打卡时间2在12点之后，打卡时间3需在13:30之前
                    if time_part <= '13:31:00':
                        record['状态3'] = '正常'
                    else:
                        record['状态3'] = '迟到'
                        all_status_normal = False
            elif i == 5:
                if not record['打卡时间5'] or record['打卡时间5'] == record['打卡时间4']:
                    record['打卡时间5'] = ''
                    record['打卡时间6'] = ''
                    record['状态5'] = '正常'
                    record['状态6'] = '正常'
                    break
                else:
                    time_diff = calculate_time_difference(record['打卡时间4'], time_part)
                    record['状态5'] = '迟到' if time_diff > 30 else '正常'
            else:
                record[f'状态{i}'] = '正常'
        else:
            record[f'打卡时间{i}'] = ''
            record[f'状态{i}'] = '缺卡'
            if i <= 4:
                all_status_normal = False

    if all_status_normal:
        record['正班'] = '8'
    else:
        record['正班'] = ''

    # 计算加班时间 - 更新规则
    if all_status_normal:
        overtime_hours = 0
        
        # 新规则1: 打卡时间2和打卡时间3相差30分钟记为1小时加班
        if record['打卡时间2'] and record['打卡时间3']:
            time_diff = calculate_time_difference(record['打卡时间2'], record['打卡时间3'])
            if time_diff <= 30:
                overtime_hours = 1
        
        # 新规则2: 存在打卡时间5则加班总时长为3小时
        if record['打卡时间5'] and record['打卡时间6']:
            overtime_hours = 3
            
        if overtime_hours > 0:
            record['加班'] = str(overtime_hours)
        else:
            record['加班'] = ''
    else:
        record['加班'] = ''

    # 如果打卡时间5和打卡时间6为空，则状态为正常
    if not record['打卡时间5']:
        record['状态5'] = '正常'
    if not record['打卡时间6']:
        record['状态6'] = '正常'

    return record

# 添加白十处理函数
def process_white_ten_record(record, punch_times):
    """
    处理白十打卡记录
    """
    
    # 打卡时间1：9:00-11:00 取最早，需在10:00前
    punch1 = get_best_punch_time_hourly(punch_times, (9, 0, 11, 0), True)
    
    if punch1:
        record['打卡时间1'] = punch1[0]
        record['状态1'] = '正常' if punch1[0] <= '10:01:00' else '迟到'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)

    if punch2:
        record['打卡时间2'] = punch2[0]
        record['状态2'] = '正常' if punch2[0] >= '11:30:00' else '早退'
    else:
        record['打卡时间2'] = ''
        record['状态2'] = '缺卡'

    # 打卡时间3：11:30-13:00 取最晚
    punch3 = get_best_punch_time_hourly(punch_times, (11, 30, 13, 0), False)
    
    if punch3:
        record['打卡时间3'] = punch3[0]
        if punch2:
            time_diff = calculate_time_difference(record['打卡时间2'], punch3[0])
            record['状态3'] = '正常' if time_diff <= 30 else '迟到'
        else:
            record['状态3'] = '缺卡'
    else:
        record['打卡时间3'] = ''
        record['状态3'] = '缺卡'

    # 打卡时间4：16:00-18:00 取最早，需在17:30前
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    
    if punch4:
        record['打卡时间4'] = punch4[0]
        record['状态4'] = '正常' if punch4[0] >= '17:00:00' else '早退'
    else:
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'

    # 打卡时间5：17:00-18:00 取最晚
    punch5 = get_best_punch_time_hourly(punch_times, (17, 0, 18, 0), False)
    
    if punch5:
        record['打卡时间5'] = punch5[0]
        if punch4:
            time_diff = calculate_time_difference(record['打卡时间4'], punch5[0])
            record['状态5'] = '正常' if time_diff <= 30 else '迟到'
        else:
            record['状态5'] = '缺卡'
    else:
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'

    # 打卡时间6：19:00-21:00 取最晚，需在20:00前
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 24, 0), False)
    
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常' if punch6[0] >= '19:00:00' else '早退'
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'
    # 工时计算
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        
        # 新的加班计算逻辑: 打卡时间6减去19:00再加上0.5小时
        if record['打卡时间6']:
            try:
                # 将打卡时间6和19:00转换为datetime对象
                punch_time = datetime.strptime(record['打卡时间6'], '%H:%M:%S')
                base_time = datetime.strptime('19:00:00', '%H:%M:%S')
                
                # 如果打卡时间早于19:00，则不计算加班
                if punch_time >= base_time:
                    # 计算时间差(小时)
                    time_diff = (punch_time - base_time).total_seconds() / 3600
                    # 加上0.5小时
                    overtime_hours = round(time_diff + 0.5, 1)
                    record['加班'] = str(overtime_hours)
                else:
                    record['加班'] = '0.5'  # 最低加班时间为0.5小时
            except Exception as e:
                print(f"计算白班十点加班时间出错: {e}")
                record['加班'] = '0.5'  # 出错时默认为0.5小时
        else:
            record['加班'] = '0.5'  # 如果没有打卡时间6，默认为0.5小时
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_security_record(record, punch_times):
    """
    处理保安打卡记录
    """

    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    
    if punch1:
        record['打卡时间1'] = punch1[0]
        record['状态1'] = '正常' if punch1[0] <= '07:01:00' else '迟到'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    # 打卡时间2到打卡时间5的状态均为正常
    record['打卡时间2'] = ''
    record['打卡时间3'] = ''
    record['打卡时间4'] = ''
    record['打卡时间5'] = ''
    record['状态2'] = '正常'
    record['状态3'] = '正常'
    record['状态4'] = '正常'
    record['状态5'] = '正常'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 21, 0), False)
    
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常'  if punch6[0] >= '19:00:00' else '早退'
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'

    # 工时计算
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '12'
        record['加班'] = ''
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_b_security_record(record, punch_times):
    """
    处理保安打卡记录
    """

    # 打卡时间1：9:00前最早
    punch1 = get_best_punch_time_hourly(punch_times, (0, 0, 9, 0), True)
    
    if punch1:
        record['打卡时间1'] = punch1[0]
        record['状态1'] = '正常' if punch1[0] >= '07:00:00' else '早退'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    # 打卡时间2到打卡时间5的状态均为正常
    record['打卡时间2'] = ''
    record['打卡时间3'] = ''
    record['打卡时间4'] = ''
    record['打卡时间5'] = ''
    record['状态2'] = '正常'
    record['状态3'] = '正常'
    record['状态4'] = '正常'
    record['状态5'] = '正常'

    # 打卡时间6：19:00-21:00 取最晚
    punch6 = get_best_punch_time_hourly(punch_times, (18, 0, 21, 0), True)
    
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常'  if punch6[0] <= '19:00:00' else '迟到'
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'

    # 工时计算
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '12'
        record['加班'] = ''
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

def process_night_monthly_record(record, punch_times, now_file):
    """
    处理夜班月薪打卡记录
    """
    # 打卡时间1：从当前日期的Excel文件中获取，取6点-10点最晚时间
    try:
        current_date = datetime.now().strftime('%Y%m%d')
        #now = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        current_file = f'D:\\attendance\\origin\\{current_date}.xlsx'
        
        if os.path.exists(current_file):
            df_current = pd.read_excel(current_file)
            employee_records = df_current[df_current['姓名'] == record['姓名']]
            
            morning_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    if 6 <= hour <= 10:
                        morning_punches.append((time_part, row['打卡设备']))
            
            if morning_punches:
                latest_punch = max(morning_punches, key=lambda x: x[0])
                record['打卡时间1'] = latest_punch[0]
                hour = int(latest_punch[0][:2])
                record['状态1'] = '正常' if hour >= 8 else '早退'
            else:
                record['打卡时间1'] = ''
                record['状态1'] = '缺卡'
        else:
            record['打卡时间1'] = ''
            record['状态1'] = '缺卡'
    except Exception as e:
        print(f"读取当前日期打卡数据出错: {str(e)}")
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'
    
    # 打卡时间2和打卡时间3的状态为正常
    record['打卡时间2'] = ''
    record['打卡时间3'] = ''
    record['状态2'] = '正常'
    record['状态3'] = '正常'
    
    # 打卡时间4：从now_file文件中获取，取17点-21点最早时间
    try:
        if os.path.exists(now_file):
            df_now = pd.read_excel(now_file)
            employee_records = df_now[df_now['姓名'] == record['姓名']]
            
            evening_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    if 17 <= hour <= 21:
                        evening_punches.append((time_part, row['打卡设备']))
            
            if evening_punches:
                earliest_punch = min(evening_punches, key=lambda x: x[0])
                record['打卡时间4'] = earliest_punch[0]
                hour = int(earliest_punch[0][:2])
                record['状态4'] = '迟到' if hour >= 20 else '正常'
            else:
                record['打卡时间4'] = ''
                record['状态4'] = '缺卡'
        else:
            record['打卡时间4'] = ''
            record['状态4'] = '缺卡'
    except Exception as e:
        print(f"读取now_file打卡数据出错: {str(e)}")
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'
    
    # 打卡时间5：从now_file文件中获取，取0点-1.30点最早打卡时间
    try:
        if os.path.exists(now_file):
            df_now = pd.read_excel(now_file)
            employee_records = df_now[df_now['姓名'] == record['姓名']]
            
            night_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    minute = int(time_part[3:5])
                    if (hour == 0) or (hour == 1 and minute <= 30):
                        night_punches.append((time_part, row['打卡设备']))
            
            if night_punches:
                earliest_punch = min(night_punches, key=lambda x: x[0])
                record['打卡时间5'] = earliest_punch[0]
                hour = int(earliest_punch[0][:2])
                record['状态5'] = '正常' if hour > 0 else '异常'
            else:
                record['打卡时间5'] = ''
                record['状态5'] = '缺卡'
        else:
            record['打卡时间5'] = ''
            record['状态5'] = '缺卡'
    except Exception as e:
        print(f"读取now_file打卡数据出错: {str(e)}")
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'
    
    # 打卡时间6：从now_file文件中获取，取0点-1.40点最晚打卡时间
    try:
        if os.path.exists(now_file):
            df_now = pd.read_excel(now_file)
            employee_records = df_now[df_now['姓名'] == record['姓名']]
            
            night_punches = []
            for _, row in employee_records.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    hour = int(time_part[:2])
                    minute = int(time_part[3:5])
                    if (hour == 0) or (hour == 1 and minute <= 40):
                        night_punches.append((time_part, row['打卡设备']))
            
            if night_punches:
                latest_punch = max(night_punches, key=lambda x: x[0])
                if record['打卡时间5'] == latest_punch[0]:
                    record['打卡时间6'] = ''
                    record['状态6'] = '缺卡'
                else:
                    record['打卡时间6'] = latest_punch[0]
                    hour = int(latest_punch[0][:2])
                    minute = int(latest_punch[0][3:5])
                    record['状态6'] = '正常' if (hour < 1) or (hour == 1 and minute <= 30) else '迟到'
            else:
                record['打卡时间6'] = ''
                record['状态6'] = '缺卡'
        else:
            record['打卡时间6'] = ''
            record['状态6'] = '缺卡'
    except Exception as e:
        print(f"读取now_file打卡数据出错: {str(e)}")
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'
    
    # 工时计算
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        record['加班'] = '3.5'
    else:
        record['正班'] = ''
        record['加班'] = ''

    return record

# 添加白九处理函数
def process_bai_jiu_record(record, punch_times):
    """
    处理白九打卡记录
    """

    # 打卡时间1取值为8点到10点的最早的打卡时间
    punch1 = get_best_punch_time_hourly(punch_times, (8, 0, 10, 0), True)
    if punch1:
        record['打卡时间1'] = punch1[0]
        # 状态1根据9点判断 大于九点迟到 反之正常
        record['状态1'] = '迟到' if punch1[0] > '09:00:00' else '正常'
    else:
        record['打卡时间1'] = ''
        record['状态1'] = '缺卡'

    # 打卡时间2取值为11点到13点的最早的打卡时间
    punch2 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), True)
    if punch2:
        record['打卡时间2'] = punch2[0]
        # 状态2根据12点判断 大于12点正常 反之早退
        record['状态2'] = '正常' if punch2[0] > '12:00:00' else '早退'
    else:
        record['打卡时间2'] = ''
        record['状态2'] = '缺卡'

    # 打卡时间3取值为11点到13点的最晚的打卡时间
    punch3 = get_best_punch_time_hourly(punch_times, (11, 0, 13, 0), False)
    if punch3:
        # 如果打卡时间2等于打卡时间3则 打卡时间3为空，打卡时间3的状态为缺卡
        if record['打卡时间2'] == punch3[0]:
            record['打卡时间3'] = ''
            record['状态3'] = '缺卡'
        else:
            record['打卡时间3'] = punch3[0]
            # 如果打卡时间2不等于打卡时间3 状态3根据和打卡时间2差额 在30分钟之内为正常 反之为迟到
            if record['打卡时间2']:
                time_diff = calculate_time_difference(record['打卡时间2'], punch3[0])
                record['状态3'] = '正常' if time_diff <= 30 else '迟到'
            else:
                 record['状态3'] = '缺卡' # 如果打卡时间2不存在，打卡时间3也应为缺卡
    else:
        record['打卡时间3'] = ''
        record['状态3'] = '缺卡'

    # 打卡时间4取值为16点到18点的最早的打卡时间
    punch4 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), True)
    if punch4:
        record['打卡时间4'] = punch4[0]
        # 状态4根据17.30点判断 大于正常 反之早退
        record['状态4'] = '正常' if punch4[0] > '17:30:00' else '早退'
    else:
        record['打卡时间4'] = ''
        record['状态4'] = '缺卡'

    # 打卡时间5取值为16点到18点的最晚的打卡时间
    punch5 = get_best_punch_time_hourly(punch_times, (16, 0, 18, 0), False)
    if punch5:
        # 打卡时间5若等于打卡时间4 状态为异常
        if record['打卡时间4'] == punch5[0]:
             record['打卡时间5'] = punch5[0]
             record['状态5'] = '异常'
        else:
            record['打卡时间5'] = punch5[0]
            # 状态5根据和打卡时间4差额 在30分钟之内为正常 反之为迟到
            if record['打卡时间4']:
                time_diff = calculate_time_difference(record['打卡时间4'], punch5[0])
                record['状态5'] = '正常' if time_diff <= 30 else '迟到'
            else:
                 record['状态5'] = '缺卡' # 如果打卡时间4不存在，打卡时间5也应为缺卡
    else:
        record['打卡时间5'] = ''
        record['状态5'] = '缺卡'

    # 打卡时间6 取值为19点到22点的最晚的打卡时间
    punch6 = get_best_punch_time_hourly(punch_times, (19, 0, 22, 0), False)
    if punch6:
        record['打卡时间6'] = punch6[0]
        record['状态6'] = '正常' # 状态6的初始判断，后面会根据打卡时间5和6是否存在进行调整
    else:
        record['打卡时间6'] = ''
        record['状态6'] = '缺卡'

    # 若是打卡时间5不存在 且打卡时间6不存在 则打卡时间5和打卡时间6的状态记为正常
    if not record['打卡时间5'] and not record['打卡时间6']:
        record['状态5'] = '正常'
        record['状态6'] = '正常'
        
    # 状态全为正常的话 正班为8小时
    all_status_normal = all(record[f'状态{i}'] == '正常' for i in range(1, 7))
    if all_status_normal:
        record['正班'] = '8'
        
        # 新规则1: 打卡时间2和打卡时间3相差30分钟记为1小时加班
        overtime_hours = 0
        if record['打卡时间2'] and record['打卡时间3']:
            time_diff = calculate_time_difference(record['打卡时间2'], record['打卡时间3'])
            if time_diff <= 30:
                overtime_hours = 1
        
        # 新规则2: 存在打卡时间5和6的 加班总时长为打卡时间6减去打卡时间5
        if record['打卡时间5'] and record['打卡时间6']:
            try:
                # 将时间字符串转换为datetime对象进行计算
                time5 = datetime.strptime(record['打卡时间5'], '%H:%M:%S')
                time6 = datetime.strptime(record['打卡时间6'], '%H:%M:%S')
                # 如果跨越午夜，需要调整时间
                if time6 < time5:
                    time6 += timedelta(days=1)
                
                time_diff = time6 - time5
                # 计算小时差，保留一位小数
                overtime_hours = round(time_diff.total_seconds() / 3600, 1)
            except Exception as e:
                print(f"计算加班时间出错: {e}")
        
        if overtime_hours > 0:
            record['加班'] = str(overtime_hours)
        else:
            record['加班'] = ''
    else:
        record['正班'] = ''
        record['加班'] = '' # 如果状态不全正常，加班时间为空

    return record

# 添加无需打卡组别的处理函数
def process_no_punch_record(record, punch_times):
    """
    处理无需打卡组别的记录，所有状态记为正常
    """
    for i in range(1, 7):
        record[f'打卡时间{i}'] = ''
        record[f'状态{i}'] = '正常'
    record['正班'] = '8'
    record['加班'] = '0'
    return record

def process_attendance_data(input_file):
    try:
        
        current_date = datetime.now().strftime('%Y%m%d')
        exception_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        #exception_date = (datetime.now() - timedelta(days=2)).strftime('%Y%m%d')
        output_file = f'D:\\attendance\\out\\out_{exception_date}.xlsx'
        exception_file = f'D:\\attendance\\exceptions\\exception_{exception_date}.xlsx'
        employee_file = 'D:\\attendance\\employee.xlsx'
        #now = datetime.now().strftime('%Y%m%d')
        #now=exception_date = (datetime.now() - timedelta(days=2)).strftime('%Y%m%d')
        now_file = f'D:\\attendance\\origin\\{exception_date}.xlsx'

        if not os.path.exists(employee_file):
            print(f'员工文件不存在: {employee_file}')
            return
        df_employee = pd.read_excel(employee_file)
        employee_category = dict(zip(df_employee['姓名'], df_employee['类别']))
        employee_department = dict(zip(df_employee['姓名'], df_employee['部门']))
        df = pd.read_excel(input_file)
        
        reorganized_data = []
        exception_data = []
        processed_employees = set()

        grouped = df.groupby(['姓名', '日期'])

        for (name, date), group in grouped:
            category = employee_category.get(name, '未知')
            
            processed_employees.add(name)
            department = group['部门'].iloc[0]

            record = {
                '姓名': name,
                '部门': department,
                '类别': category,
                '日期': date,
                '打卡时间1': '', '状态1': '缺卡',
                '打卡时间2': '', '状态2': '缺卡',
                '打卡时间3': '', '状态3': '缺卡',
                '打卡时间4': '', '状态4': '缺卡',
                '打卡时间5': '', '状态5': '缺卡',
                '打卡时间6': '', '状态6': '缺卡',
                '正班': '', '加班': ''
            }

            punch_times = []
            for _, row in group.iterrows():
                if isinstance(row['打卡时间'], str) and len(row['打卡时间']) >= 19:
                    time_part = row['打卡时间'][11:19]
                    punch_times.append((time_part, row['打卡设备']))
            

            if category == '夜班':
                record = process_night_shift_record(record, punch_times)
            elif category == '中班':
                record = process_monthly_record(record, punch_times)
            elif category == '白班十点': 
                record = process_white_ten_record(record, punch_times)
            elif category in ['月薪', '经理','保洁','帮厨']:
                record = process_monthly_record(record, punch_times)
            elif category == '白班':
                record = process_hourly_record(record, punch_times)
            elif category == '保安':  
                record = process_security_record(record, punch_times)
            elif category == '夜班保安':  
                record = process_b_security_record(record, punch_times)
            elif category == '夜班月薪':
                record = process_night_monthly_record(record, punch_times, now_file)
            elif category == '白班九点': # 添加白九类别处理
                record = process_bai_jiu_record(record, punch_times)
            elif category == '无需打卡': # 添加无需打卡类别处理
                record = process_no_punch_record(record, punch_times)

            reorganized_data.append(record)

            if category != '经理' and any(record[f'状态{i}'] in ['迟到', '早退', '缺卡'] for i in range(1, 7)):
                exception_data.append(record)
            elif category == '经理' and any(record[f'状态{i}'] == '缺卡' for i in [1, 4]):
                exception_data.append(record)

        for _, employee_row in df_employee.iterrows():
            name = employee_row['姓名']
            if name not in processed_employees:
                category = employee_row['类别']
                department = employee_row['部门']
                
                record = {
                    '姓名': name,
                    '部门': department,
                    '类别': category,
                    '日期': exception_date,
                    '打卡时间1': '', '状态1': '缺卡',
                    '打卡时间2': '', '状态2': '缺卡',
                    '打卡时间3': '', '状态3': '缺卡',
                    '打卡时间4': '', '状态4': '缺卡',
                    '打卡时间5': '', '状态5': '缺卡',
                    '打卡时间6': '', '状态6': '缺卡',
                    '正班': '', '加班': ''
                }
                reorganized_data.append(record)
                exception_data.append(record)
        df_reorganized = pd.DataFrame(reorganized_data)
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_reorganized.to_excel(writer, index=False)
            worksheet = writer.sheets['Sheet1']
            red_font = Font(color='FF0000')
            status_columns = [col for col in df_reorganized.columns if col.startswith('状态')]
            for row in range(2, len(df_reorganized) + 2):  # 从第2行开始（跳过标题行）
                for col in status_columns:
                    col_idx = df_reorganized.columns.get_loc(col) + 1  # 获取列号（Excel中从1开始）
                    cell = worksheet.cell(row=row, column=col_idx)
                    # 如果状态是异常的（迟到、早退、缺卡），设置红色字体
                    if cell.value in ['迟到', '早退', '缺卡']:
                        cell.font = red_font
                        
                        # 同时将对应的打卡时间也标红
                        time_col = col.replace('状态', '打卡时间')
                        time_col_idx = df_reorganized.columns.get_loc(time_col) + 1
                        time_cell = worksheet.cell(row=row, column=time_col_idx)
                        time_cell.font = red_font

        print(f"\n结果已保存至: {output_file}")

        # 保存异常记录（如果有的话）
        if exception_data:
            df_exception = pd.DataFrame(exception_data)
            with pd.ExcelWriter(exception_file, engine='openpyxl') as writer:
                df_exception.to_excel(writer, index=False)
            
                # 获取工作表
                worksheet = writer.sheets['Sheet1']
                
                # 为异常记录文件也添加红色标记
                for row in range(2, len(df_exception) + 2):
                    for col in status_columns:
                        col_idx = df_exception.columns.get_loc(col) + 1
                        cell = worksheet.cell(row=row, column=col_idx)
                        if cell.value in ['迟到', '早退', '缺卡']:
                            cell.font = red_font
                            
                            # 同时将对应的打卡时间也标红
                            time_col = col.replace('状态', '打卡时间')
                            time_col_idx = df_exception.columns.get_loc(time_col) + 1
                            time_cell = worksheet.cell(row=row, column=time_col_idx)
                            time_cell.font = red_font
            
            print(f"异常记录已保存至: {exception_file}")
            #os.remove(now_file)
            print("当前夜班文件已经删除")

    except Exception as e:
        print(f'\n处理出错: {str(e)}')
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    print("程序开始执行...")
    exception_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
    input_file = f'D:\\attendance\\origin\\{exception_date}.xlsx'
    
    if os.path.exists(input_file):
        process_attendance_data(input_file)
    else:
        print(f'输入文件不存在: {input_file}')