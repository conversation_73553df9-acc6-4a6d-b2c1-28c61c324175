#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Hikiot API登录方法
根据实际curl请求创建的登录函数
"""

import requests
import json


def get_hikiot_token(username="19136753172", password="34eNtEaiBjs/c/cDnSJccA=="):
    """
    获取Hikiot API的token
    
    Args:
        username (str): 用户名，默认为 "19136753172"
        password (str): 密码，默认为 "34eNtEaiBjs/c/cDnSJccA=="
    
    Returns:
        str: 返回token字符串，如果失败返回None
    
    Raises:
        Exception: 当请求失败时抛出异常
    """
    
    # API端点
    url = "https://api.hikiot.com/api-saas/open/v1/pwdLogin"
    
    # 请求头
    headers = {
        'Authorization': 'Basic bGluay13ZWI6bGluaw==',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.hikiot.com',
        'Connection': 'keep-alive'
    }
    
    # 请求体
    data = {
        "username": username,
        "password": password,
        "isAuto": True
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        # 检查HTTP状态码
        response.raise_for_status()
        
        # 解析JSON响应
        result = response.json()
        
        # 检查API返回码
        if result.get("code") == 0:
            # 提取token
            token = result.get("data", {}).get("token")
            if token:
                print(f"✅ 登录成功，获取到token: {token}")
                return token
            else:
                print("❌ 响应中没有找到token")
                return None
        else:
            print(f"❌ API返回错误: {result.get('msg', '未知错误')} (code: {result.get('code')})")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        raise Exception(f"登录请求失败: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        raise Exception(f"响应JSON解析失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        raise Exception(f"登录过程中发生错误: {e}")


def hikiot_login_with_details(username="19136753172", password="34eNtEaiBjs/c/cDnSJccA=="):
    """
    获取Hikiot API的完整登录信息
    
    Args:
        username (str): 用户名
        password (str): 密码
    
    Returns:
        dict: 包含token、phone、otherToken的字典，失败返回None
    """
    
    url = "https://api.hikiot.com/api-saas/open/v1/pwdLogin"
    
    headers = {
        'Authorization': 'Basic bGluay13ZWI6bGluaw==',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.hikiot.com',
        'Connection': 'keep-alive'
    }
    
    data = {
        "username": username,
        "password": password,
        "isAuto": True
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get("code") == 0:
            data_section = result.get("data", {})
            login_info = {
                "token": data_section.get("token"),
                "phone": data_section.get("phone"),
                "otherToken": data_section.get("otherToken")
            }
            print(f"✅ 登录成功")
            print(f"   Token: {login_info['token']}")
            print(f"   Phone: {login_info['phone']}")
            print(f"   Other Token: {login_info['otherToken']}")
            return login_info
        else:
            print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 登录过程中发生错误: {e}")
        return None


# 测试函数
if __name__ == "__main__":
    print("🔐 测试Hikiot API登录")
    print("=" * 50)
    
    # 方法1: 只获取token
    print("\n📱 方法1: 获取token")
    try:
        token = get_hikiot_token()
        if token:
            print(f"Token: {token}")
        else:
            print("获取token失败")
    except Exception as e:
        print(f"错误: {e}")
    
    # 方法2: 获取完整信息
    print("\n📱 方法2: 获取完整登录信息")
    login_info = hikiot_login_with_details()
    if login_info:
        print("登录信息:")
        for key, value in login_info.items():
            print(f"  {key}: {value}")
    else:
        print("获取登录信息失败")
