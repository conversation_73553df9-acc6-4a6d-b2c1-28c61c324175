#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

def compare_with_fjson():
    """对比Python实现与f.json的差异"""
    
    try:
        sys.path.append('.')
        os.environ['DISPLAY'] = ''
        
        import tkinter as tk
        from transfer import DrlConverterApp
        
        root = tk.Tk()
        root.withdraw()
        
        converter = DrlConverterApp(root)
        
        print("=== Python实现与f.json数据对比 ===")
        
        # f.json中的期望值（105工程）
        expected_105 = {
            37: 85, 38: 169, 39: 271, 40: 109, 41: 241, 42: 253, 43: 121,
            44: 49, 45: 181, 46: 121, 47: 25, 48: 289, 49: 97, 50: 1
        }
        
        print(f"测试文件: z0l04p0t105902a0.drl (105工程)")
        print(f"期望槽工具数: {len(expected_105)}")
        
        result = converter.parse_drl_file("z0l04p0t105902a0.drl")
        
        if len(result) >= 6:
            tools = result[0]
            panel_a_counts = result[5]
            
            print(f"\n详细对比结果:")
            print("工具号 | 直径(英寸) | f.json期望 | Python计算 | 差异 | 误差率 | 状态")
            print("-" * 80)
            
            perfect_matches = 0
            close_matches = 0
            total_tools = 0
            
            for tool_id in sorted(expected_105.keys()):
                if tool_id in tools:
                    total_tools += 1
                    diameter = tools[tool_id]
                    expected_val = expected_105[tool_id]
                    calculated = panel_a_counts.get(tool_id, 0)
                    diff = calculated - expected_val
                    error_rate = abs(diff) / expected_val * 100 if expected_val > 0 else 0
                    
                    if abs(diff) <= 2:
                        perfect_matches += 1
                        status = "✅ 完美匹配"
                    elif error_rate <= 10:
                        close_matches += 1
                        status = "🟡 接近匹配"
                    else:
                        status = "❌ 需要修正"
                    
                    print(f"T{tool_id:02d}    | {diameter:10.3f} | {expected_val:10} | {calculated:10} | {diff:+4} | {error_rate:5.1f}% | {status}")
            
            accuracy = perfect_matches / total_tools * 100 if total_tools > 0 else 0
            close_accuracy = (perfect_matches + close_matches) / total_tools * 100 if total_tools > 0 else 0
            
            print(f"\n统计结果:")
            print(f"  槽工具总数: {total_tools}")
            print(f"  完美匹配: {perfect_matches} ({accuracy:.1f}%)")
            print(f"  接近匹配: {close_matches}")
            print(f"  总体准确率: {close_accuracy:.1f}%")
            
            # 分析差异原因
            print(f"\n=== 差异原因分析 ===")
            
            if accuracy < 50:
                print(f"❌ 准确率过低，存在根本性问题:")
                print(f"   1. 可能我们的separated公式计算与ERP系统不一致")
                print(f"   2. 可能ERP系统不使用separated公式，而是直接统计槽钻孔数")
                print(f"   3. 可能存在其他未发现的计算逻辑")
            elif accuracy < 80:
                print(f"🟡 准确率中等，需要微调:")
                print(f"   1. separated公式的常数可能需要调整")
                print(f"   2. 钻孔数计算逻辑可能有细微差异")
            else:
                print(f"✅ 准确率良好，只需要小幅优化")
            
            # 关键洞察
            print(f"\n=== 关键洞察 ===")
            print(f"从f.json分析发现:")
            print(f"1. PANEL_A = SLOT_HQTY (完全相等)")
            print(f"2. 这意味着ERP系统可能不使用separated公式计算PANEL_A")
            print(f"3. 而是直接统计每个槽的钻孔数量")
            print(f"4. 我们的Python实现可能过度复杂化了计算过程")
            
            # 建议的修正方案
            print(f"\n=== 建议的修正方案 ===")
            
            if accuracy < 50:
                print(f"方案1: 直接统计槽钻孔数")
                print(f"  - 不使用separated公式")
                print(f"  - 直接计算每个G85槽包含的钻孔数")
                print(f"  - PANEL_A = 槽数量 × 每槽钻孔数")
                
                print(f"\n方案2: 修正separated公式参数")
                print(f"  - 调整separated常数")
                print(f"  - 优化钻孔数计算逻辑")
                print(f"  - 保持与C#代码的一致性")
            else:
                print(f"微调separated公式参数以提高准确率")
        
        else:
            print(f"❌ 105工程文件解析失败")
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    with open("fjson_comparison_result.txt", "w", encoding="utf-8") as f:
        original_stdout = sys.stdout
        sys.stdout = f
        compare_with_fjson()
        sys.stdout = original_stdout
    print("f.json对比结果已写入fjson_comparison_result.txt")
